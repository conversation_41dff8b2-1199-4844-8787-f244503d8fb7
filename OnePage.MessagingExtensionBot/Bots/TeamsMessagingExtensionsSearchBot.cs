// Copyright (c) Microsoft Corporation. All rights reserved.
// Licensed under the MIT License.

using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Reflection;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using AdaptiveCards;
using AdaptiveCards.Templating;
using Microsoft.AspNetCore.Identity;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Teams;
using Microsoft.Bot.Schema;
using Microsoft.Bot.Schema.Teams;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Identity.Core.Cache;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using OnePage.Common;
using OnePage.Models.Models;
using OnePage.Models.Models.AdminModel;
using OnePage.Models.Models.DataModel;
using OnePage.Models.Models.ENTEventModel;
using OnePage.Models.Models.PeopleModel;
using OnePage.Services;
using OnePage.Services.Interfaces;
using Org.BouncyCastle.Asn1.Tsp;

namespace Microsoft.BotBuilderSamples.Bots
{
    public class TeamsMessagingExtensionsSearchBot : TeamsActivityHandler
    {
        public readonly string _baseUrl;
        RestClient restClient;
        private readonly PeopleContext wepdb;
        private readonly AdminContext weadb;
        private readonly DataContext weddb;

        private readonly IRedisCaching _redisCaching;
        private readonly string _cards = Path.Combine(".", "Resources", "eventsCard.json");
        private readonly string _nodatacard = Path.Combine(".", "Resources", "noDataFoundCard.json");
        private readonly string _eventListcards = Path.Combine(".", "Resources", "eventsListCard.json");
        private readonly string _peoplcards = Path.Combine(".", "Resources", "peopleCard.json");
        private readonly string _companycards = Path.Combine(".", "Resources", "companyCard.json");
        public TeamsMessagingExtensionsSearchBot(IConfiguration configuration, IRedisCaching redisCaching, PeopleContext wepdb, AdminContext weadb, DataContext weddb) : base()
        {
            this._baseUrl = configuration["BaseUrl"];
            restClient = new();
            this.wepdb = wepdb;
            this.weadb = weadb;
            this.weddb = weddb;
            _redisCaching = redisCaching;
        }
        public class EventReturnModel
        {
            public Guid Id { get; set; }
            public Guid AppId { get; set; }
            public string Subject { get; set; }
            public string EventId { get; set; }
            public DateTimeOffset? Start { get; set; }
            public DateTimeOffset? End { get; set; }
            public string Desc { get; set; }
            public string Location { get; set; }
            public string URL { get; set; }
            public string Link { get; set; }
            public string Notes { get; set; }
            public string OnlineMeetingLink { get; set; }
            public List<EventAttModel> AttendeesList { get; set; } = new();
        }
        public class EventAttModel
        {
            public string Email { get; set; }
            public string Name { get; set; }
        }
        public class PeopleReturnModel
        {
            public Guid Id { get; set; }
            public Guid AppId { get; set; }
            public string EmailAddress { get; set; }
            public string DisplayName { get; set; }
            public string Comapny { get; set; }
            public string Title { get; set; }
            public bool IsAlreadyRedeemed { get; set; }
            public string InsightText { get; set; }
            public string EmailInteractionCount { get; set; }
            public string MeetingInteractionCount { get; set; }
            public string LastMeetingDate { get; set; }
            public string UpcomingMeetingDate { get; set; }

        }
        public class CompanyReturnModel
        {
            public string Id { get; set; }
            public Guid AppId { get; set; }
            public string ComapnyName { get; set; }
            public string CompanyDomain { get; set; }
            public string NoInsightsPresent { get; set; }
            public string NoInsightMessage { get; set; }
            public List<EventAttModel> PeopleList { get; set; }
            public string MeetingInteractionCount { get; set; }
            public string LastMeetingDate { get; set; }
            public string UpcomingMeetingDate { get; set; }

        }
        public class ExtensionPayload
        {
            public object extensionRequest { get; set; } = new();
            public Guid UserId { get; set; }
        }
        protected override async Task<MessagingExtensionResponse> OnTeamsMessagingExtensionQueryAsync(ITurnContext<IInvokeActivity> turnContext, MessagingExtensionQuery query, CancellationToken cancellationToken)
        {
            try
            {
                TelegramService.SendMessageToTestBot2("plugin hit!!!");
                var text = query?.Parameters?[0]?.Value as string ?? string.Empty;
                TelegramService.SendMessageToTestBot2(text);
                bool upiExists = false;
                var aadValue = turnContext.Activity.From.AadObjectId;
                TelegramService.SendMessageToTestBot2("aad value: " + aadValue);
                Guid userId = Guid.Empty;
                bool isFreeDomainUser = false;
                try
                {
                    var allItems = weddb.CopilotAadModels.Where(w => w.IsActive == true).ToList().Count;
                    TelegramService.SendMessageToTestBot2("count: " + allItems);
                    CopilotAadModel userProviderIdentifierValues = _redisCaching.AzureCheckForItem("copilot_upi_new_value_2" + aadValue, () => weddb.CopilotAadModels.Where(w => w.AadValue.Contains(aadValue) && w.IsActive == true).FirstOrDefault(), CommonData.CustomJsonSerializer.Serialize, JsonConvert.DeserializeObject<CopilotAadModel>, DateTimeOffset.Now.AddMinutes(10));
                    if (userProviderIdentifierValues != null)
                    {
                        upiExists = true;
                        userId = userProviderIdentifierValues.UserId;
                        var userEmail = await wepdb.Users.Where(w => w.Id == userId).Select(w => w.Email).FirstOrDefaultAsync();
                        var userEmailSplit = userEmail.Split("@");
                        var isEmailWhiteListed = await wepdb.EmailWhiteLists.AnyAsync(w => w.Email == userEmail);
                        var isFreeDomain = await weddb.FreeDomains.AnyAsync(w => w.DomainName.ToLower().Contains(userEmailSplit[1].ToLower()));
                        if (isFreeDomain == true && isEmailWhiteListed == false)
                        {
                            isFreeDomainUser = true;
                        }
                    }
                }
                catch (Exception ex)
                {
                    TelegramService.SendMessageToTestBot2("1st condition failed");
                    TelegramService.SendMessageToTestBot2(ex.ToString());
                    throw;
                }
                if (userId != Guid.Empty)
                    TelegramService.SendMessage($"{userId} used copilot query as {text}");
                var queryJson = JsonConvert.SerializeObject(query.Parameters.ToList());
                int wordLength = 0;
                var nameExists = query.Parameters.ToList().Any(p => p.Name == "name");
                if (nameExists)
                {
                    wordLength = (query.Parameters.ToList().Find(p => p.Name == "name")?.Value).ToString().Length;
                    //TelegramService.SendMessageToTestBot2("word length: " + wordLength);
                }
                bool companyNameExists = false;
                if (nameExists == false)
                {
                    companyNameExists = query.Parameters.ToList().Any(p => p.Name == "companyName");
                    if (companyNameExists)
                    {
                        wordLength = (query.Parameters.ToList().Find(p => p.Name == "companyName")?.Value).ToString().Length;
                    }
                }
                bool emailExists = false;
                if (nameExists == false && companyNameExists == false)
                {
                    emailExists = query.Parameters.ToList().Any(p => p.Name == "email");
                    if (emailExists)
                    {
                        wordLength = (query.Parameters.ToList().Find(p => p.Name == "email")?.Value).ToString().Length;
                    }
                }
                bool companyDomainExists = false;
                if (nameExists == false && companyNameExists == false && emailExists == false)
                {
                    companyDomainExists = query.Parameters.ToList().Any(p => p.Name == "companyDomain");
                    if (companyDomainExists)
                    {
                        wordLength = (query.Parameters.ToList().Find(p => p.Name == "companyDomain")?.Value).ToString().Length;
                    }
                }
                if (wordLength < 3 || upiExists == false || isFreeDomainUser == true)
                {
                    if (upiExists == false)
                    {
                        var previewcard = new HeroCard
                        {
                            Title = "Authentication failed. Please signin into 1Page application.",
                            Text = ""
                        };
                        var adaptiveList = FetchAdaptive();
                        List<MessagingExtensionAttachment> msgExtList = new List<MessagingExtensionAttachment>();
                        foreach (var item in adaptiveList)
                        {
                            var attachment = new MessagingExtensionAttachment
                            {
                                ContentType = "application/vnd.microsoft.card.adaptive",
                                Content = item.Content,
                                Preview = previewcard.ToAttachment()
                            };
                            msgExtList.Add(attachment);
                        }
                        return new MessagingExtensionResponse
                        {
                            ComposeExtension = new MessagingExtensionResult
                            {
                                Type = "result",
                                AttachmentLayout = "list",
                                Attachments = msgExtList
                            }
                        };
                    }
                    else if (isFreeDomainUser == true)
                    {
                        var previewcard = new HeroCard
                        {
                            Title = "You need to login with work account to use this.",
                            Text = ""
                        };
                        var adaptiveList = FetchAdaptive();
                        List<MessagingExtensionAttachment> msgExtList = new List<MessagingExtensionAttachment>();
                        foreach (var item in adaptiveList)
                        {
                            var attachment = new MessagingExtensionAttachment
                            {
                                ContentType = "application/vnd.microsoft.card.adaptive",
                                Content = item.Content,
                                Preview = previewcard.ToAttachment()
                            };
                            msgExtList.Add(attachment);
                        }
                        return new MessagingExtensionResponse
                        {
                            ComposeExtension = new MessagingExtensionResult
                            {
                                Type = "result",
                                AttachmentLayout = "list",
                                Attachments = msgExtList
                            }
                        };
                    }
                    else
                    {
                        var previewcard = new HeroCard
                        {
                            Title = "You need atleast 3 letters to see the results.",
                            Text = ""
                        };
                        var adaptiveList = FetchAdaptive();
                        List<MessagingExtensionAttachment> msgExtList = new List<MessagingExtensionAttachment>();
                        foreach (var item in adaptiveList)
                        {
                            var attachment = new MessagingExtensionAttachment
                            {
                                ContentType = "application/vnd.microsoft.card.adaptive",
                                Content = item.Content,
                                Preview = previewcard.ToAttachment()
                            };
                            msgExtList.Add(attachment);
                        }
                        return new MessagingExtensionResponse
                        {
                            ComposeExtension = new MessagingExtensionResult
                            {
                                Type = "result",
                                AttachmentLayout = "list",
                                Attachments = msgExtList
                            }
                        };
                    }
                }
                else
                {
                    bool isMatch = false;
                    ExtensionPayload payload = new ExtensionPayload();
                    payload.extensionRequest = query;
                    payload.UserId = userId;
                    var jsonPayload = JsonConvert.SerializeObject(payload);
                    TelegramService.SendMessageToTestBot2(jsonPayload);
                    try
                    {
                        if (query.CommandId == "listEvents")
                        {
                            string apiUrl = "https://teamsmessagingextension-dzcbeaftdyd2edg6.northeurope-01.azurewebsites.net/listEvents";
                            var result = await restClient.WEPostAsync<List<EventReturnModel>>(apiUrl, payload, Guid.Empty, useAuthToken: false);
                            //TelegramService.SendMessageToTestBot2("result count: " + result.Count.ToString());
                            if (result.Count > 0)
                            {
                                isMatch = true;
                                MessagingExtensionResponse response = GetEventsCard(result);
                                return response;
                            }
                        }
                        else if (query.CommandId == "listPeople")
                        {
                            //TelegramService.SendMessageToTestBot2("listpeople");
                            string apiUrl = "https://teamsmessagingextension-dzcbeaftdyd2edg6.northeurope-01.azurewebsites.net/listPeople";
                            var result = await restClient.WEPostAsync<List<PeopleReturnModel>>(apiUrl, payload, Guid.Empty, useAuthToken: false);
                            //TelegramService.SendMessageToTestBot2("result count: " + result.Count.ToString());
                            if (result.Count > 0)
                            {
                                isMatch = true;
                                MessagingExtensionResponse response = GetPeopleCard(result);
                                return response;
                            }
                            else
                            {
                                isMatch = false;
                            }
                        }
                        else if (query.CommandId == "listCompanies")
                        {
                            string apiUrl = "https://teamsmessagingextension-dzcbeaftdyd2edg6.northeurope-01.azurewebsites.net/listCompanies";
                            var result = await restClient.WEPostAsync<List<CompanyReturnModel>>(apiUrl, payload, Guid.Empty, useAuthToken: false);
                            //TelegramService.SendMessageToTestBot2("result count: " + result.Count.ToString());
                            if (result.Count > 0)
                            {
                                isMatch = true;
                                MessagingExtensionResponse response = GetCompanyCard(result);
                                var jsonPa = JsonConvert.SerializeObject(response.ComposeExtension.Text);
                                //TelegramService.SendMessageToTestBot2(jsonPa.ToString());
                                return response;
                            }
                        }
                        var previewcard = new HeroCard
                        {
                            Title = "No data found",
                            Text = ""
                        };
                        var adaptiveList = FetchAdaptive();
                        List<MessagingExtensionAttachment> msgExtList = new List<MessagingExtensionAttachment>();
                        foreach (var item in adaptiveList)
                        {
                            var attachment = new MessagingExtensionAttachment
                            {
                                ContentType = "application/vnd.microsoft.card.adaptive",
                                Content = item.Content,
                                Preview = previewcard.ToAttachment()
                            };
                            msgExtList.Add(attachment);
                        }
                        return new MessagingExtensionResponse
                        {
                            ComposeExtension = new MessagingExtensionResult
                            {
                                Type = "result",
                                AttachmentLayout = "list",
                                Attachments = msgExtList
                            }
                        };
                    }
                    catch (Exception ex)
                    {
                        TelegramService.SendMessageToTestBot2(ex.StackTrace);
                        TelegramService.SendMessageToTestBot2(ex.ToString());
                        return new MessagingExtensionResponse
                        {
                            ComposeExtension = new MessagingExtensionResult
                            {
                                Type = "result",
                                AttachmentLayout = "list",
                                Attachments = new List<MessagingExtensionAttachment> { }
                            }
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                TelegramService.SendMessageToTestBot2(ex.ToString());
                TelegramService.SendMessageToTestBot2(ex.StackTrace);
                return null;
            }
        }
        protected override Task<MessagingExtensionResponse> OnTeamsMessagingExtensionSelectItemAsync(ITurnContext<IInvokeActivity> turnContext, JObject query, CancellationToken cancellationToken)
        {
            //var text = query?..Parameters?[0]?.Value as string ?? string.Empty;
            TelegramService.SendMessageToTestBot2(query.ToString());
            // The Preview card's Tap should have a Value property assigned, this will be returned to the bot in this event. 
            var (packageId, version, description, projectUrl, iconUrl) = query.ToObject<(string, string, string, string, string)>();

            // We take every row of the results and wrap them in cards wrapped in in MessagingExtensionAttachment objects.
            // The Preview is optional, if it includes a Tap, that will trigger the OnTeamsMessagingExtensionSelectItemAsync event back on this bot.

            var card = new ThumbnailCard
            {
                Title = $"{packageId}, {version}",
                Subtitle = description,
                Buttons = new List<CardAction>
                     {
                         new CardAction { Type = ActionTypes.OpenUrl, Title = "Nuget Package", Value = $"https://www.nuget.org/packages/{packageId}" },
                         new CardAction { Type = ActionTypes.OpenUrl, Title = "Project", Value = projectUrl },
                     },
            };

            if (!string.IsNullOrEmpty(iconUrl))
            {
                card.Images = new List<CardImage>() { new CardImage(iconUrl, "Icon") };
            }

            var attachment = new MessagingExtensionAttachment
            {
                ContentType = ThumbnailCard.ContentType,
                Content = card,
            };

            return Task.FromResult(new MessagingExtensionResponse
            {
                ComposeExtension = new MessagingExtensionResult
                {
                    Type = "result",
                    AttachmentLayout = "list",
                    Attachments = new List<MessagingExtensionAttachment> { attachment }
                }
            });
        }
        protected async override Task<MessagingExtensionResponse> OnTeamsAppBasedLinkQueryAsync(ITurnContext<IInvokeActivity> turnContext, AppBasedLinkQuery query, CancellationToken cancellationToken)
        {
            //TelegramService.SendMessageToTestBot2("link method!");
            var text = query.Url;
            //TelegramService.SendMessageToTestBot2(text);
            if (query.Url.Contains("/h/e"))
            {
                var urlString = query.Url.Split("/e/");
                string apiUrl = "https://teamsmessagingextension-dzcbeaftdyd2edg6.northeurope-01.azurewebsites.net/listEventById?eventId=" + urlString[1];
                var result = await restClient.WEPostAsync<List<EventReturnModel>>(apiUrl, null, Guid.Empty, useAuthToken: false);
                //TelegramService.SendMessageToTestBot2(result.ToString());
                if (result.Count > 0)
                {
                    MessagingExtensionResponse response = GetEventsCard(result);
                    return response;
                }
            }
            else if (query.Url.Contains("/h/c"))
            {
                var urlString = query.Url.Split("/c/");
                string apiUrl = "https://teamsmessagingextension-dzcbeaftdyd2edg6.northeurope-01.azurewebsites.net/listPeopleById?contactId=" + urlString[1];
                var result = await restClient.WEPostAsync<List<PeopleReturnModel>>(apiUrl, null, Guid.Empty, useAuthToken: false);
                TelegramService.SendMessageToTestBot2(result.ToString());
                if (result.Count > 0)
                {
                    MessagingExtensionResponse response = GetPeopleCard(result);
                    return response;
                }
            }
            else
            {

            }
            AdaptiveCard adaptiveCard = new AdaptiveCard(new AdaptiveSchemaVersion(1, 3));
            adaptiveCard.Body.Add(new AdaptiveTextBlock()
            {
                Text = "Adaptive Card Test22",
                Size = AdaptiveTextSize.ExtraLarge
            });
            adaptiveCard.Body.Add(new AdaptiveImage()
            {
                Url = new Uri("https://raw.githubusercontent.com/microsoft/botframework-sdk/master/icon.png")
            });
            var attachments = new MessagingExtensionAttachment()
            {
                Content = adaptiveCard,
                ContentType = AdaptiveCard.ContentType
            };
            return await Task.FromResult(new MessagingExtensionResponse
            {
                ComposeExtension = new MessagingExtensionResult
                {
                    AttachmentLayout = "list",
                    Type = "result",
                    Attachments = new List<MessagingExtensionAttachment>
                     {
                         new MessagingExtensionAttachment
                         {
                              Content = adaptiveCard,
                              ContentType = AdaptiveCard.ContentType,
                              Preview = attachments,

                         },
                     },
                },
            });
        }

        //public MessagingExtensionResponse GetAdaptiveCard()
        //{
        //    var paths = new[] { ".", "Resources", "RestaurantCard.json" };
        //    string filepath = Path.Combine(paths);
        //    var previewcard = new ThumbnailCard
        //    {
        //        Title = "Adaptive Card",
        //        Text = "Please select to get Adaptive card"
        //    };
        //    var adaptiveList = FetchAdaptive();
        //    var attachment = new MessagingExtensionAttachment
        //    {
        //        ContentType = "application/vnd.microsoft.card.adaptive",
        //        Content = adaptiveList.Content,
        //        Preview = previewcard.ToAttachment()
        //    };

        //    return new MessagingExtensionResponse
        //    {
        //        ComposeExtension = new MessagingExtensionResult
        //        {
        //            Type = "result",
        //            AttachmentLayout = "list",
        //            Attachments = new List<MessagingExtensionAttachment> { attachment }
        //        }
        //    };
        //}
        public MessagingExtensionResponse GetEventsCard(List<EventReturnModel> eventList)
        {
            var paths = new[] { ".", "Resources", "eventsCard.json" };
            string filepath = Path.Combine(paths);
            var adaptiveList = FetchAdaptive(eventList.ToList());
            List<MessagingExtensionAttachment> msgExtList = new List<MessagingExtensionAttachment>();
            int index = 0;
            foreach (var item in adaptiveList)
            {
                var previewcard = new HeroCard
                {
                    Title = eventList[index].Subject,
                    Text = "Please select to see the people card"
                };
                var attachment = new MessagingExtensionAttachment
                {
                    ContentType = "application/vnd.microsoft.card.adaptive",
                    Content = item.Content,
                    Preview = previewcard.ToAttachment()
                };
                msgExtList.Add(attachment);
                index++;
            }

            return new MessagingExtensionResponse
            {
                ComposeExtension = new MessagingExtensionResult
                {
                    Type = "result",
                    AttachmentLayout = "list",
                    Attachments = msgExtList
                }
            };
        }
        public MessagingExtensionResponse GetPeopleCard(List<PeopleReturnModel> peopleList)
        {
            var paths = new[] { ".", "Resources", "peopleCard.json" };
            string filepath = Path.Combine(paths);

            var adaptiveList = FetchAdaptive(peopleModelList: peopleList.Where(w => !string.IsNullOrEmpty(w.EmailAddress)).ToList());
            List<MessagingExtensionAttachment> msgExtList = new List<MessagingExtensionAttachment>();
            int index = 0;
            foreach (var item in adaptiveList)
            {
                var previewcard = new HeroCard
                {
                    Title = peopleList[index].DisplayName,
                    Text = "Please select to see the people card"
                };
                var attachment = new MessagingExtensionAttachment
                {
                    ContentType = "application/vnd.microsoft.card.adaptive",
                    Content = item.Content,
                    Preview = previewcard.ToAttachment()
                };
                msgExtList.Add(attachment);
                index++;
            }
            return new MessagingExtensionResponse
            {
                ComposeExtension = new MessagingExtensionResult
                {
                    Type = "result",
                    AttachmentLayout = "list",
                    Attachments = msgExtList
                }
            };
        }
        public MessagingExtensionResponse GetCompanyCard(List<CompanyReturnModel> companyList)
        {
            var paths = new[] { ".", "Resources", "companyCard.json" };
            string filepath = Path.Combine(paths);

            //TelegramService.SendMessageToTestBot2("GetCompanyCard");
            var adaptiveList = FetchAdaptive(companyModelList: companyList);
            List<MessagingExtensionAttachment> msgExtensionList = new List<MessagingExtensionAttachment>();
            int index = 0;
            foreach (var item in adaptiveList)
            {
                var previewcard = new HeroCard
                {
                    Title = companyList[index].ComapnyName,
                    Text = "Please select to see the company card"
                };
                var attachment = new MessagingExtensionAttachment
                {
                    ContentType = "application/vnd.microsoft.card.adaptive",
                    Content = item.Content,
                    Preview = previewcard.ToAttachment()
                };
                msgExtensionList.Add(attachment);
                index++;
            }
            return new MessagingExtensionResponse
            {
                ComposeExtension = new MessagingExtensionResult
                {
                    Type = "result",
                    AttachmentLayout = "list",
                    Attachments = msgExtensionList
                }
            };
        }
        public MessagingExtensionResponse GetConnectorCard()
        {
            var path = new[] { ".", "Resources", "connectorCard.json" };
            var filepath = Path.Combine(path);
            var previewcard = new ThumbnailCard
            {
                Title = "O365 Connector Card",
                Text = "Please select to get Connector card"
            };

            var connector = FetchConnector(filepath);
            var attachment = new MessagingExtensionAttachment
            {
                ContentType = O365ConnectorCard.ContentType,
                Content = connector.Content,
                Preview = previewcard.ToAttachment()
            };

            return new MessagingExtensionResponse
            {
                ComposeExtension = new MessagingExtensionResult
                {
                    Type = "result",
                    AttachmentLayout = "list",
                    Attachments = new List<MessagingExtensionAttachment> { attachment }
                }
            };
        }
        string TruncateString(string input, int maxLength)
        {
            if (input.Length <= maxLength)
            {
                return input;
            }
            else
            {
                return input.Substring(0, maxLength) + "...";
            }
        }
        public List<Attachment> FetchAdaptive(List<EventReturnModel> eventModelList = null, List<PeopleReturnModel> peopleModelList = null, List<CompanyReturnModel> companyModelList = null)
        {
            List<Attachment> attachmentList = new List<Attachment>();
            if (eventModelList != null)
            {
                foreach (var eventModel in eventModelList)
                {
                    string cardJson = "";
                    string appUrl = "https://teams.get1page.com/";

                    if (eventModel.AppId == CommonData.OPV2AppId)
                    {
                        appUrl = "https://web.get1page.com/";
                    }


                    var adaptiveCardJson = System.IO.File.ReadAllText(_cards);
                    AdaptiveCardTemplate template = new AdaptiveCardTemplate(adaptiveCardJson);
                    string tabUrlString = "https://teams.microsoft.com/l/entity/2b876f4d-2e6b-4ee7-9b09-8893808c1380/1Page" + "?webUrl=" + appUrl + "h/e" + eventModel.Id + "&label=contact" + "&context=" + "%7B%22subEntityId%22%3A%20%22" + eventModel.Id + "%22%7D";
                    string url = "https://teams.microsoft.com/l/entity/2b876f4d-2e6b-4ee7-9b09-8893808c1380/1Page?webUrl=";
                    string encodedurl1 = HttpUtility.UrlEncode(appUrl);
                    string orginialUrl = "{{\"subEntityId\": \"/h/e/{0}\"}}";
                    string formattedUrl = string.Format(orginialUrl, eventModel.Id);
                    string encodedUrl = HttpUtility.UrlEncode(formattedUrl);
                    var cardObj = new
                    {
                        subject = eventModel.Subject,
                        time = ((DateTimeOffset)(eventModel.Start)).ToString("t") + " - " + ((DateTimeOffset)(eventModel.End)).ToString("t"),
                        description = TruncateString(eventModel.Desc, 50),
                        tabUrl = url + encodedurl1 + "&context=" + encodedUrl
                    };
                    //TelegramService.SendMessageToTestBot2(tabUrlString);
                    cardJson = template.Expand(cardObj);
                    var adaptiveCardAttachment = new Attachment
                    {
                        ContentType = "application/vnd.microsoft.card.adaptive",
                        Content = JsonConvert.DeserializeObject(cardJson)
                    };
                    attachmentList.Add(adaptiveCardAttachment);
                }
            }
            else if (peopleModelList != null)
            {
                foreach (var peopleModel in peopleModelList)
                {
                    string cardJson = "";
                    string appUrl = "https://teams.get1page.com/";

                    if (peopleModel.AppId == CommonData.OPV2AppId)
                    {
                        appUrl = "https://web.get1page.com/";
                    }
                    var adaptiveCardJson = System.IO.File.ReadAllText(_peoplcards);
                    AdaptiveCardTemplate template = new AdaptiveCardTemplate(adaptiveCardJson);
                    var emailsplit = peopleModel.EmailAddress.Split('|');
                    string upcomingMeetingText = "";
                    string lastMeetingText = "";
                    string meetingInterationText = "";
                    if (peopleModel.MeetingInteractionCount == "0" || string.IsNullOrEmpty(peopleModel.MeetingInteractionCount))
                        meetingInterationText = "you did not interact with " + peopleModel.DisplayName;
                    else
                        meetingInterationText = "You had " + peopleModel.MeetingInteractionCount + " meetings with " + peopleModel.DisplayName;
                    if (!string.IsNullOrEmpty(peopleModel.LastMeetingDate))
                        lastMeetingText = "Your last meeting was on " + peopleModel.LastMeetingDate;
                    else
                        lastMeetingText = "You do not have any past meetings";
                    if (!string.IsNullOrEmpty(peopleModel.UpcomingMeetingDate))
                        upcomingMeetingText = "The upcoming meeting is on " + peopleModel.UpcomingMeetingDate;
                    else
                        upcomingMeetingText = "There are no scheduled meetings";

                    string url = "https://teams.microsoft.com/l/entity/2b876f4d-2e6b-4ee7-9b09-8893808c1380/1Page?webUrl=";
                    string encodedurl1 = HttpUtility.UrlEncode(appUrl);
                    string orginialUrl = "{{\"subEntityId\": \"/h/c/{0}\"}}";
                    string formattedUrl = string.Format(orginialUrl, peopleModel.Id);
                    string encodedUrl = HttpUtility.UrlEncode(formattedUrl);
                    var cardObj = new
                    {
                        Name = peopleModel.DisplayName,
                        Email = emailsplit[0],
                        InsightText = peopleModel.InsightText,
                        MeetingInteractions = meetingInterationText,
                        LastMeetingDate = lastMeetingText,
                        UpcomingMeetingDate = upcomingMeetingText,
                        tabUrl = url + encodedurl1 + "&context=" + encodedUrl
                    };
                    //TelegramService.SendMessageToTestBot2(encodedUrl);
                    cardJson = template.Expand(cardObj);
                    var adaptiveCardAttachment = new Attachment
                    {
                        ContentType = "application/vnd.microsoft.card.adaptive",
                        Content = JsonConvert.DeserializeObject(cardJson)
                    };
                    attachmentList.Add(adaptiveCardAttachment);
                }
            }
            else if (companyModelList != null)
            {
                try
                {
                    foreach (var companyModel in companyModelList)
                    {
                        string cardJson = "";
                        string appUrl = "https://teams.get1page.com/";

                        if (companyModel.AppId == CommonData.OPV2AppId)
                        {
                            appUrl = "https://web.get1page.com/";
                        }
                        //TelegramService.SendMessageToTestBot2("companyModel");
                        var adaptiveCardJson = System.IO.File.ReadAllText(_companycards);
                        AdaptiveCardTemplate template = new AdaptiveCardTemplate(adaptiveCardJson);
                        string upcomingMeetingText = "";
                        string lastMeetingText = "";
                        string meetingInterationText = "";
                        string peopleInteractionText = "";
                        if (companyModel.MeetingInteractionCount == "0" || string.IsNullOrEmpty(companyModel.MeetingInteractionCount))
                            meetingInterationText = "you haven't interacted with anyone in " + companyModel.ComapnyName + " yet.";
                        else
                        {
                            int interactionCount = int.Parse(companyModel.MeetingInteractionCount);
                            if (interactionCount == 1)
                                meetingInterationText = "You've had " + companyModel.MeetingInteractionCount + " meeting with " + companyModel.ComapnyName;
                            else
                                meetingInterationText = "You've had " + companyModel.MeetingInteractionCount + " meetings with " + companyModel.ComapnyName;
                        }
                        if (!string.IsNullOrEmpty(companyModel.LastMeetingDate))
                            lastMeetingText = "Your last meeting was on " + companyModel.LastMeetingDate;
                        //else
                        //    lastMeetingText = "You do not have any past meetings";

                        if (!string.IsNullOrEmpty(companyModel.UpcomingMeetingDate))
                            upcomingMeetingText = "The upcoming meeting is on " + companyModel.UpcomingMeetingDate;
                        //else
                        //    upcomingMeetingText = "There are no scheduled meetings";
                        if (string.IsNullOrEmpty(companyModel.LastMeetingDate) && string.IsNullOrEmpty(companyModel.UpcomingMeetingDate))
                            lastMeetingText = "You don't have any scheduled meetings with anyone from " + companyModel.ComapnyName + ".";

                        if (companyModel.PeopleList.Count > 0)
                        {
                            if (companyModel.PeopleList.Count == 1)
                                peopleInteractionText = "You've interacted with " + companyModel.PeopleList.Count + " person in " + companyModel.ComapnyName + ".";
                            else
                                peopleInteractionText = "You've interacted with " + companyModel.PeopleList.Count + " people in " + companyModel.ComapnyName + ".";
                        }
                        string url = "https://teams.microsoft.com/l/entity/2b876f4d-2e6b-4ee7-9b09-8893808c1380/1Page?webUrl=";
                        string encodedurl1 = HttpUtility.UrlEncode(appUrl);
                        string orginialUrl = "{{\"subEntityId\": \"/h/company/{0}\"}}";
                        string formattedUrl = string.Format(orginialUrl, companyModel.ComapnyName);
                        string encodedUrl = HttpUtility.UrlEncode(formattedUrl);
                        var cardObj = new
                        {
                            Name = companyModel.ComapnyName,
                            Domain = "Domain: " + companyModel.CompanyDomain,
                            PeopleInteraction = peopleInteractionText,
                            MeetingInteractions = meetingInterationText,
                            LastMeetingDate = lastMeetingText,
                            UpcomingMeetingDate = upcomingMeetingText,
                            tabUrl = url + encodedurl1 + "&context=" + encodedUrl
                        };
                        cardJson = template.Expand(cardObj);
                        //TelegramService.SendMessageToTestBot2(cardJson);
                        var adaptiveCardAttachment = new Attachment
                        {
                            ContentType = "application/vnd.microsoft.card.adaptive",
                            Content = JsonConvert.DeserializeObject(cardJson)
                        };
                        attachmentList.Add(adaptiveCardAttachment);
                    }
                }
                catch (Exception ex)
                {
                    TelegramService.SendMessageToTestBot2(ex.ToString());
                }
                //  cardJson = cardJson.Replace("${dataItems}", JsonConvert.SerializeObject(companyModel.PeopleList));
            }
            else
            {
                string cardJson = "";
                var adaptiveCardJson = System.IO.File.ReadAllText(_nodatacard);
                AdaptiveCardTemplate template = new AdaptiveCardTemplate(adaptiveCardJson);
                var cardObj = new
                {
                    noDataText = "No Data Found!"
                };
                cardJson = template.Expand(cardObj);
                var adaptiveCardAttachment = new Attachment
                {
                    ContentType = "application/vnd.microsoft.card.adaptive",
                    Content = JsonConvert.DeserializeObject(cardJson)
                };
                attachmentList.Add(adaptiveCardAttachment);
            }
            return attachmentList;
        }
        public Attachment FetchAdaptive2(string filepath, List<EventReturnModel> eventModelList = null, List<PeopleReturnModel> peopleModelList = null)
        {
            try
            {
                string cardJson = "";
                var expandedCards = new List<AdaptiveCard>();
                if (eventModelList != null)
                {
                    var adaptiveCardJson = System.IO.File.ReadAllText(_eventListcards);
                    AdaptiveCardTemplate template = new AdaptiveCardTemplate(adaptiveCardJson);
                    var facts = new List<Fact>();
                    //foreach (var eventItem in eventModelList)
                    //{
                    //var fact = new Fact()
                    //{
                    //    Key = eventItem.Subject,
                    //    Value = $"Description: {eventItem.Desc}\nTime: {((DateTimeOffset)(eventItem.Start)).ToString("t") + " - " + ((DateTimeOffset)(eventItem.End)).ToString("t")}"
                    //};
                    // facts.Add(fact);
                    var data = new
                    {
                        facts = eventModelList.Select(eventItem => new
                        {
                            title = eventItem.Subject,
                            value = $"Description: {eventItem.Desc}\nTime: {((DateTimeOffset)(eventItem.Start)).ToString("t") + " - " + ((DateTimeOffset)(eventItem.End)).ToString("t")}"
                        })
                    };
                    cardJson = template.Expand(data);
                    //var data = new
                    //{
                    //    subject = eventItem.Subject,
                    //    description = eventItem.Desc,
                    //    time = ((DateTimeOffset)(eventItem.Start)).ToString("t") + " - " + ((DateTimeOffset)(eventItem.End)).ToString("t"),
                    //    //tabUrl = "https://teams.microsoft.com/l/entity/2b876f4d-2e6b-4ee7-9b09-8893808c1380/1Page" + "?webUrl=" + "https://web.get1page.com/h/"
                    //    //peopleList = eventItem.AttendeesList.Select(attendee => new
                    //    //{
                    //    //    Name = attendee.Name,
                    //    //    Email = attendee.Email
                    //    //})
                    //};
                    //    var card = template.Expand(data);
                    //    cardJson = string.Concat(cardJson, " ", card);
                    //}
                }
                else if (peopleModelList != null)
                {
                    var adaptiveCardJson = System.IO.File.ReadAllText(_peoplcards);
                    AdaptiveCardTemplate template = new AdaptiveCardTemplate(adaptiveCardJson);
                    foreach (var peopleItem in peopleModelList)
                    {
                        var data = new
                        {
                            Name = peopleItem.DisplayName,
                            Email = peopleItem.EmailAddress,
                            tabUrl = "https://teams.microsoft.com/l/entity/2b876f4d-2e6b-4ee7-9b09-8893808c1380/1Page" + "?webUrl=" + "https://web.get1page.com/h/"
                        };
                        var card = template.Expand(data);
                        cardJson = string.Concat(cardJson, " ", card);
                    }
                }
                else
                {
                    cardJson = System.IO.File.ReadAllText(filepath);
                }
                var adaptiveCardAttachment = new Attachment
                {
                    ContentType = "application/vnd.microsoft.card.adaptive",
                    Content = JsonConvert.DeserializeObject(cardJson)
                };
                return adaptiveCardAttachment;
            }
            catch (Exception ex)
            {
                return null;
            }
        }
        public Attachment FetchConnector(string filepath)
        {
            var connectorCardJson = File.ReadAllText(filepath);
            var connectorCardAttachment = new MessagingExtensionAttachment
            {
                ContentType = O365ConnectorCard.ContentType,
                Content = JsonConvert.DeserializeObject(connectorCardJson),

            };
            return connectorCardAttachment;
        }

        public MessagingExtensionResponse GetResultGrid()
        {
            var imageFiles = Directory.EnumerateFiles("wwwroot", "*.*", SearchOption.AllDirectories)
            .Where(s => s.EndsWith(".jpg"));

            List<MessagingExtensionAttachment> attachments = new List<MessagingExtensionAttachment>();

            foreach (string img in imageFiles)
            {
                var image = img.Split("\\");
                var thumbnailCard = new ThumbnailCard();
                thumbnailCard.Images = new List<CardImage>() { new CardImage(_baseUrl + "/" + image[1]) };
                var attachment = new MessagingExtensionAttachment
                {
                    ContentType = ThumbnailCard.ContentType,
                    Content = thumbnailCard,
                };
                attachments.Add(attachment);
            }
            return new MessagingExtensionResponse
            {
                ComposeExtension = new MessagingExtensionResult
                {
                    Type = "result",
                    AttachmentLayout = "grid",
                    Attachments = attachments
                }
            };
        }
    }
}
