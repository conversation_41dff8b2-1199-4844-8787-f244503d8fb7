﻿// Copyright (c) Microsoft Corporation. All rights reserved.
// Licensed under the MIT License.

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Text.Encodings.Web;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using AdaptiveCards.Templating;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Integration.AspNet.Core;
using Microsoft.Bot.Schema;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Graph;
using Newtonsoft.Json;
using OnePage.Models.Cosmos;
using OnePage.Models.Models;
using OnePage.Models.Models.PeopleModel;
using OnePage.Services;
using OnePage.Services.Interfaces;
using ProactiveBot.Services;
using Attachment = Microsoft.Bot.Schema.Attachment;
using TelegramService = ProactiveBot.Services.TelegramService;
using Microsoft.ApplicationInsights.DataContracts;
using OnePage.Common;

namespace ProactiveBot.Controllers
{
    [Route("api/notify")]
    [ApiController]
    public class NotifyController : ControllerBase
    {
        private readonly ITelemetryTrack telemetryTrack;
        private readonly PeopleContext wepdb;
        private readonly EventRDSContext _weedb;
        private readonly BotService botService;
        //CosmosService cosmosService;
        string eventData;
        private readonly string _cards = Path.Combine(".", "Resources", "card.json");
        private readonly string _researchcards = Path.Combine(".", "Resources", "research.json");
        private readonly IBotFrameworkHttpAdapter _adapter;
        private readonly string _appId;
        private readonly ConcurrentDictionary<string, ConversationReference> _conversationReferences;

        public NotifyController(IBotFrameworkHttpAdapter adapter, IConfiguration configuration, BotService botService, ConcurrentDictionary<string, ConversationReference> conversationReferences, ITelemetryTrack track, PeopleContext wepdb, EventRDSContext weedb)
        {
            telemetryTrack = track;
            this.wepdb = wepdb;
            _weedb = weedb;

            //cosmosService = new();
            _adapter = adapter;
            _conversationReferences = conversationReferences;
            this.botService = botService;
            _appId = configuration["MicrosoftAppId"] ?? string.Empty;
        }
        public async Task<IActionResult> Get(string RecipientId, string ConversationId, string ConversationTenantId, Guid SignalId)
        {
            Dictionary<string, string> properties = new Dictionary<string, string>();
            try
            {
                //foreach (var conversationReference in _conversationReferences.Values)
                //{
                var signalData = wepdb.Signals.First(w => w.Id == SignalId);
                eventData = signalData.AdditionalData;
                ConversationReference cr = new ConversationReference();
                ConversationAccount ca = new ConversationAccount();
                ChannelAccount user = new ChannelAccount();
                user.Id = RecipientId;
                ca.Id = ConversationId;
                ca.TenantId = ConversationTenantId;
                ca.ConversationType = "personal";
                cr.Conversation = ca;
                cr.ChannelId = "msteams";
                cr.Locale = "en-US";
                cr.User = user;
                cr.ServiceUrl = "https://smba.trafficmanager.net/amer/";
                properties.Add("userid", user.Id.ToString());
                telemetryTrack.TrackTrace("ca.Id" + ca.Id + "userId" + user.Id, SeverityLevel.Information);
                telemetryTrack.TrackTrace("ca.TenantId" + ca.TenantId + "userId" + user.Id, SeverityLevel.Information);
                telemetryTrack.TrackTrace("cr.Converstion" + cr.Conversation.Name + "userId" + user.Id, SeverityLevel.Information);
                await ((BotAdapter)_adapter).ContinueConversationAsync(_appId, cr, BotCallback, default(CancellationToken));
                telemetryTrack.TrackEvent("BotNotification", properties);
                //  }

                // Let the caller know proactive messages have been sent
                return new ContentResult()
                {
                    Content = "<html><body><h1>Proactive messages have been sent.</h1></body></html>",
                    ContentType = "text/html",
                    StatusCode = (int)HttpStatusCode.OK,
                };
            }
            catch (Exception ex)
            {
                telemetryTrack.TrackException(ex, Guid.Empty);
                return BadRequest(ex.Message);
            }

        }

        private async Task BotCallback(ITurnContext turnContext, CancellationToken cancellationToken)
        {
            Dictionary<string, string> properties = new Dictionary<string, string>();
            try
            {

                if (!string.IsNullOrEmpty(eventData))
                {
                    var eventModel = Newtonsoft.Json.JsonConvert.DeserializeObject<BotNotifyModel>(eventData);
                    //var attendees = await weedb.EventAttendees.Where(w => w.EventId == eventModel.EventId && w.IsUser == false && (w.BOne == true || w.BOne == null)).ToListAsync();
                    //if (attendees.Count > 0)
                    //{
                    //    foreach (var attendee in attendees)
                    //    {
                    //        PeopleDataModel pplModel = new PeopleDataModel();
                    //        pplModel.Email = attendee.Address;
                    //        pplModel.Name = attendee.Name;
                    //        pplModel.ContactId = attendee.ContactId.ToString();
                    //        eventModel.PeopleList.Add(pplModel);
                    //    }
                    //}
                    if (!string.IsNullOrEmpty(eventModel.Desc))
                    {
                        var adaptiveCardJson = System.IO.File.ReadAllText(_cards);
                        AdaptiveCardTemplate template = new AdaptiveCardTemplate(adaptiveCardJson);
                        var encodedContext = HttpUtility.UrlEncode("{\"subEntityId\": \"" + eventModel.EventId + "\"}");
                        var encodedWebUrl = HttpUtility.UrlEncode(eventModel.WebUrl);
                        properties.Add("userId", eventModel.UserId.ToString());
                        properties.Add("Subject", eventModel.Subject.ToString());
                        string lang = "en";
                        try
                        {
                            Guid uId = Guid.Parse(eventModel.UserId);
                            var langUserSetting = wepdb.UserSettings.FirstOrDefault(w => w.UserId == uId && w.SettingId == CommonData.SelectedLanguageSettingId && w.IsActive == true);
                            if (langUserSetting != null)
                            {
                                if (langUserSetting.Value == "es-CL")
                                {
                                    lang = "es-CL";
                                }
                            }
                        }
                        catch (Exception ex)
                        {

                        }
                        string url = "https://teams.microsoft.com/l/entity/2b876f4d-2e6b-4ee7-9b09-8893808c1380/1Page?webUrl=";
                        string encodedurl1 = "";
                        if (eventModel.Subject == "Starts Soon! Shall we prepare?" || eventModel.Subject == "¡Empieza pronto! ¿Nos preparamos?")
                        {
                            if (lang == "es-CL")
                            {
                                eventModel.Subject = "¡Empieza pronto! ¿Nos preparamos?";
                            }
                            else
                            {
                                eventModel.Subject = "Starts Soon! Shall we prepare?";
                            }
                        }
                        else if (eventModel.Subject == "Event has ended. Do you have any updates?" || eventModel.Subject == "El evento ha terminado. ¿Tiene alguna actualización?")
                        {
                            if (lang == "es-CL")
                            {
                                eventModel.Subject = "El evento ha terminado. ¿Tiene alguna actualización?";
                            }
                            else
                            {
                                eventModel.Subject = "Event has ended. Do you have any updates?";
                            }
                        }

                        if (eventModel.AppId.ToLower() == CommonData.GlobalAppId.ToString().ToLower())
                        {
                            encodedurl1 = HttpUtility.UrlEncode("https://auth.get1page.com/");
                        }
                        else
                        {
                            encodedurl1 = HttpUtility.UrlEncode("https://app.get1page.com/");
                        }
                        string orginialUrl = "{{\"subEntityId\": \"/h/e/{0}\"}}";
                        string formattedUrl = string.Format(orginialUrl, eventModel.EventId);
                        string encodedUrl = HttpUtility.UrlEncode(formattedUrl);

                        var cardObj = new
                        {
                            subject = eventModel.Subject,
                            time = eventModel.Start.ToString("t") + " - " + eventModel.End.ToString("t"),
                            description = eventModel.Desc,
                            tabUrl = url + encodedurl1 + "&context=" + encodedUrl,
                            peopleList = eventModel.PeopleList
                        };
                        if (cardObj.peopleList.Count > 0)
                        {
                            foreach (var ppl in cardObj.peopleList)
                            {
                                ppl.ContactUrl = "https://teams.microsoft.com/l/entity/" + "2b876f4d-2e6b-4ee7-9b09-8893808c1380" + "/" + eventModel.EntityId + "?webUrl=" + encodedWebUrl + "&label=contact" + "&context=" + "%7B%22subEntityId%22%3A%20%22" + ppl.ContactId + "%22%7D";
                            }
                        }
                        Services.TelegramService.SendMessageToTestBot2(cardObj.tabUrl + ", " + encodedContext + ", " + encodedWebUrl);
                        string cardJson = template.Expand(cardObj);
                        var cardAttachment = CreateAdaptiveCardAttachment(cardJson);
                        var response = MessageFactory.Text(string.Empty);
                        response.Attachments.Add(cardAttachment);
                        if (eventModel.Subject == "Starts Soon! Shall we prepare?" || eventModel.Subject == "¡Empieza pronto! ¿Nos preparamos?")
                        {
                            if (lang == "es-CL")
                            {
                                response.Summary = "¡Reunión a punto de comenzar!" + " - " + eventModel.Desc;
                            }
                            else
                            {
                                response.Summary = "Meeting about to start!" + " - " + eventModel.Desc;
                            }
                        }
                        else if (eventModel.Subject == "Event has ended. Do you have any updates?" || eventModel.Subject == "El evento ha terminado. ¿Tiene alguna actualización?")
                        {
                            if (lang == "es-CL")
                            {
                                response.Summary = "Reunión finalizada" + " - " + eventModel.Desc;
                            }
                            else
                            {
                                response.Summary = "Meeting ended" + " - " + eventModel.Desc;
                            }
                        }
                        var responsejson = JsonConvert.SerializeObject(response);
                        Services.TelegramService.SendMessageToTestBot2(responsejson);
                        var result = await turnContext.SendActivityAsync(response, cancellationToken);
                        var resjson = JsonConvert.SerializeObject(result);
                        Services.TelegramService.SendMessageToTestBot2(resjson);
                    }
                    else
                    {
                        var adaptiveCardJson = System.IO.File.ReadAllText(_researchcards);
                        AdaptiveCardTemplate template = new AdaptiveCardTemplate(adaptiveCardJson);
                        var cardObj = new
                        {
                            subject = eventModel.Subject
                        };

                        string cardJson = template.Expand(cardObj);
                        var cardAttachment = CreateAdaptiveCardAttachment(cardJson);
                        var response = MessageFactory.Text(string.Empty);
                        response.Attachments.Add(cardAttachment);
                        response.Summary = eventModel.Subject;
                        botService.SendMessageForTestBot("botnotification fn triggered");
                        await turnContext.SendActivityAsync(response, cancellationToken);
                    }
                }
            }
            catch (Exception ex)
            {
                botService.SendMessageForTestBot("notification exception: " + ex.ToString());
                Console.WriteLine(ex.ToString());
                Services.TelegramService.SendMessage(ex.Message.ToString());
                Services.TelegramService.SendMessage(ex.StackTrace.ToString());

            }
        }
        private static Attachment CreateAdaptiveCardAttachment(string _card)
        {
            var adaptiveCardAttachment = new Attachment()
            {
                ContentType = "application/vnd.microsoft.card.adaptive",
                Content = JsonConvert.DeserializeObject(_card),
            };
            return adaptiveCardAttachment;
        }
        public class PeopleModel
        {
            public string title { get; set; }
            public string value { get; set; }
        }
    }
}
