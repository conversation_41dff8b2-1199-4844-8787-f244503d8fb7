﻿{
  "type": "AdaptiveCard",
  "body": [
    {
      "type": "TextBlock",
      "wrap": true,
      "text": "${subject}",
      "weight": "Lighter",
      "separator": true

    },
    {
      "type": "TextBlock",
      "text": "${description}",
      "wrap": true,
      "size": "Small",
      "weight": "Bolder",
      "isSubtle": true,
      "spacing": "None"
    },
    {
      "type": "TextBlock",
      "wrap": true,
      "text": "${time}",
      "size": "Small",
      "weight": "Lighter",
      "isSubtle": true,
      "spacing": "None"
    },
    {
      "type": "Container",
      "$data": "${peopleList}",
      "items": [
        {
          "type": "TextBlock",
          "text": " ${Name} - ${Email} ",
          "wrap": true
        }
      ],
      "separator": false,
      "spacing": "Medium"
    },
    {
      "type": "ActionSet",
      "actions": [
        {
          "type": "Action.OpenUrl",
          "title": "Open",
          "url": "${tabUrl}"
        }
      ]
    }
  ],
  "$schema": "http://adaptivecards.io/schemas/adaptive-card.json",
  "version": "1.5"
}