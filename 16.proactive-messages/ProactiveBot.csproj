﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <LangVersion>latest</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(RunConfiguration)' == '.NET Core' " />
  <ItemGroup>
    <Content Remove="Resources\card.json" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Resources\card.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="AdaptiveCards.Templating" Version="1.2.2" />
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.21.0" />
    <PackageReference Include="Microsoft.Bot.Builder.Integration.AspNet.Core" Version="4.15.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.6" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\OnePage.Common\OnePage.Common.csproj" />
    <ProjectReference Include="..\OnePage.Models\OnePage.Models.csproj" />
    <ProjectReference Include="..\OnePage.Services\OnePage.Services.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Content Update="appsettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
</Project>