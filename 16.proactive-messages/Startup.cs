﻿// Copyright (c) Microsoft Corporation. All rights reserved.
// Licensed under the MIT License.

using System;
using Microsoft.Extensions.Configuration;
using System.Collections.Concurrent;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Builder.Integration.AspNet.Core;
using Microsoft.Bot.Connector.Authentication;
using Microsoft.Bot.Schema;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using OnePage.Models.Models;
using OnePage.Models.Models.AdminModel;
using OnePage.Models.Models.DataModel;
using OnePage.Models.Models.ENTEventModel;
using OnePage.Models.Models.OrgModel;
using OnePage.Models.Models.PeopleModel;
using OnePage.Models.Models.TokenModel;
using OnePage.Services;
using OnePage.Services.Interfaces;



namespace Microsoft.BotBuilderSamples
{
    public class Startup
    {

        public Startup(IConfiguration configuration)
        {
            this.Configuration = configuration;

        }
        IConfiguration Configuration { get; set; }
        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
            var appSettingsFile = $"appsettings.{environment}.json";
            var configuration = new ConfigurationBuilder()
                .AddJsonFile(appSettingsFile)
                .Build();

            services.AddHttpClient().AddControllers().AddNewtonsoftJson();

            // Create the Bot Framework Authentication to be used with the Bot Adapter.
            services.AddSingleton<BotFrameworkAuthentication, ConfigurationBotFrameworkAuthentication>();

            // Create the Bot Adapter with error handling enabled.
            services.AddSingleton<IBotFrameworkHttpAdapter, AdapterWithErrorHandler>();

            // Create a global hashset for our ConversationReferences
            services.AddSingleton<ConcurrentDictionary<string, ConversationReference>>();

            services.AddApplicationInsightsTelemetry();
            services.AddScoped<ITelemetryTrack, TelemetryTrack>();
            services.AddScoped<BotService>();

            // Create the bot as a transient. In this case the ASP Controller is expecting an IBot.
            services.AddTransient<IBot, ProactiveBot>();
            services.AddDbContext<OrgContext>(options =>
                options.UseSqlServer(Configuration["DBConnections:WEOrgConnectionString"]));

            services.AddDbContext<DataContext>(options =>
                options.UseSqlServer(Configuration["DBConnections:WEDataConnectionString"]));

            services.AddDbContext<PeopleContext>(options =>
                options.UseSqlServer(Configuration["DBConnections:WEPeopleConnectionString"]));

            services.AddDbContext<AdminContext>(options =>
                options.UseSqlServer(Configuration["DBConnections:WEAdminConnectionString"]));

            services.AddDbContext<TokenContext>(options =>
                options.UseSqlServer(Configuration["DBConnections:WETokenConnectionString"]));

            services.AddDbContext<ContactContext>(options =>
                options.UseSqlServer(Configuration["DBConnections:WEContactConnectionString"]));

            services.AddDbContext<EventRDSContext>(options =>
                options.UseSqlServer(Configuration["DBConnections:WEEventRDSConnectionString"]));
            services.AddDbContext<ENTEventContext>(option =>
                option.UseSqlServer(Configuration["DBConnections:WEEventRDSConnectionString"]));
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }

            app.UseDefaultFiles()
                .UseStaticFiles()
                .UseRouting()
                .UseAuthorization()
                .UseEndpoints(endpoints =>
                {
                    endpoints.MapControllers();
                });

            // app.UseHttpsRedirection();
        }
    }
}
