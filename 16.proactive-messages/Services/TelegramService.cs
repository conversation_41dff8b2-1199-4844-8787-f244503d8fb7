﻿using System;
using System.Net;

namespace ProactiveBot.Services
{
    public static class TelegramService
    {
        //  static readonly string token = "1979617440:AAE_q-2irwpi5IdoKIersDaruxOa81uIkFk";
        static readonly string token = "5277113211:AAGCRaM-0ZL25CqTOfzcPMfwlMo6V36FMBI";
        static readonly string gaganBotToken = "6880724844:AAH8y6CcDA634gKhODWCBle0S54mhzXoxck";
        static readonly string prakashBotToken = "7168330395:AAH7KtPIyL5R6MnfpD2jdHvlnvMx-9D79m8";
        // static readonly string chatId = "65294787";
        static readonly string chatId = "184762097";
        static readonly string gaganChatId = "6568143944";
        static readonly string prakashChatId = "796226735";

        public static string SendMessage(string message)
        {
            string retval = string.Empty;
            //string url = $"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatId}&text={message}";

            //using (var webClient = new WebClient())
            //{
            //    retval = webClient.DownloadString(url);
            //}

            return retval;
        }
        public static string SendMessageToTestBot2(string message)
        {
            try
            {
                string retval = string.Empty;

                //string url = $"https://api.telegram.org/bot{gaganBotToken}/sendMessage?chat_id={gaganChatId}&text={message}";

                //using (var webClient = new WebClient())
                //{
                //    retval = webClient.DownloadString(url);
                //}

                return retval;
            }
            catch (Exception ex)
            {
                return "";
            }
        }
    }
}
