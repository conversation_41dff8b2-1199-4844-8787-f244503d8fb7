{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"groupLocation": {"type": "string", "metadata": {"description": "Specifies the location of the Resource Group."}}, "groupName": {"type": "string", "metadata": {"description": "Specifies the name of the Resource Group."}}, "appId": {"type": "string", "metadata": {"description": "Active Directory App ID or User-Assigned Managed Identity Client ID, set as MicrosoftAppId in the Web App's Application Settings."}}, "appSecret": {"type": "string", "defaultValue": "", "metadata": {"description": "Active Directory App Password, set as MicrosoftAppPassword in the Web App's Application Settings. Required for MultiTenant and SingleTenant app types. Defaults to \"\"."}}, "appType": {"type": "string", "defaultValue": "MultiTenant", "allowedValues": ["MultiTenant", "SingleTenant", "UserAssignedMSI"], "metadata": {"description": "Type of Bot Authentication. set as MicrosoftAppType in the Web App's Application Settings. Allowed values are: MultiTenant, SingleTenant, UserAssignedMSI. Defaults to \"MultiTenant\"."}}, "botId": {"type": "string", "metadata": {"description": "The globally unique and immutable bot ID. Also used to configure the displayName of the bot, which is mutable."}}, "botSku": {"type": "string", "metadata": {"description": "The pricing tier of the Bot Service Registration. Acceptable values are F0 and S1."}}, "newAppServicePlanName": {"type": "string", "metadata": {"description": "The name of the App Service Plan."}}, "newAppServicePlanSku": {"type": "object", "defaultValue": {"name": "S1", "tier": "Standard", "size": "S1", "family": "S", "capacity": 1}, "metadata": {"description": "The SKU of the App Service Plan. Defaults to Standard values."}}, "newAppServicePlanLocation": {"type": "string", "metadata": {"description": "The location of the App Service Plan. Defaults to \"westus\"."}}, "newWebAppName": {"type": "string", "defaultValue": "", "metadata": {"description": "The globally unique name of the Web App. Defaults to the value passed in for \"botId\"."}}, "tenantId": {"type": "string", "defaultValue": "[subscription().tenantId]", "metadata": {"description": "The Azure AD Tenant ID to use as part of the Bot's Authentication. Only used for SingleTenant and UserAssignedMSI app types. Defaults to \"Subscription Tenant ID\"."}}, "existingUserAssignedMSIName": {"type": "string", "defaultValue": "", "metadata": {"description": "The User-Assigned Managed Identity Resource used for the Bot's Authentication. Defaults to \"\"."}}, "existingUserAssignedMSIResourceGroupName": {"type": "string", "defaultValue": "", "metadata": {"description": "The User-Assigned Managed Identity Resource Group used for the Bot's Authentication. Defaults to \"\"."}}}, "variables": {"appServicePlanName": "[parameters('newAppServicePlanName')]", "resourcesLocation": "[parameters('newAppServicePlanLocation')]", "webAppName": "[if(empty(parameters('newWebAppName')), parameters('botId'), parameters('newWebAppName'))]", "siteHost": "[concat(variables('webAppName'), '.azurewebsites.net')]", "botEndpoint": "[concat('https://', variables('siteHost'), '/api/messages')]", "resourceGroupId": "[concat(subscription().id, '/resourceGroups/', parameters('groupName'))]", "msiResourceId": "[concat(subscription().id, '/resourceGroups/', parameters('existingUserAssignedMSIResourceGroupName'), '/providers/', 'Microsoft.ManagedIdentity/userAssignedIdentities/', parameters('existingUserAssignedMSIName'))]", "appTypeDef": {"MultiTenant": {"tenantId": "", "msiResourceId": "", "identity": {"type": "None"}}, "SingleTenant": {"tenantId": "[parameters('tenantId')]", "msiResourceId": "", "identity": {"type": "None"}}, "UserAssignedMSI": {"tenantId": "[parameters('tenantId')]", "msiResourceId": "[variables('msiResourceId')]", "identity": {"type": "UserAssigned", "userAssignedIdentities": {"[variables('msiResourceId')]": {}}}}}, "appType": {"tenantId": "[variables('appTypeDef')[parameters('appType')].tenantId]", "msiResourceId": "[variables('appTypeDef')[parameters('appType')].msiResourceId]", "identity": "[variables('appTypeDef')[parameters('appType')].identity]"}}, "resources": [{"name": "[parameters('groupName')]", "type": "Microsoft.Resources/resourceGroups", "apiVersion": "2018-05-01", "location": "[parameters('groupLocation')]", "properties": {}}, {"type": "Microsoft.Resources/deployments", "apiVersion": "2018-05-01", "name": "storageDeployment", "resourceGroup": "[parameters('groupName')]", "dependsOn": ["[resourceId('Microsoft.Resources/resourceGroups/', parameters('groupName'))]"], "properties": {"mode": "Incremental", "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {}, "variables": {}, "resources": [{"comments": "Create a new App Service Plan", "type": "Microsoft.Web/serverfarms", "name": "[variables('appServicePlanName')]", "apiVersion": "2018-02-01", "location": "[variables('resourcesLocation')]", "sku": "[parameters('newAppServicePlanSku')]", "properties": {"name": "[variables('appServicePlanName')]"}}, {"comments": "Create a Web App using the new App Service Plan", "type": "Microsoft.Web/sites", "apiVersion": "2015-08-01", "location": "[variables('resourcesLocation')]", "kind": "app", "dependsOn": ["[concat(variables('resourceGroupId'), '/providers/Microsoft.Web/serverfarms/', variables('appServicePlanName'))]"], "name": "[variables('webAppName')]", "identity": "[variables('appType').identity]", "properties": {"name": "[variables('webAppName')]", "serverFarmId": "[variables('appServicePlanName')]", "siteConfig": {"appSettings": [{"name": "WEBSITE_NODE_DEFAULT_VERSION", "value": "10.14.1"}, {"name": "MicrosoftAppType", "value": "[parameters('appType')]"}, {"name": "MicrosoftAppId", "value": "[parameters('appId')]"}, {"name": "MicrosoftAppPassword", "value": "[parameters('appSecret')]"}, {"name": "MicrosoftAppTenantId", "value": "[variables('appType').tenantId]"}], "cors": {"allowedOrigins": ["https://botservice.hosting.portal.azure.net", "https://hosting.onecloud.azure-test.net/"]}}}}, {"apiVersion": "2021-03-01", "type": "Microsoft.BotService/botServices", "name": "[parameters('botId')]", "location": "global", "kind": "azurebot", "sku": {"name": "[parameters('botSku')]"}, "properties": {"name": "[parameters('botId')]", "displayName": "[parameters('botId')]", "iconUrl": "https://docs.botframework.com/static/devportal/client/images/bot-framework-default.png", "endpoint": "[variables('botEndpoint')]", "msaAppId": "[parameters('appId')]", "msaAppTenantId": "[variables('appType').tenantId]", "msaAppMSIResourceId": "[variables('appType').msiResourceId]", "msaAppType": "[parameters('appType')]", "luisAppIds": [], "schemaTransformationVersion": "1.3", "isCmekEnabled": false, "isIsolated": false}, "dependsOn": ["[concat(variables('resourceGroupId'), '/providers/Microsoft.Web/sites/', variables('webAppName'))]"]}], "outputs": {}}}}]}