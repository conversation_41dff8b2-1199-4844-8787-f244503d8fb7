﻿using Microsoft.ApplicationInsights.DataContracts;
using System.Collections.Generic;
using System.Diagnostics;
using System;

namespace ProactiveBot.TelemetryService
{
    public interface ITelemetryTrack
    {
        void TrackException(Exception ex, Guid userId);
        void TrackTrace(string message, SeverityLevel severity);
        void TrackEvent(string eventName, Dictionary<string, string> properties = null);
        Stopwatch StartTrackRequest(string requestName);
        void StopTrackRequest(string requestName, Stopwatch stopwatch);
    }
}
