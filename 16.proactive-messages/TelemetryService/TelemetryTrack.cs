﻿using Microsoft.ApplicationInsights.DataContracts;
using Microsoft.ApplicationInsights;
using System.Collections.Generic;
using System.Diagnostics;
using System;

namespace ProactiveBot.TelemetryService
{
    public class TelemetryTrack:ITelemetryTrack
    {
        private readonly TelemetryClient _telemetry = new TelemetryClient
        {
            InstrumentationKey = "b7162ca6-849a-49c0-8157-07245147e069"
        };

        public void TrackException(Exception ex, Guid userId)
        {
            Dictionary<string, string> properties = new Dictionary<string, string>();
            properties.Add("userId", userId.ToString());
            _telemetry.TrackException(ex.InnerException, properties);
            _telemetry.TrackTrace(ex.StackTrace, properties);
            _telemetry.TrackException(ex, properties);
            _telemetry.Flush();
        }

        public void TrackTrace(string message, SeverityLevel severity)
        {

            _telemetry.TrackTrace(message, severity);
        }

        public void TrackEvent(string eventName, Dictionary<string, string> properties = null)
        {
            _telemetry.TrackEvent(eventName, properties);
        }

        public Stopwatch StartTrackRequest(string requestName)
        {
            // Operation Id is attached to all telemetry and helps you identify
            // telemetry associated with one request:
            _telemetry.Context.Operation.Id = Guid.NewGuid().ToString();
            return Stopwatch.StartNew();

        }

        public void StopTrackRequest(string requestName, Stopwatch stopwatch)
        {
            stopwatch.Stop();
            _telemetry.TrackRequest(requestName, DateTime.Now,
                stopwatch.Elapsed,
                "200", true);  // Response code, success
        }
    }
}

