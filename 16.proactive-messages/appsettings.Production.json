{"MicrosoftAppType": "UserAssignedMSI", "MicrosoftAppId": "12d08be6-5118-4f93-a514-42d0365a236d", "MicrosoftAppPassword": "", "MicrosoftAppTenantId": "d0da15a3-00e9-4854-be4a-464d0a484d60", "BaseUrl": "https://eu-fc-wb-bt.azurewebsites.net/", "AllowedHosts": "*", "ApplicationInsights": {"ConnectionString": "InstrumentationKey=b7162ca6-849a-49c0-8157-07245147e069;IngestionEndpoint=https://northeurope-2.in.applicationinsights.azure.com/;LiveEndpoint=https://northeurope.livediagnostics.monitor.azure.com/"}, "DBConnections": {"WEAdminConnectionString": "Data Source=eu-ne-sd.database.windows.net;Initial Catalog=eu-fc-ss-ad;Persist Security Info=True;User ID=eu-ne-sd;Password=**************;MultipleActiveResultSets=True;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;", "WEDataConnectionString": "Data Source=eu-ne-sd.database.windows.net;Initial Catalog=eu-fc-ss-dt;Persist Security Info=True;User ID=eu-ne-sd;Password=**************;MultipleActiveResultSets=True;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;", "WEOrgConnectionString": "Data Source=eu-ne-sd.database.windows.net;Initial Catalog=eu-fc-ss-or;Persist Security Info=True;User ID=eu-ne-sd;Password=**************;MultipleActiveResultSets=True;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;", "WEPeopleConnectionString": "Data Source=eu-ne-sd.database.windows.net;Initial Catalog=eu-fc-ss-pp;Persist Security Info=True;User ID=eu-ne-sd;Password=**************;MultipleActiveResultSets=True;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;", "WETokenConnectionString": "Data Source=eu-ne-sd.database.windows.net;Initial Catalog=eu-fc-ss-tk;Persist Security Info=True;User ID=eu-ne-sd;Password=**************;MultipleActiveResultSets=True;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;", "WEContactConnectionString": "Data Source=eu-ne-sd.database.windows.net;Initial Catalog=eu-fc-ss-ct;Persist Security Info=True;User ID=eu-ne-sd;Password=**************;MultipleActiveResultSets=True;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;", "WEENTEventConnectionString": "Data Source=eu-ne-sd.database.windows.net;Initial Catalog=ent-ev;Persist Security Info=True;User ID=eu-ne-sd;Password=**************;MultipleActiveResultSets=True;Encrypt=True;TrustServerCertificate=False;Connection Timeout=60;", "WEEventRDSConnectionString": "Data Source=eu-ne-sd.database.windows.net;Initial Catalog=ev;Persist Security Info=True;User ID=eu-ne-sd;Password=**************;MultipleActiveResultSets=True;Encrypt=True;TrustServerCertificate=False;Connection Timeout=60;"}}