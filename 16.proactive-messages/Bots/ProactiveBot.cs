﻿// Copyright (c) Microsoft Corporation. All rights reserved.
// Licensed under the MIT License.

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Bot.Builder;
using Microsoft.Bot.Schema;
using Newtonsoft.Json;
using OnePage.Models.Models;
using OnePage.Models.Models.DataModel;
using OnePage.Models.Models.PeopleModel;
using OnePage.Services.Interfaces;
using ProactiveBot.Services;
using static OnePage.Models.Models.PropertyModels.ContactDataModel;

namespace Microsoft.BotBuilderSamples
{
    public class ProactiveBot : ActivityHandler
    {

        private readonly PeopleContext _wepdb;
        private readonly DataContext _weddb;
        // public ProactiveBot(PeopleContext wepdb, DataContext weddb)
        // {
        //     _wepdb = wepdb;
        //     _weddb = weddb;
        // }
        private readonly ITelemetryTrack telemetry;
        // Message to send to users when the bot receives a Conversation Update event
        private const string WelcomeMessage = "Welcome to 1Page Bot! \r\n This bot helps you prepare for meetings with timely alerts, before the meeting, and once the meeting is over, another alert to take notes. \r\n  \r\n Soon this bot will allow you to request more details with actions and messages. \r\n \r\n It is a notification only bot and users will not be able to interact with the bot.";

        // Dependency injected dictionary for storing ConversationReference objects used in NotifyController to proactively message users
        private readonly ConcurrentDictionary<string, ConversationReference> _conversationReferences;

        public ProactiveBot(ConcurrentDictionary<string, ConversationReference> conversationReferences,ITelemetryTrack telemetryTrack, PeopleContext wepdb, DataContext weddb)
        {
            telemetry = telemetryTrack;
            _wepdb = wepdb;
            _weddb = weddb;
            _conversationReferences = conversationReferences;
            
        }

        private void AddConversationReference(Activity activity)
        {
            var conversationReference = activity.GetConversationReference();
            _conversationReferences.AddOrUpdate(conversationReference.User.Id, conversationReference, (key, newValue) => conversationReference);
        }

        protected override Task OnConversationUpdateActivityAsync(ITurnContext<IConversationUpdateActivity> turnContext, CancellationToken cancellationToken)
        {
            AddConversationReference(turnContext.Activity as Activity);

            return base.OnConversationUpdateActivityAsync(turnContext, cancellationToken);
        }
        
        protected override async Task OnMembersAddedAsync(IList<ChannelAccount> membersAdded, ITurnContext<IConversationUpdateActivity> turnContext, CancellationToken cancellationToken)
        {
            TelegramService.SendMessage("OnMemeberAdded");
            foreach (var member in membersAdded)
            {
                string msg = "ConvId: " + turnContext.Activity.Conversation.Id + ",Conv TenantId: " + turnContext.Activity.Conversation.TenantId + ",ChannelId: " + turnContext.Activity.ChannelId + ",ServiceUrl: " + turnContext.Activity.ServiceUrl + ",Conv AadObjectId: " + turnContext.Activity.Conversation.AadObjectId + ",Recipient Id: " + turnContext.Activity.Recipient.Id + ",Recipient AadObjectId: " + turnContext.Activity.Recipient.AadObjectId + "," + turnContext.Activity.ChannelData + turnContext.Activity.Conversation.IsGroup + "," + turnContext.Activity.Conversation.Name + ", " + turnContext.Activity.Conversation.Properties + ", " + turnContext.Activity.Conversation.Role + ", " + turnContext.Activity.From.Id + ", " + turnContext.Activity.Recipient.Properties;
                TelegramService.SendMessage(msg);
                Dictionary<string, string> properties = new Dictionary<string, string>();
                properties.Add("msg", msg);
                telemetry.TrackEvent("msg", properties);
                telemetry.TrackTrace(msg, ApplicationInsights.DataContracts.SeverityLevel.Information);
                try
                {
                    var isExistingUPI = _wepdb.UserProviderIdentifiers.Any(w => w.Value == turnContext.Activity.From.AadObjectId);
                    BotDataModel contextModel = new BotDataModel();
                    contextModel.ChannelId = turnContext.Activity.ChannelId;
                    contextModel.ConvAadObjId = turnContext.Activity.Conversation.AadObjectId;
                    contextModel.ConvId = turnContext.Activity.Conversation.Id;
                    contextModel.ConvTenantId = turnContext.Activity.Conversation.TenantId;
                    contextModel.FromAadObjId = turnContext.Activity.From.AadObjectId;
                    contextModel.RecipientAadObjectId = turnContext.Activity.Recipient.AadObjectId;
                    contextModel.RecipientId = turnContext.Activity.Recipient.Id;
                    var jsonData = JsonConvert.SerializeObject(contextModel);
                    var checkForExisting = _wepdb.BotData.Any(w => w.Data == jsonData);
                    if (!checkForExisting)
                    {
                        BotDatum botData = new BotDatum();
                        botData.Id = Guid.NewGuid();
                        botData.CreatedDate = DateTime.UtcNow;
                        botData.Data = jsonData;
                        botData.IsActive = true;
                        _wepdb.BotData.Add(botData);
                        _wepdb.SaveChanges();
                    }
                    //try
                    //{
                    //    if (isExistingUPI)
                    //    {
                    //        var orgId = Guid.Parse("97859466-D85E-47E7-ABB5-56CF76335318");
                    //        Guid BotProviderId = Guid.Parse("825E7926-04DB-409F-8BCB-F48ED904156C");
                    //        var identifierList = weddb.Identifiers.Where(w => w.ProviderId == BotProviderId && w.IsActive == true).ToList();
                    //        if (identifierList.Count > 0)
                    //        {
                    //            foreach (var identifier in identifierList)
                    //            {
                    //                if (identifier.Name == "blconversationid")
                    //                {
                    //                    identifier.Value = turnContext.Activity.Conversation.Id;
                    //                }
                    //                else if (identifier.Name == "blconversationtenantid")
                    //                {
                    //                    identifier.Value = turnContext.Activity.Conversation.TenantId;
                    //                }
                    //                else if (identifier.Name == "blchannelid")
                    //                {
                    //                    identifier.Value = turnContext.Activity.ChannelId;
                    //                }
                    //                else if (identifier.Name == "blconversationaadobjectid")
                    //                {
                    //                    identifier.Value = turnContext.Activity.Conversation.AadObjectId;
                    //                }
                    //                else if (identifier.Name == "blrecipientid")
                    //                {
                    //                    identifier.Value = turnContext.Activity.Recipient.Id;
                    //                }
                    //                else if (identifier.Name == "blrecipientaadobjectid")
                    //                {
                    //                    identifier.Value = turnContext.Activity.Recipient.AadObjectId;
                    //                }
                    //                else if (identifier.Name == "blfromaadobjid")
                    //                {
                    //                    identifier.Value = turnContext.Activity.From.AadObjectId;
                    //                }
                    //            }
                    //        }
                    //        Guid teamsIId = Guid.Parse("B41E74CA-B8DE-4768-9C4C-77BD2AC71F22");
                    //        var existingUPI = wepdb.UserProviderIdentifiers.First(w => w.Value == turnContext.Activity.From.AadObjectId && w.IdentifierId == teamsIId);
                    //        var userProvider = wepdb.UserProviders.First(w => w.Id == existingUPI.UserProviderId);
                    //        var user = wepdb.Users.First(w => w.Id == userProvider.UserId);
                    //        var isExistingBotUP = wepdb.UserProviders.Any(w => w.UserId == user.Id && w.ProviderId == BotProviderId && w.IsActive == true);
                    //        if (!isExistingBotUP)
                    //        {
                    //            var newUserProvider = new UserProvider();
                    //            newUserProvider.Id = Guid.NewGuid();
                    //            newUserProvider.UserId = user.Id;
                    //            newUserProvider.AppId = user.AppId;
                    //            newUserProvider.OrgId = orgId;
                    //            newUserProvider.ProviderId = BotProviderId;
                    //            newUserProvider.EmailAddress = user.Email.ToLower();
                    //            newUserProvider.IsAuthenticated = true;
                    //            newUserProvider.IsActive = true;
                    //            newUserProvider.Code = "";
                    //            newUserProvider.ActiveFrom = DateTime.UtcNow;
                    //            newUserProvider.ActiveTill = DateTime.UtcNow.AddYears(1);
                    //            newUserProvider.IsProvisioned = false;
                    //            newUserProvider.IsFree = true;
                    //            newUserProvider.IsPayed = false;
                    //            newUserProvider.IsFullSyncDone = false;
                    //            newUserProvider.Source = 0;
                    //            newUserProvider.PurchaseId = "";
                    //            newUserProvider.PurchaseState = 0;
                    //            newUserProvider.ProductId = "";
                    //            newUserProvider.CreatedDate = DateTime.UtcNow;
                    //            newUserProvider.ModifiedDate = DateTime.UtcNow;
                    //            newUserProvider.IsRelogginRequired = false;
                    //            newUserProvider.Priority = 100;
                    //            int trialDays = 30;
                    //            newUserProvider.IsTrial = false;
                    //            wepdb.UserProviders.Add(newUserProvider);
                    //            wepdb.SaveChanges();

                    //            foreach (var identifier in identifierList)
                    //            {
                    //                try
                    //                {
                    //                    if (identifier.Value != null && identifier.Value != "")
                    //                    {

                    //                        var upIdentifiers = new UserProviderIdentifier();
                    //                        upIdentifiers.Id = Guid.NewGuid();
                    //                        upIdentifiers.IdentifierId = identifier.Id;
                    //                        upIdentifiers.UserProviderId = newUserProvider.Id;
                    //                        upIdentifiers.Value = identifier.Value;
                    //                        upIdentifiers.IsActive = true;
                    //                        upIdentifiers.CreatedDate = DateTime.UtcNow;
                    //                        upIdentifiers.ModifiedDate = DateTime.UtcNow;
                    //                        wepdb.UserProviderIdentifiers.Add(upIdentifiers);
                    //                        wepdb.SaveChanges();
                    //                    }
                    //                }
                    //                catch (Exception ex)
                    //                {
                    //                    continue;
                    //                    // throw;
                    //                }
                    //            }
                    //        }
                    //    }
                    //}
                    //catch (Exception ex)
                    //{
                    //    TelegramService.SendMessage("Error in bot code:" + ex.StackTrace.ToString());
                    //}
                }
                catch (Exception ex)
                {
                    TelegramService.SendMessage("Error in bot code:" + ex.StackTrace.ToString());
                }

                // Greet anyone that was not the target (recipient) of this message.
                if (member.Id != turnContext.Activity.Recipient.Id)
                {
                    var result = await turnContext.SendActivityAsync(MessageFactory.Text(WelcomeMessage), cancellationToken);
                }
            }
        }

        protected override async Task OnMessageActivityAsync(ITurnContext<IMessageActivity> turnContext, CancellationToken cancellationToken)
        {
            AddConversationReference(turnContext.Activity as Activity);

            try
            {
                string msg = "ConvId: " + turnContext.Activity.Conversation.Id + ",Conv TenantId: " + turnContext.Activity.Conversation.TenantId + ",ChannelId: " + turnContext.Activity.ChannelId + ",ServiceUrl: " + turnContext.Activity.ServiceUrl + ",Conv AadObjectId: " + turnContext.Activity.Conversation.AadObjectId + ",Recipient Id: " + turnContext.Activity.Recipient.Id + ",Recipient AadObjectId: " + turnContext.Activity.Recipient.AadObjectId + "," + turnContext.Activity.ChannelData + turnContext.Activity.Conversation.IsGroup + "," + turnContext.Activity.Conversation.Name + ", " + turnContext.Activity.Conversation.Properties + ", " + turnContext.Activity.Conversation.Role + ",From.AadObjectId " + turnContext.Activity.From.AadObjectId + ", " + turnContext.Activity.Recipient.Properties + ", " + turnContext.TurnState.Keys + ", ";
                TelegramService.SendMessage(msg);
                var isExistingUPI = _wepdb.UserProviderIdentifiers.Any(w => w.Value == turnContext.Activity.From.AadObjectId);
                try
                {
                    if (isExistingUPI)
                    {
                        var orgId = Guid.Parse("97859466-D85E-47E7-ABB5-56CF76335318");
                        Guid BotProviderId = Guid.Parse("825E7926-04DB-409F-8BCB-F48ED904156C");
                        var identifierList = _weddb.Identifiers.Where(w => w.ProviderId == BotProviderId && w.IsActive == true).ToList();
                        if (identifierList.Count > 0)
                        {
                            foreach (var identifier in identifierList)
                            {
                                if (identifier.Name == "blconversationid")
                                {
                                    identifier.Value = turnContext.Activity.Conversation.Id;
                                }
                                else if (identifier.Name == "blconversationtenantid")
                                {
                                    identifier.Value = turnContext.Activity.Conversation.TenantId;
                                }
                                else if (identifier.Name == "blchannelid")
                                {
                                    identifier.Value = turnContext.Activity.ChannelId;
                                }
                                else if (identifier.Name == "blconversationaadobjectid")
                                {
                                    identifier.Value = turnContext.Activity.Conversation.AadObjectId;
                                }
                                else if (identifier.Name == "blrecipientid")
                                {
                                    identifier.Value = turnContext.Activity.Recipient.Id;
                                }
                                else if (identifier.Name == "blrecipientaadobjectid")
                                {
                                    identifier.Value = turnContext.Activity.Recipient.AadObjectId;
                                }
                                else if (identifier.Name == "blfromaadobjid")
                                {
                                    identifier.Value = turnContext.Activity.From.AadObjectId;
                                }
                            }
                        }
                        Guid teamsIId = Guid.Parse("B41E74CA-B8DE-4768-9C4C-77BD2AC71F22");
                        var existingUPI = _wepdb.UserProviderIdentifiers.First(w => w.Value == turnContext.Activity.From.AadObjectId && w.IdentifierId == teamsIId);
                        var userProvider = _wepdb.UserProviders.First(w => w.Id == existingUPI.UserProviderId);
                        var user = _wepdb.Users.First(w => w.Id == userProvider.UserId);
                        var isExistingBotUP = _wepdb.UserProviders.Any(w => w.UserId == user.Id && w.ProviderId == BotProviderId && w.IsActive == true);
                        if (!isExistingBotUP)
                        {
                            var newUserProvider = new UserProvider();
                            newUserProvider.Id = Guid.NewGuid();
                            newUserProvider.UserId = user.Id;
                            newUserProvider.AppId = user.AppId;
                            newUserProvider.OrgId = orgId;
                            newUserProvider.ProviderId = BotProviderId;
                            newUserProvider.EmailAddress = user.Email.ToLower();
                            newUserProvider.IsAuthenticated = true;
                            newUserProvider.IsActive = true;
                            newUserProvider.Code = "";
                            newUserProvider.ActiveFrom = DateTime.UtcNow;
                            newUserProvider.ActiveTill = DateTime.UtcNow.AddYears(1);
                            newUserProvider.IsProvisioned = false;
                            newUserProvider.IsFree = true;
                            newUserProvider.IsPayed = false;
                            newUserProvider.IsFullSyncDone = false;
                            newUserProvider.Source = 0;
                            newUserProvider.PurchaseId = "";
                            newUserProvider.PurchaseState = 0;
                            newUserProvider.ProductId = "";
                            newUserProvider.CreatedDate = DateTime.UtcNow;
                            newUserProvider.ModifiedDate = DateTime.UtcNow;
                            newUserProvider.IsRelogginRequired = false;
                            newUserProvider.Priority = 100;
                            int trialDays = 30;
                            newUserProvider.IsTrial = false;
                            _wepdb.UserProviders.Add(newUserProvider);
                            _wepdb.SaveChanges();

                            foreach (var identifier in identifierList)
                            {
                                try
                                {
                                    if (identifier.Value != null && identifier.Value != "")
                                    {

                                        var upIdentifiers = new UserProviderIdentifier();
                                        upIdentifiers.Id = Guid.NewGuid();
                                        upIdentifiers.IdentifierId = identifier.Id;
                                        upIdentifiers.UserProviderId = newUserProvider.Id;
                                        upIdentifiers.Value = identifier.Value;
                                        upIdentifiers.IsActive = true;
                                        upIdentifiers.CreatedDate = DateTime.UtcNow;
                                        upIdentifiers.ModifiedDate = DateTime.UtcNow;
                                        _wepdb.UserProviderIdentifiers.Add(upIdentifiers);
                                        _wepdb.SaveChanges();
                                    }
                                }
                                catch (Exception ex)
                                {
                                    continue;
                                    // throw;
                                }
                            }
                        }
                        else
                        {
                            var existingUP = _wepdb.UserProviders.First(w => w.UserId == user.Id && w.ProviderId == BotProviderId && w.IsActive == true);
                            var existingUPI2 = _wepdb.UserProviderIdentifiers.Where(w => w.UserProviderId == existingUP.Id).ToList();
                            if (existingUPI2.Count > 0)
                            {
                                foreach (var identifier in identifierList)
                                {
                                    try
                                    {
                                        if (identifier.Value != null && identifier.Value != "")
                                        {
                                            var isExistingValue = existingUPI2.Any(w => w.IdentifierId == identifier.Id);
                                            if (isExistingValue)
                                            {
                                                var existingValue = existingUPI2.First(w => w.IdentifierId == identifier.Id);
                                                existingValue.Value = identifier.Value;
                                                existingValue.ModifiedDate = DateTime.UtcNow;
                                                _wepdb.SaveChanges();
                                            }
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        continue;
                                        // throw;
                                    }
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    TelegramService.SendMessage("Error in bot code:" + ex.StackTrace.ToString());
                }
            }
            catch (Exception ex)
            {
                TelegramService.SendMessage("Error in bot code:" + ex.StackTrace.ToString());
            }
            //Echo back what the user said
            //await turnContext.SendActivityAsync(MessageFactory.Text($""), cancellationToken);
        }
    }
}
