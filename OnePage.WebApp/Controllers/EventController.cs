#nullable enable
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using OnePage.Models.Models.AdminModel;
using OnePage.Models.Models.DataModel;
using OnePage.Models.Models.OrgModel;
using OnePage.Models.Models.PeopleModel;
using OnePage.Models.Models.TokenModel;
using System.Threading.Tasks;
using OnePage.Services;
using Contact = OnePage.Models.Models.Contact;
using OnePage.Models.Cosmos;
using OnePage.Models.Models;
using Newtonsoft.Json;
using OnePage.Common;
using OnePage.Models;
using static OnePage.Models.Models.PropertyModels.DataModels;
using ChargeBee.Models;
using Microsoft.AspNetCore.Cors;
using OnePage.Services.Interfaces;
using Microsoft.ApplicationInsights.DataContracts;
using OnePage.WebApp.TelemetryServices;
using UserProvider = OnePage.Models.Models.PeopleModel.UserProvider;
using Microsoft.EntityFrameworkCore;
using static OnePage.Common.CommonData;
using System.Threading;
using Newtonsoft.Json.Linq;
using System.Diagnostics;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Microsoft.Graph;
using Microsoft.IdentityModel.Tokens;
using OnePage.Models.Models.ENTEventModel;
using CalendarModel = OnePage.Models.Models.CalendarModel;
using EventAttendee = OnePage.Models.Models.EventAttendee;
using EventModel = OnePage.Models.Models.EventModel;
using EventRDSContext = OnePage.Models.Models.EventRDSContext;
using TokenContext = OnePage.Models.Models.TokenModel.TokenContext;
using System.Configuration;
using Amazon.Runtime;
using MongoDB.Bson.IO;
using static OnePage.Services.EventTranscriptService;
using OnePage.Models.Models.CustomModels;
using static OnePage.WebApp.Controllers.EventController;
using Humanizer;
using OnePage.Models.Models.ContactModel;

namespace OnePage.WebApp.Controllers
{
    //[EnableCors("MyAllowSpecificOrigins")]
    public class EventController : Controller
    {
        private readonly PeopleContext wepdb;

        private readonly DataContext weddb;

        // private readonly AdminContext weadb;
        private readonly Models.Models.TokenModel.TokenContext wetdb;
        private readonly OrgContext weodb;
        private readonly ContactContext db;
        private readonly EventRDSContext weedb;
        RestClient restClient = new();

        private readonly CalendarService calendarService;
        private readonly ProviderService providerService;
        private readonly RequestResearchService _requestResearchService;
        private readonly EventTranscriptService transcriptService;
        MetricsService metricsService = new();
        private readonly BotService botService;
        // private readonly ResearchService researchService;

        private readonly IRedisCaching _redisCaching;
        private readonly ICustomCacheService _customCacheService;
        private readonly OnePage.WebApp.TelemetryServices.ITelemetryTrack telemetryTracker;
        private readonly IConfiguration _configuration;
        private readonly UserProviderCaching _userProviderCaching;
        private readonly AuthService _authService;

        public EventController(IRedisCaching redisCaching, ICustomCacheService customCacheService,
            OnePage.WebApp.TelemetryServices.ITelemetryTrack tracker, PeopleContext wepdb, DataContext weddb,
            TokenContext wetdb, OrgContext weodb, ContactContext db, EventRDSContext weedb, BotService botService,
            IConfiguration configuration, CalendarService calendarService, ProviderService providerService, RequestResearchService requestResearchService, EventTranscriptService transcriptService, UserProviderCaching userProviderCaching, AuthService authService)
        {
            _redisCaching = redisCaching;
            telemetryTracker = tracker;
            this.wepdb = wepdb;
            this.weddb = weddb;
            // this.weadb = weadb;
            this.wetdb = wetdb;
            this.weodb = weodb;
            this.db = db;
            this.weedb = weedb;
            _configuration = configuration;
            this.calendarService = calendarService;
            this.providerService = providerService;
            this.transcriptService = transcriptService;
            // this.researchService = researchService;
            _userProviderCaching = userProviderCaching;
            _requestResearchService = requestResearchService;
            this.botService = botService;
            _authService = authService;
            // providerService = new(redisCaching);
            // calendarService = new(redisCaching, customCacheService);
            _customCacheService = customCacheService;
            // transcriptService = new(redisCaching);
            //researchService = new(redisCaching);
        }

        [HttpPost]
        [Route("C4B2AD1B")]
        public async Task<IActionResult> EventContactandsignal()

        {
            Guid userId = Guid.Empty;
            try
            {
                userId = _authService.GetUser(Request);
                var token = _authService.GetToken(Request);
                if (userId == Guid.Empty)
                {
                    return Unauthorized("Wrong Token");
                }

                var cacheKeyEventData = RedisKeyManager.GetUserEventsDataKey(userId);

                var EventsList = await _redisCaching.GetListAsync<EventModel>(cacheKeyEventData);
                /*EventsList = await weedb.EventModels.Where(x => x.Id == Guid.Parse("cfc87edd-04eb-4b8b-a0be-d3e9989e28c0")).ToListAsync()*/
                ;


                await calendarService.ScheduleForWeb(userId, EventsList);

                return Ok("success");
            }
            catch (Exception ex)
            {
                Models.Models.LogModel logModel = new Models.Models.LogModel()
                {
                    ErrorMessage = ex.Message,
                    Id = userId.ToString(),
                    //InputUsed = jsonKey,
                    Source = "webApp",
                    StackTrace = ex.StackTrace
                };

                await SESService.SendErrorMailsWebApp(logModel);
                return BadRequest(ex.Message);
            }
        }

        [HttpPost]
        [Route("DD0BB1B3")]
        public async Task<IActionResult> CRMContactandsignal()
        {
            Guid userId = Guid.Empty;
            try
            {
                userId = _authService.GetUser(Request);
                var token = _authService.GetToken(Request);
                if (userId == Guid.Empty)
                {
                    return Unauthorized("Wrong Token");
                }
                botService.SendMessageForTestBot("Crm contact and signal trigered for user: " + userId);
                var cacheKeyEventData = RedisKeyManager.GetUserEventsDataKey(userId);
                var EventsList =
                 await _redisCaching.GetListAsync<EventModel>(cacheKeyEventData);
                //var EventsList = _redisCaching.GetData<List<EventModel>>("eventsForPush" + userId);

                DateTime CurrentTime = DateTime.UtcNow;
                DateTime unixEpochStart = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
                long epochTime = (long)(CurrentTime - unixEpochStart).TotalSeconds;
                EventsList = EventsList.Where(w => w.LEnd > epochTime).ToList();

                await calendarService.ScheduleForCRMLog(userId, EventsList);
                return Ok("success");
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }


        [HttpPost]
        [Route("47EB1EB0")]
        public async Task<IActionResult> TestFireflies(Guid userId, string emailId)
        {
            try
            {
                await calendarService.GetFirefliesTranscriptions(userId, emailId);
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.ToString());
            }
        }


        [HttpPost]
        [Route("C4DDA4C5")]
        public async Task<IActionResult> ProcessEventWithCardIds(bool fetchOnline = false)
        {
            var userId = _authService.GetUser(Request);
            try
            {


                var userEmail = await wepdb.Users.AsNoTracking().Where(x => x.Id == userId).Select(x => x.Email).AsNoTracking().FirstOrDefaultAsync();
                var eventDb = new DbContextOptionsBuilder<EventRDSContext>();
                eventDb.UseSqlServer(
                    _configuration["DBConnections:WEEventRDSConnectionString"]);
                using (var evdb = new EventRDSContext(eventDb.Options))
                {
                    var EmailSplit = userEmail.Split('@');
                    var domain = EmailSplit[1].ToLower();
                    var isFreeDomain = await weddb.FreeDomains.AsNoTracking().AnyAsync(w => w.DomainName.ToLower() == domain.ToLower());
                    if (fetchOnline)
                    {
                        var profileEmails = await _redisCaching.GetListAsync<string>("ProfileEmailsList" + userId);

                        var profileId = await evdb.Contacts.AsNoTracking().Where(w => w.UserId == userId && w.OtherUserId == userId)
                            .Select(x => x.Id).FirstOrDefaultAsync();
                        var profileDisplay = await evdb.Contacts.AsNoTracking().Where(w => w.UserId == userId && w.OtherUserId == userId)
                            .Select(x => x.EmailDisplay)
                            .FirstOrDefaultAsync();
                        if (string.IsNullOrEmpty(profileDisplay))
                        {
                            profileDisplay = await evdb.ContactEmails.AsNoTracking().Where(x => x.ContactId == profileId && x.Email.ToLower() == userEmail.ToLower()).Select(x => x.Email).FirstOrDefaultAsync();
                        }

                        string newEventsCacheKey = RedisKeyManager.GetNewEventsKey(userId.ToString());
                        var EventsList = await _redisCaching.GetListAsync<EventModel>(newEventsCacheKey);
                        //var EventsList = weedb.EventModels.Where(x => x.Id == Guid.Parse("7e5ebf84-48aa-40fc-b0f7-db28af38e4ce")).ToList();
                        TrackMetadata trackMetadata1 = new TrackMetadata();
                        trackMetadata1.user_id = " Events List";
                        if (EventsList != null)
                            trackMetadata1.numberOfEvents = EventsList.Count.ToString();
                        else
                            trackMetadata1.numberOfEvents = "0";
                        var deviceId = await wepdb.UserDevices.AsNoTracking().Where(x => x.UserId == userId && x.IsActive == true)
                        .Select(x => x.Id).FirstOrDefaultAsync();
                        trackMetadata1.deviceId = deviceId.ToString();
                        var deviceTypeId = await wepdb.UserDevices.AsNoTracking().Where(x => x.UserId == userId && x.IsActive == true)
                            .Select(x => x.DeviceTypeId).FirstOrDefaultAsync();
                        trackMetadata1.deviceName = await weddb.DeviceTypes.AsNoTracking().Where(x => x.Id == deviceTypeId)
                            .Select(x => x.Name).FirstOrDefaultAsync();

                        int i = 0;
                        Stopwatch s = new Stopwatch();
                        s.Start();
                        ThreadPool.SetMinThreads(Environment.ProcessorCount, Environment.ProcessorCount);
                        if (EventsList != null)
                        {
                            int mid = EventsList.Count / 2;
                            List<EventModel> FirstHalfEvent = new List<EventModel>();
                            List<EventModel> SecondHalf = new List<EventModel>();
                            List<System.Action> actions = new List<System.Action>();
                            if (EventsList.Count == 1)
                            {
                                FirstHalfEvent.Add(EventsList.First());
                            }
                            else
                            {
                                FirstHalfEvent = EventsList.Take(mid).ToList();
                                SecondHalf = EventsList.Skip(mid).ToList();
                            }

                            List<Task> tasks = new List<Task>();
                            await calendarService.FirstHalfEvent2(userId, SecondHalf, profileEmails, profileId,
                                  profileDisplay);
                            await calendarService.FirstHalfEvent(userId, FirstHalfEvent, profileEmails, profileId,
                                    profileDisplay);

                            //i++;
                            //actions.Add(async () =>
                            //{
                            //    await calendarService.FirstHalfEvent(userId, FirstHalfEvent, profileEmails, profileId,
                            //        profileDisplay);
                            //    await calendarService.FirstHalfEvent2(userId, SecondHalf, profileEmails, profileId,
                            //        profileDisplay);
                            //});

                            // Execute actions in parallel
                            //Parallel.ForEach(actions, action => { action.Invoke(); });
                        }
                    }
                    var userDetail = await wepdb.Users.Where(x => x.Id == userId).AsNoTracking().FirstOrDefaultAsync();
                    var timezoneSetting = wepdb.UserSettings.AsNoTracking()
                        .FirstAsync(x => x.SettingId == CommonData.TimeZoneSettingId && x.UserId == userId).Result.Value;
                    int hourToAdd = 0;
                    int minutesToAdd = 0;

                    if (!string.IsNullOrEmpty(timezoneSetting))
                    {
                        var split = timezoneSetting.Split(":");
                        hourToAdd = int.Parse(split[0]);
                        minutesToAdd = int.Parse(split[1]);
                    }

                    TimeSpan timespan = new TimeSpan(hourToAdd, minutesToAdd, 0);
                    DateTimeOffset utcNow = DateTime.UtcNow.Date;
                    DateTimeOffset today = utcNow.ToOffset(timespan);

                    DateTimeOffset tomorrow = today.AddHours(24);
                    DateTimeOffset yesterday = today.AddHours(-24);
                    long yLong = yesterday.ToUnixTimeSeconds();
                    DateTimeOffset minusOne = today.AddMinutes(-1);
                    long mLong = minusOne.ToUnixTimeSeconds();
                    long tomorrowSecs = tomorrow.AddHours(24).ToUnixTimeSeconds();
                    List<EventModel> events = new List<EventModel>();
                    var activeUserProviderList = await wepdb.UserProviders.AsNoTracking().Where(x => x.UserId == userId && x.IsActive == true).Select(x => x.Id).ToListAsync();
                    Stopwatch stopwatch = new Stopwatch();
                    stopwatch.Start();
                    events = await evdb.EventModels.AsNoTracking().Where(w =>
                        w.UserId == userId && w.IsActive == true && w.BTwo == true && w.LEnd > yLong &&
                        w.LStart < tomorrowSecs).ToListAsync();
                    events = (from e in events
                              join ap in activeUserProviderList on e.UserProviderId equals
                             ap
                              select e).ToList();
                    TelegramService.SendMessageToTestBot("Total Time to Events From table=" + stopwatch.ElapsedMilliseconds);
                    stopwatch.Stop();
                    //}

                    TrackMetadata trackMetadata = new TrackMetadata();
                    try
                    {

                        trackMetadata.user_id = userId.ToString();
                        trackMetadata.action = "Event";
                        var userDevice = await wepdb.UserDevices.Where(x => x.UserId == userId && x.IsActive == true)
                            .FirstOrDefaultAsync();
                        trackMetadata.numberOfEvents = events.Count.ToString();
                        trackMetadata.deviceId = userDevice.Id.ToString();
                        trackMetadata.deviceTypeId = userDevice.DeviceTypeId.ToString();
                        trackMetadata.deviceName = weddb.DeviceTypes.Where(x => x.Id == userDevice.DeviceTypeId)
                            .Select(x => x.Name).FirstOrDefault();
                        trackMetadata.signInTime = DateTime.Now.ToString();
                        trackMetadata.loginWebsite = "WebApp";
                        trackMetadata.isFreeDomain = isFreeDomain.ToString();

                        if (userDetail.IsTestUser != null)
                        {
                            trackMetadata.isTestUser = userDetail.IsTestUser.ToString();
                        }
                        metricsService.TrackUsuage(trackMetadata, "events");
                    }
                    catch (Exception ex)
                    {
                        Models.Models.LogModel logModel = new Models.Models.LogModel()
                        {
                            ErrorMessage = ex.Message,
                            Id = userId.ToString(),
                            //InputUsed = jsonKey,
                            Source = "Web App",
                            StackTrace = ex.StackTrace
                        };

                        await SESService.SendErrorMailsWebApp(logModel);

                    }

                    foreach (var events1 in events)
                    {
                        events.FirstOrDefault(x => x.EventId == events1.EventId).EventAttendees = await evdb.EventAttendees.AsNoTracking()
                            .Where(x => x.EventId == events1.EventId && x.UserId == userId).ToListAsync();
                    }


                    if (events != null)
                    {
                        telemetryTracker.TrackTrace("events Received" + userId.ToString(), SeverityLevel.Information);
                    }
                    var homePageCards = await weddb.Cards.AsNoTracking().Where(w => w.IsActive == true && w.PageType == 1)
                        .Select(w => w.Id).ToListAsync();
                    WebAppEventModel returnModel = new();
                    Stopwatch stopwatch1 = new Stopwatch();
                    stopwatch1.Start();
                    //Stopwatch stopwatch = new Stopwatch();
                    stopwatch.Start();
                    var task1 = GetEvents(false, events, userId, false);
                    TelegramService.SendMessageToTestBot("Total Time to Events For today=" + stopwatch.ElapsedMilliseconds);
                    stopwatch.Stop();
                    stopwatch.Start();
                    var task2 = GetEvents(true, events, userId, false);
                    TelegramService.SendMessageToTestBot("Total Time to Events For now=" + stopwatch.ElapsedMilliseconds);
                    stopwatch.Stop();
                    stopwatch.Start();
                    var task3 = GetEvents(false, events, userId, true);
                    TelegramService.SendMessageToTestBot("Total Time to Events For tmrw=" + stopwatch.ElapsedMilliseconds);
                    stopwatch.Stop();

                    await Task.WhenAll(task1, task2, task3);

                    var todayEvents = await task1;
                    var nowEvents = await task2;
                    var tomorrowEvents = await task3;
                    //  var todayEvents = await GetEvents(false, events, userId, false);

                    //    var nowEvents = await GetEvents(true, events, userId, false);
                    //    var tomorrowEvents = await GetEvents(false, events, userId, true);
                    TelegramService.SendMessageToTestBot("Total Time to process Event of all three today tmrw=" + stopwatch1.ElapsedMilliseconds);
                    var mLong2 = DateTimeOffset.UtcNow.ToOffset(timespan).AddMinutes(-1).ToUnixTimeSeconds();
                    var yEvents = events
                        .Where(w => w.IsActive == true && w.BTwo == true && w.LStart > yLong && w.LEnd <= mLong2).ToList();
                    List<EventModel> yestEvent = new List<EventModel>();
                    foreach (var yItem in yEvents)
                    {
                        var isCalendarExists =
                         await evdb.CalendarModels.AnyAsync(x => x.IsSelected == true && x.RefId == yItem.CalId);
                        if (isCalendarExists)
                        {
                            yestEvent.Add(yItem);
                        }
                    }
                    List<OnePage.Models.Models.EventModel> reminderList = new List<OnePage.Models.Models.EventModel>();
                    if (yestEvent != null)
                    {
                        TelegramService.SendMessageToTestBot($"yesterday event count: " + yestEvent.ToList().Count());
                        reminderList = yestEvent.ToList();
                    }
                    else
                    {
                        reminderList = new List<EventModel>();
                    }
                    if (todayEvents == null)
                        todayEvents = new List<EventModel>();
                    if (nowEvents == null)
                        nowEvents = new List<EventModel>();
                    if (tomorrowEvents == null)
                        tomorrowEvents = new List<EventModel>();
                    try
                    {

                        returnModel.TodayList = todayEvents.ToList();
                        TelegramService.SendMessageToTestBot($"today event count: " + todayEvents.ToList().Count);
                        telemetryTracker.TrackTrace("Process Now  Event Started= " + userId.ToString(),
                            SeverityLevel.Information);
                        int nowCount = 0;
                        returnModel.NowList = nowEvents.ToList();
                        TelegramService.SendMessageToTestBot($"nowEvents  count: " + nowEvents.ToList().Count);
                        stopwatch.Start();
                        foreach (var titem in returnModel.TodayList.ToList())
                        {
                            nowCount++;
                            telemetryTracker.TrackTrace("Process Now  Event Count= " + nowCount.ToString() + " " + userId.ToString(), SeverityLevel.Information);

                            if (!titem.Desc.IsNullOrEmpty())
                            {
                                if (titem.Desc.Contains("\n"))
                                {
                                    var splitValue = titem.Desc.Split('\n');
                                    var str1 = " ";
                                    var str2 = " ";

                                    foreach (var item in splitValue)
                                    {
                                        if (item.Contains("meet.google.com") || item.Contains("zoom.us") ||
                                            item.Contains("teams.microsoft.com") || item.Contains(".....") ||
                                            item.Contains("join.skype.com") || item.Contains("calendly.com"))
                                        {
                                            continue;
                                        }
                                        else
                                        {
                                            str1 = item + "\n";
                                            str2 = str2 + str1;
                                        }
                                    }

                                    titem.Desc = str2;
                                }
                            }
                        }

                    }
                    catch (Exception ex)
                    {
                        TelegramService.SendMessageToTestBot("user: " + userId.ToString() + "....." + ex.StackTrace);
                        Models.Models.LogModel logModel = new Models.Models.LogModel()
                        {
                            ErrorMessage = ex.Message,
                            Id = userId.ToString(),
                            //InputUsed = jsonKey,
                            Source = "Web App",
                            StackTrace = ex.StackTrace
                        };

                        await SESService.SendErrorMailsWebApp(logModel);
                    }
                    TelegramService.SendMessageToTestBot("Total Time to Events From summary=" + stopwatch.ElapsedMilliseconds);
                    stopwatch.Stop();
                    telemetryTracker.TrackTrace("Process Now  Event Completed= " + " " + userId.ToString(),
                        SeverityLevel.Information);
                    var reminderList2 = reminderList.Where(w => w.Notes == "" || w.Notes == null).ToList();
                    stopwatch.Start();

                    if (reminderList2.Count > 0)
                    {
                        foreach (var item in reminderList2)
                        {
                            var attendees = await evdb.EventAttendees.AsNoTracking().Where(x =>
                                x.UserId == userId && x.EventId == item.EventId && (x.BOne == true || x.BOne == null) &&
                                (x.IsUser == false || x.IsUser == null) && x.UserId == userId).ToListAsync();
                            item.EventAttendees = attendees;
                        }
                    }

                    returnModel.ReminderList = reminderList2;

                    TelegramService.SendMessageToTestBot("Total Time to reminder attenddess" + stopwatch.ElapsedMilliseconds);
                    stopwatch.Stop();
                    returnModel.TomorrowList = tomorrowEvents;
                    telemetryTracker.TrackTrace("Process Today  Event Started=  " + userId.ToString(),
                        SeverityLevel.Information);
                    int todayEventCount = 0;
                    var totalAttendes = new List<AttendeesModel>();

                    //  var userDetails = await wepdb.Users.AsNoTracking().Where(x => x.Id == userId).FirstOrDefaultAsync();
                    var slice = userDetail.Email.Split('@');

                    var orgUser = await weodb.OrgUsers.AsNoTracking().Where(w =>
                            w.UserId == userId && w.Org.Name.ToLower() == slice[1].ToLower() && w.IsActive == true)
                        .Include(x => x.Org).ToListAsync();
                    var orgId = orgUser
                        .Where(x => x.Org.AppId.ToLower() == userDetail.AppId.ToString().ToLower() &&
                                    x.Org.IsActive == true).Select(x => x.Org.Id).FirstOrDefault();
                    var contactContext = new DbContextOptionsBuilder<EventRDSContext>();
                    contactContext.UseSqlServer(_configuration["DBConnections:WEEventRDSConnectionString"]);
                    var contsbContext = new DbContextOptionsBuilder<ContactContext>();
                    contsbContext.UseSqlServer(_configuration["DBConnections:WEContactConnectionString"]);
                    stopwatch.Start();
                    using (var db1 = new ContactContext(contsbContext.Options))
                    {
                        if (todayEvents.Count > 0)
                        {
                            foreach (var eventItem in todayEvents)
                            {
                                int tempCount = 0;
                                bool isPresent = false;
                                var attendeeList = new List<AttendeesModel>();
                                todayEventCount++;

                                telemetryTracker.TrackTrace(
                                    "Process Today  Event Count= " + todayEventCount.ToString() + " " +
                                    userId.ToString(), SeverityLevel.Information);
                                if (eventItem.EventAttendees.Count > 0)
                                {
                                    foreach (var attItem in eventItem.EventAttendees)
                                    {
                                        var researchExists = false;
                                        var contactDataExists = await evdb.Contacts.AsNoTracking().AnyAsync(w => w.Id == attItem.ContactId);
                                        if (contactDataExists)
                                        {
                                            var contactData = await evdb.Contacts.AsNoTracking().Where(w => w.Id == attItem.ContactId)
                                                .FirstOrDefaultAsync();
                                            var contactSocials = await evdb.ContactSocials.AsNoTracking().Where(w => w.ContactId == attItem.ContactId).ToListAsync();

                                            if (contactSocials.Count > 0)
                                            {
                                                researchExists = contactSocials.Any(w =>
                                                    w.Url.ToLower().Contains("twitter") ||
                                                    w.Url.ToLower().Contains("linkedin"));
                                            }

                                            AttendeesModel attModel = new();
                                            attModel.Address = attItem.Address;
                                            attModel.ContactId = (Guid)attItem.ContactId;
                                            if (attItem.Created != null)
                                                attModel.Created = (DateTimeOffset)attItem.Created;
                                            attModel.EventId = attItem.EventId;
                                            attModel.id = attItem.Id;
                                            if (attItem.BOne != null)
                                                attModel.IsActive = (bool)attItem.BOne;
                                            else
                                                attModel.IsActive = true;

                                            if (attItem.IsUser != null)
                                                attModel.IsUser = (bool)attItem.IsUser;
                                            attModel.Start = (DateTimeOffset)eventItem.Start;
                                            attModel.LStart = (long)eventItem.LStart;
                                            attModel.End = (DateTimeOffset)eventItem.End;
                                            attModel.LEnd = (long)eventItem.LEnd;
                                            if (attItem.Modified != null)
                                                attModel.Modified = (DateTimeOffset)attItem.Modified;
                                            attModel.Name = contactData.FirstName + " " + contactData.LastName;
                                            var stringReplace1 =
                                                contactData.EmailDisplay.Replace("|", "");
                                            Guid cId = (Guid)attItem.ContactId;
                                            var contactContextBuilder =
                                                new DbContextOptionsBuilder<ContactContext>();
                                            contactContextBuilder.UseSqlServer(
                                                _configuration["DBConnections:WEContactConnectionString"]);
                                            using (var dbdb = new ContactContext(contactContextBuilder.Options))
                                            {
                                                if (!isFreeDomain)
                                                {
                                                    var contactPersonExists = await dbdb.OrgPeople.AsNoTracking().AnyAsync(w =>
                                                        w.OrgId == orgId && w.EmailId.ToLower() == stringReplace1);
                                                    if (contactPersonExists)
                                                    {
                                                        var cp = await dbdb.OrgPeople.AsNoTracking().FirstOrDefaultAsync(w => w.OrgId == orgId && w.EmailId.ToLower() == stringReplace1);
                                                        attModel.Status = _requestResearchService.GetResearchStatus(cp);
                                                        attModel.profileImage = CommonData.S3BucketUrlForPic + cp.PersonId;
                                                    }
                                                    else
                                                    {
                                                        attModel.Status = 0;
                                                    }
                                                    attModel.UserId = (Guid)attItem.UserId;
                                                    attModel.role = contactData.Desig;
                                                    attModel.company = contactData.Company;
                                                    attendeeList.Add(attModel);
                                                    totalAttendes.Add(attModel);
                                                }
                                                else
                                                {
                                                    var contactPersonExists = await db1.ContactPeople.AsNoTracking().AnyAsync(w => w.ContactId == attItem.ContactId);
                                                    if (contactPersonExists)
                                                    {
                                                        var cp = await dbdb.ContactPeople.AsNoTracking().FirstOrDefaultAsync(w => w.ContactId == attItem.ContactId);
                                                        attModel.Status = _requestResearchService.GetResearchStatus(cp);
                                                        attModel.profileImage = CommonData.S3BucketUrlForPic + cp.PersonId;
                                                    }
                                                    else
                                                    {
                                                        attModel.Status = 0;
                                                    }
                                                    attModel.role = contactData.Desig;
                                                    attModel.company = contactData.Company;
                                                    attModel.UserId = (Guid)attItem.UserId;
                                                    attendeeList.Add(attModel);
                                                    totalAttendes.Add(attModel);
                                                }
                                            }
                                        }
                                    }
                                }
                                telemetryTracker.TrackTrace("Process Today  Event Completed " + " " + userId.ToString(),
                                    SeverityLevel.Information);
                                returnModel.AttendeesList = totalAttendes;
                            }
                        }

                        TelegramService.SendMessageToTestBot("Total Time to Events attendee for today" + stopwatch.ElapsedMilliseconds + "For total Attendess=" + returnModel.AttendeesList.Count);
                        stopwatch.Stop();
                        returnModel.ReminderList = returnModel.ReminderList.OrderBy(w => w.Start).ToList();
                        if (returnModel != null)
                        {
                            telemetryTracker.TrackTrace("returnModel is full" + userId.ToString(), SeverityLevel.Information);
                        }
                        List<WebAppWithCardIdsEventModel> returnWithCardIds = new List<WebAppWithCardIdsEventModel>();
                        foreach (var item in homePageCards)
                        {
                            WebAppWithCardIdsEventModel cardDataItem = new WebAppWithCardIdsEventModel();
                            switch (item.ToString().ToUpper())
                            {
                                case "A2A0CC8A-06A2-4A8C-B6E9-14B586CAAEA8": // People Today
                                    cardDataItem.CardId = item;
                                    cardDataItem.CardContent = returnModel.AttendeesList.DistinctBy(w => w.Address).ToList();
                                    returnWithCardIds.Add(cardDataItem);
                                    break;
                                case "B1B3FB2C-227F-447D-BCAF-7FE5095F9DCD": // Reminders
                                    cardDataItem.CardId = item;
                                    cardDataItem.CardContent = returnModel.ReminderList;
                                    returnWithCardIds.Add(cardDataItem);
                                    break;
                                case "E35F0620-C2EA-4373-98D0-8480498D8628": // Today
                                    cardDataItem.CardId = item;
                                    cardDataItem.CardContent = returnModel.TodayList;
                                    returnWithCardIds.Add(cardDataItem);
                                    break;
                                case "A2659706-289E-4830-9DD7-F0D1A9BBBDDB": // Now
                                    cardDataItem.CardId = item;
                                    cardDataItem.CardContent = returnModel.NowList;
                                    returnWithCardIds.Add(cardDataItem);
                                    break;
                                case "E35F0620-C2EA-4373-98D0-8480498D8629": // Tomorrow
                                    cardDataItem.CardId = item;
                                    cardDataItem.CardContent = returnModel.TomorrowList;
                                    returnWithCardIds.Add(cardDataItem);
                                    break;
                                default:
                                    break;
                            }
                        }

                        trackMetadata.numberOfAttendees = returnModel.AttendeesList.Count.ToString();
                        trackMetadata.loginWebsite = "WebApp";
                        metricsService.TrackUsuage(trackMetadata, "events");
                        return Ok(returnWithCardIds);
                    }
                }
            }
            catch (Exception ex)
            {
                Models.Models.LogModel logModel = new Models.Models.LogModel()
                {
                    ErrorMessage = ex.Message,
                    Id = userId.ToString(),
                    //InputUsed = jsonKey,
                    Source = "Web App",
                    StackTrace = ex.StackTrace
                };

                await SESService.SendErrorMailsWebApp(logModel);
                string errormessage = ex.StackTrace.ToString() + " " + ex.Message;
                return BadRequest(errormessage);
            }
        }

        [HttpPost]
        [Route("6B084C19")]

        public async Task<IActionResult> SendSQSForEventAttendee()
        {
            var userId = _authService.GetUser(Request);
            var token = _authService.GetToken(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized("Wrong Token");
            }
            var EventAttendeeToSendForSqsFrom1 = await _redisCaching.GetListAsync<EventAttendee>("SQSForAttendee1" + userId);
            var EventAttendeeToSendForSqsFrom2 = await _redisCaching.GetListAsync<EventAttendee>("SQSForAttendee2" + userId);
            EventAttendeeToSendForSqsFrom1 = EventAttendeeToSendForSqsFrom1.Concat(EventAttendeeToSendForSqsFrom2).ToList();
            foreach (var attendee in EventAttendeeToSendForSqsFrom1)
            {
                await calendarService.SendSqsSet(attendee);
            }
            return Ok();

        }
        [HttpPost]
        [Route("33292B9A")]
        public async Task<IActionResult> FetchEventsOnline()
        {
            Guid userId = Guid.Empty;
            try
            {
                Dictionary<string, string> properties = new Dictionary<string, string>();
                Response.Headers.Add("X-Frame-Options", "DENY");
                Response.Headers.Add("X-Content-Type-Options", "nosniff");
                Response.Headers.Add("Content-Security-Policy", "default-src 'self'");
                Response.Headers.Add("Referrer-Policy", "no-referrer");
                // Response.Headers.Add("Strict-Transport-Security", "max-age=31536000; includeSubDomains");
                userId = _authService.GetUser(Request);
                var token = _authService.GetToken(Request);
                var webhookToken = await wetdb.Tokens.AsNoTracking().Where(x => x.UserId == userId && x.IsActive == true && x.Details.ToLower() == "webhook").FirstOrDefaultAsync();
                if (userId == Guid.Empty)
                {
                    return Unauthorized("Wrong Token");
                }
                await calendarService.ProcessEventsNew(userId, token, webhookToken.Id.ToString(), true, true);


                return Ok();

            }
            catch (Exception ex)
            {
                Models.Models.LogModel logModel = new Models.Models.LogModel()
                {
                    ErrorMessage = ex.Message,
                    Id = userId.ToString(),
                    //InputUsed = jsonKey,
                    Source = "Web App",
                    StackTrace = ex.StackTrace
                };

                await SESService.SendErrorMailsWebApp(logModel);
                telemetryTracker.TrackException(ex, userId);
                return BadRequest("Error Occured " + ex.Message + " stack trace: " + ex.StackTrace.ToString());
            }
        }
        [HttpPost]
        [Route("2E361E36")]

        public async Task<IActionResult> UnsubscribeNotificationForUserProviders([FromQuery] string userProviderId)
        {
            try
            {
                var userId = await wepdb.UserProviders.AsNoTracking().Where(x => x.Id == Guid.Parse(userProviderId)).Select(x => x.UserId).FirstOrDefaultAsync();
                var userDetail = await wepdb.Users.Where(x => x.Id == userId).AsNoTracking().FirstOrDefaultAsync();
                var timezoneSetting = wepdb.UserSettings.AsNoTracking()
                    .FirstAsync(x => x.SettingId == CommonData.TimeZoneSettingId && x.UserId == userId).Result.Value;
                int hourToAdd = 0;
                int minutesToAdd = 0;
                var orgDetail = weodb.Orgs.Where(x => x.Id == CommonData.WhatElseCustomerOrgId).FirstOrDefault();
                string appId = orgDetail.WebOneSignalId;
                string appKey = orgDetail.WebOneSignalKey;

                if (!string.IsNullOrEmpty(timezoneSetting))
                {
                    var split = timezoneSetting.Split(":");
                    hourToAdd = int.Parse(split[0]);
                    minutesToAdd = int.Parse(split[1]);
                }
                TimeSpan timespan = new TimeSpan(hourToAdd, minutesToAdd, 0);
                DateTimeOffset utcNow = DateTime.UtcNow;
                DateTimeOffset today = utcNow.ToOffset(timespan);

                long tLong = today.ToUnixTimeSeconds();
                var events = await weedb.EventModels.AsNoTracking().Where(w =>
                        w.UserId == userId && w.UserProviderId == Guid.Parse(userProviderId) && w.LEnd > tLong).Select(w => new CustomEventModel
                        {
                            Id = w.Id,
                            ProviderId = w.ProviderId,
                            UserId = (Guid)w.UserId,
                            UserProviderId = w.UserProviderId,
                            OneSignalNotificationIdList = w.OneSignalNotificationIdList

                        }).ToListAsync();


                bool teamUserDeviceExists = await wepdb.UserDevices
                      .AnyAsync(w => w.UserId == userId && w.DeviceTypeId == CommonData.TeamsDeviceTypeId && w.IsActive == true);
                var url = CommonData.UnsubscribeOneSignalApi;
                try
                {
                    foreach (var e in events)
                    {
                        if (!string.IsNullOrEmpty(e.OneSignalNotificationIdList))
                        {
                            var notifiactionIdList = e.OneSignalNotificationIdList.Trim().Split(',');
                            foreach (var id in notifiactionIdList)
                            {
                                if (!string.IsNullOrEmpty(id))
                                {
                                    url = url.Replace("{notifcationId}", id).Replace("{appId}", appId);
                                    var res = await restClient.DeleteAsync<string>(url: url, isOneSignal: true, oneSignalKey: appKey);
                                }
                            }
                        }
                        if (teamUserDeviceExists)
                        {
                            var signalList = await wepdb.Signals.Where(x => x.SignalTypeId == Guid.Parse("A56AD9EF-98AE-449D-8329-EE30787D8E06") && x.UserId == userId && x.IsActive == true && x.AdditionalData.ToLower().Contains(e.Id.ToString().ToLower())).ToListAsync();
                            foreach (var signal in signalList)
                            {
                                signal.IsActive = false;
                                await wepdb.SaveChangesAsync();

                            }

                        }
                    }
                }
                catch (Exception ex)
                {
                    return BadRequest(ex);
                }




            }
            catch (Exception ex)
            {
                return BadRequest(ex);

            }
            return Ok();
        }





        [HttpPost]
        [Route("BE76C7E1")]

        public async Task<IActionResult> UnsubscribeConnectedToCalendarEvents([FromQuery] Guid userProvider)
        {
            try
            {
                var userId = await wepdb.UserProviders.AsNoTracking().Where(x => x.Id == userProvider).Select(x => x.UserId).FirstOrDefaultAsync();
                var userDetail = await wepdb.Users.Where(x => x.Id == userId).AsNoTracking().FirstOrDefaultAsync();
                var timezoneSetting = wepdb.UserSettings.AsNoTracking()
                    .FirstAsync(x => x.SettingId == CommonData.TimeZoneSettingId && x.UserId == userId).Result.Value;
                int hourToAdd = 0;
                int minutesToAdd = 0;

                if (!string.IsNullOrEmpty(timezoneSetting))
                {
                    var split = timezoneSetting.Split(":");
                    hourToAdd = int.Parse(split[0]);
                    minutesToAdd = int.Parse(split[1]);
                }

                TimeSpan timespan = new TimeSpan(hourToAdd, minutesToAdd, 0);
                DateTimeOffset utcNow = DateTime.UtcNow.Date;
                DateTimeOffset today = utcNow.ToOffset(timespan);
                var startTimeToFilter = today.ToUnixTimeSeconds();

                var eventConnectedToCalendar = await weedb.EventModels.Where(x => x.UserProviderId == userProvider && x.IsActive == true && x.LStart > startTimeToFilter).ToListAsync();
                if (eventConnectedToCalendar.Count > 0)
                {
                    foreach (var eve in eventConnectedToCalendar)
                    {
                        eve.IsActive = false;
                        await weedb.SaveChangesAsync();

                    }
                }
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest();
            }
        }
        [HttpPost]
        [Route("CC14F05B")]
        public async Task<IActionResult> ProcessEventWithCardIdsNew(bool fetchOnline = false)
        {
            var userId = _authService.GetUser(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized("Wrong Token");
            }
            try
            {
                var userEmail = await wepdb.Users.AsNoTracking().Where(x => x.Id == userId).Select(x => x.Email).AsNoTracking().FirstOrDefaultAsync();
                var eventDb = new DbContextOptionsBuilder<EventRDSContext>();
                eventDb.UseSqlServer(
                    _configuration["DBConnections:WEEventRDSConnectionString"]);
                using (var evdb = new EventRDSContext(eventDb.Options))
                {
                    var EmailSplit = userEmail.Split('@');
                    var domain = EmailSplit[1].ToLower();
                    var isFreeDomain = await weddb.FreeDomains.AsNoTracking().AnyAsync(w => w.DomainName.ToLower() == domain.ToLower());
                    if (fetchOnline)
                    {
                        var profileEmails = await _redisCaching.GetListAsync<string>("ProfileEmailsList" + userId);

                        var profileId = await evdb.Contacts.AsNoTracking().Where(w => w.UserId == userId && w.OtherUserId == userId)
                            .Select(x => x.Id).FirstOrDefaultAsync();
                        var profileDisplay = await evdb.Contacts.AsNoTracking().Where(w => w.UserId == userId && w.OtherUserId == userId)
                            .Select(x => x.EmailDisplay)
                            .FirstOrDefaultAsync();
                        if (string.IsNullOrEmpty(profileDisplay))
                        {
                            profileDisplay = await evdb.ContactEmails.AsNoTracking().Where(x => x.ContactId == profileId && x.Email.ToLower() == userEmail.ToLower()).Select(x => x.Email).FirstOrDefaultAsync();
                        }

                        string newEventsCacheKey = RedisKeyManager.GetNewEventsKey(userId.ToString());
                        var EventsList = await _redisCaching.GetListAsync<EventModel>(newEventsCacheKey);
                        //var EventsList = weedb.EventModels.Where(x => x.Id == Guid.Parse("7e5ebf84-48aa-40fc-b0f7-db28af38e4ce")).ToList();
                        TrackMetadata trackMetadata1 = new TrackMetadata();
                        trackMetadata1.user_id = " Events List";
                        if (EventsList != null)
                            trackMetadata1.numberOfEvents = EventsList.Count.ToString();
                        else
                            trackMetadata1.numberOfEvents = "0";
                        var deviceId = await wepdb.UserDevices.AsNoTracking().Where(x => x.UserId == userId && x.IsActive == true)
                        .Select(x => x.Id).FirstOrDefaultAsync();
                        trackMetadata1.deviceId = deviceId.ToString();
                        var deviceTypeId = await wepdb.UserDevices.AsNoTracking().Where(x => x.UserId == userId && x.IsActive == true)
                            .Select(x => x.DeviceTypeId).FirstOrDefaultAsync();
                        trackMetadata1.deviceName = await weddb.DeviceTypes.AsNoTracking().Where(x => x.Id == deviceTypeId)
                            .Select(x => x.Name).FirstOrDefaultAsync();

                        int i = 0;
                        Stopwatch s = new Stopwatch();
                        s.Start();
                        ThreadPool.SetMinThreads(Environment.ProcessorCount, Environment.ProcessorCount);
                        if (EventsList != null)
                        {
                            int mid = EventsList.Count / 2;
                            List<EventModel> FirstHalfEvent = new List<EventModel>();
                            List<EventModel> SecondHalf = new List<EventModel>();
                            List<System.Action> actions = new List<System.Action>();
                            if (EventsList.Count == 1)
                            {
                                FirstHalfEvent.Add(EventsList.First());
                            }
                            else
                            {
                                FirstHalfEvent = EventsList.Take(mid).ToList();
                                SecondHalf = EventsList.Skip(mid).ToList();
                            }

                            List<Task> tasks = new List<Task>();
                            await calendarService.FirstHalfEvent2New(userId, SecondHalf, profileEmails, profileId,
                                  profileDisplay);
                            await calendarService.FirstHalfEventNew(userId, FirstHalfEvent, profileEmails, profileId,
                                    profileDisplay);
                        }
                    }
                    var userDetail = await wepdb.Users.Where(x => x.Id == userId).AsNoTracking().FirstOrDefaultAsync();
                    var timezoneSetting = wepdb.UserSettings.AsNoTracking()
                        .FirstAsync(x => x.SettingId == CommonData.TimeZoneSettingId && x.UserId == userId).Result.Value;
                    int hourToAdd = 0;
                    int minutesToAdd = 0;

                    if (!string.IsNullOrEmpty(timezoneSetting))
                    {
                        var split = timezoneSetting.Split(":");
                        hourToAdd = int.Parse(split[0]);
                        minutesToAdd = int.Parse(split[1]);
                    }

                    TimeSpan timespan = new TimeSpan(hourToAdd, minutesToAdd, 0);
                    DateTimeOffset utcNow = DateTime.UtcNow.Date;
                    DateTimeOffset today = utcNow.ToOffset(timespan);

                    DateTimeOffset tomorrow = today.AddHours(24);
                    DateTimeOffset yesterday = today.AddHours(-24);
                    long yLong = yesterday.ToUnixTimeSeconds();
                    DateTimeOffset minusOne = today.AddMinutes(-1);
                    long mLong = minusOne.ToUnixTimeSeconds();
                    long tomorrowSecs = tomorrow.AddHours(24).ToUnixTimeSeconds();
                    List<CustomEventModel> events = new List<CustomEventModel>();
                    var activeUserProviderList = await wepdb.UserProviders.AsNoTracking().Where(x => x.UserId == userId && x.IsActive == true).Select(x => x.Id).ToListAsync();
                    Stopwatch stopwatch = new Stopwatch();
                    stopwatch.Start();
                    events = await evdb.EventModels.AsNoTracking().Where(w =>
                        w.UserId == userId && w.IsActive == true && w.BTwo == true && w.LEnd > yLong &&
                        w.LStart < tomorrowSecs).Select(w => new CustomEventModel
                        {
                            Id = w.Id,
                            Desc = w.Desc,
                            End = (DateTimeOffset)w.End,
                            EventId = w.EventId,
                            Notes = w.Notes,
                            ProviderId = w.ProviderId,
                            Start = w.Start,
                            Subject = w.Subject,
                            UserId = (Guid)w.UserId,
                            UserProviderId = w.UserProviderId,
                            LEnd = w.LEnd,
                            LStart = w.LStart,
                            BTwo = w.BTwo,
                            IsAllDay = w.IsAllDay,
                            CalId = w.CalId
                        }).ToListAsync();
                    var activeUserProviderSet = new HashSet<Guid>(activeUserProviderList);
                    events = events.Where(e => activeUserProviderSet.Contains((Guid)e.UserProviderId)).ToList();
                    TrackMetadata trackMetadata = new TrackMetadata();
                    try
                    {

                        trackMetadata.user_id = userId.ToString();
                        trackMetadata.action = "Event";
                        var userDeviceId = await wepdb.UserDevices.Where(x => x.UserId == userId && x.IsActive == true).Select(x => x.Id)
                            .FirstOrDefaultAsync();
                        var userDeviceTypeId = await wepdb.UserDevices.Where(x => x.UserId == userId && x.IsActive == true).Select(x => x.DeviceTypeId)
                        .FirstOrDefaultAsync();
                        trackMetadata.numberOfEvents = events.Count.ToString();
                        trackMetadata.deviceId = userDeviceId.ToString();
                        trackMetadata.deviceTypeId = userDeviceTypeId.ToString();
                        trackMetadata.deviceName = weddb.DeviceTypes.Where(x => x.Id == userDeviceTypeId)
                            .Select(x => x.Name).FirstOrDefault();
                        trackMetadata.signInTime = DateTime.Now.ToString();
                        trackMetadata.loginWebsite = "WebApp";
                        trackMetadata.isFreeDomain = isFreeDomain.ToString();

                        if (userDetail.IsTestUser != null)
                        {
                            trackMetadata.isTestUser = userDetail.IsTestUser.ToString();
                        }
                        metricsService.TrackUsuage(trackMetadata, "events");
                    }
                    catch (Exception ex)
                    {
                        Models.Models.LogModel logModel = new Models.Models.LogModel()
                        {
                            ErrorMessage = ex.Message,
                            Id = userId.ToString(),
                            //InputUsed = jsonKey,
                            Source = "Web App",
                            StackTrace = ex.StackTrace
                        };

                        await SESService.SendErrorMailsWebApp(logModel);
                    }

                    foreach (var events1 in events)
                    {
                        events.FirstOrDefault(x => x.EventId == events1.EventId).EventAttendees = await evdb.EventAttendees.AsNoTracking()
                            .Where(x => x.EventId == events1.EventId && x.UserId == userId).Select(w => new CustomEventAttendeeModel
                            {
                                ContactId = w.ContactId,
                                Address = w.Address,
                                EventId = w.EventId,
                                Name = w.Name,
                                Id = w.Id,
                                UserId = (Guid)w.UserId
                            }).ToListAsync();
                    }


                    if (events != null)
                    {
                        telemetryTracker.TrackTrace("events Received" + userId.ToString(), SeverityLevel.Information);
                    }
                    var homePageCards = await weddb.Cards.AsNoTracking().Where(w => w.IsActive == true && w.PageType == 1)
                        .Select(w => w.Id).ToListAsync();
                    WebAppEventModelNew returnModel = new();
                    var task1 = calendarService.GetEventsNew(false, events, userId, false);
                    var task2 = calendarService.GetEventsNew(true, events, userId, false);
                    var task3 = calendarService.GetEventsNew(false, events, userId, true);

                    await Task.WhenAll(task1, task2, task3);

                    var todayEvents = await task1;
                    var nowEvents = await task2;
                    var tomorrowEvents = await task3;
                    var mLong2 = DateTimeOffset.UtcNow.ToOffset(timespan).AddMinutes(-1).ToUnixTimeSeconds();
                    var yEvents = events
                        .Where(w => w.BTwo == true && w.LStart > yLong && w.LEnd <= mLong2).ToList();
                    List<CustomEventModel> yestEvent = new List<CustomEventModel>();
                    foreach (var yItem in yEvents)
                    {
                        var isCalendarExists =
                         await evdb.CalendarModels.AnyAsync(x => x.IsSelected == true && x.RefId == yItem.CalId);
                        if (isCalendarExists)
                        {
                            yestEvent.Add(yItem);
                        }
                    }
                    List<CustomEventModel> reminderList = new List<CustomEventModel>();
                    if (yestEvent != null)
                    {
                        reminderList = yestEvent.ToList();
                    }
                    else
                    {
                        reminderList = new List<CustomEventModel>();
                    }
                    if (todayEvents == null)
                        todayEvents = new List<CustomEventModel>();
                    if (nowEvents == null)
                        nowEvents = new List<CustomEventModel>();
                    if (tomorrowEvents == null)
                        tomorrowEvents = new List<CustomEventModel>();
                    try
                    {

                        returnModel.TodayList = todayEvents.ToList();
                        TelegramService.SendMessageToTestBot($"today event count: " + todayEvents.ToList().Count);
                        telemetryTracker.TrackTrace("Process Now  Event Started= " + userId.ToString(),
                            SeverityLevel.Information);
                        int nowCount = 0;
                        returnModel.NowList = nowEvents.ToList();
                        TelegramService.SendMessageToTestBot($"nowEvents  count: " + nowEvents.ToList().Count);
                        stopwatch.Start();
                        foreach (var titem in returnModel.TodayList.ToList())
                        {
                            nowCount++;
                            telemetryTracker.TrackTrace("Process Now  Event Count= " + nowCount.ToString() + " " + userId.ToString(), SeverityLevel.Information);

                            if (!titem.Desc.IsNullOrEmpty())
                            {
                                if (titem.Desc.Contains("\n"))
                                {
                                    var splitValue = titem.Desc.Split('\n');
                                    var str1 = " ";
                                    var str2 = " ";

                                    foreach (var item in splitValue)
                                    {
                                        if (item.Contains("meet.google.com") || item.Contains("zoom.us") ||
                                            item.Contains("teams.microsoft.com") || item.Contains(".....") ||
                                            item.Contains("join.skype.com") || item.Contains("calendly.com"))
                                        {
                                            continue;
                                        }
                                        else
                                        {
                                            str1 = item + "\n";
                                            str2 = str2 + str1;
                                        }
                                    }

                                    titem.Desc = str2;
                                }
                            }
                        }

                    }
                    catch (Exception ex)
                    {
                        Models.Models.LogModel logModel = new Models.Models.LogModel()
                        {
                            ErrorMessage = ex.Message,
                            Id = userId.ToString(),
                            //InputUsed = jsonKey,
                            Source = "Web App",
                            StackTrace = ex.StackTrace
                        };

                        await SESService.SendErrorMailsWebApp(logModel);
                        TelegramService.SendMessageToTestBot("user: " + userId.ToString() + "....." + ex.StackTrace);
                    }
                    TelegramService.SendMessageToTestBot("Total Time to Events From summary=" + stopwatch.ElapsedMilliseconds);
                    stopwatch.Stop();
                    telemetryTracker.TrackTrace("Process Now  Event Completed= " + " " + userId.ToString(),
                        SeverityLevel.Information);
                    var reminderList2 = reminderList.Where(w => w.Notes == "" || w.Notes == null).ToList();
                    stopwatch.Start();
                    returnModel.ReminderList = reminderList2;

                    TelegramService.SendMessageToTestBot("Total Time to reminder attenddess" + stopwatch.ElapsedMilliseconds);
                    stopwatch.Stop();
                    returnModel.TomorrowList = tomorrowEvents;
                    telemetryTracker.TrackTrace("Process Today  Event Started=  " + userId.ToString(),
                        SeverityLevel.Information);
                    int todayEventCount = 0;
                    var totalAttendes = new List<CustomEventAttendeeModel>();

                    //  var userDetails = await wepdb.Users.AsNoTracking().Where(x => x.Id == userId).FirstOrDefaultAsync();
                    var slice = userDetail.Email.Split('@');

                    var orgUser = await weodb.OrgUsers.AsNoTracking().Where(w =>
                            w.UserId == userId && w.Org.Name.ToLower() == slice[1].ToLower() && w.IsActive == true)
                        .Include(x => x.Org).ToListAsync();
                    var orgId = orgUser
                        .Where(x => x.Org.AppId.ToLower() == userDetail.AppId.ToString().ToLower() &&
                                    x.Org.IsActive == true).Select(x => x.Org.Id).FirstOrDefault();
                    var contactContext = new DbContextOptionsBuilder<EventRDSContext>();
                    contactContext.UseSqlServer(_configuration["DBConnections:WEEventRDSConnectionString"]);
                    var contsbContext = new DbContextOptionsBuilder<ContactContext>();
                    contsbContext.UseSqlServer(_configuration["DBConnections:WEContactConnectionString"]);
                    stopwatch.Start();
                    using (var db1 = new ContactContext(contsbContext.Options))
                    {
                        if (todayEvents.Count > 0)
                        {
                            foreach (var eventItem in todayEvents)
                            {
                                int tempCount = 0;
                                bool isPresent = false;
                                var attendeeList = new List<CustomEventAttendeeModel>();
                                todayEventCount++;

                                telemetryTracker.TrackTrace(
                                    "Process Today  Event Count= " + todayEventCount.ToString() + " " +
                                    userId.ToString(), SeverityLevel.Information);
                                if (eventItem.EventAttendees.Count > 0)
                                {
                                    foreach (var attItem in eventItem.EventAttendees)
                                    {
                                        //  var researchExists = false;
                                        var contactDataExists = await evdb.Contacts.AsNoTracking().AnyAsync(w => w.Id == attItem.ContactId);
                                        if (contactDataExists)
                                        {
                                            var contactData = await evdb.Contacts.AsNoTracking().Where(w => w.Id == attItem.ContactId).Select(w => new CustomContactModel
                                            {
                                                Id = w.Id,
                                                CRMId = w.CRMId,
                                                EmailAddresse = w.EmailDisplay,
                                                FullName = w.FirstName + " " + w.LastName,
                                                OtherUserId = w.OtherUserId,
                                                PersonId = w.SOne,
                                                UserId = (Guid)w.UserId,
                                                IsCContact = w.IsCContact,
                                                IsCLead = w.IsCLead,
                                                IsProvider = w.IsProvider,
                                                role = w.Desig,
                                                company = w.Company

                                            })
                                                .FirstOrDefaultAsync();

                                            CustomEventAttendeeModel attModel = new();
                                            attModel.Address = attItem.Address;
                                            attModel.ContactId = (Guid)attItem.ContactId;
                                            attModel.EventId = attItem.EventId;
                                            attModel.Id = attItem.Id;
                                            attModel.Name = contactData.FullName;
                                            var stringReplace1 =
                                                contactData.EmailAddresse.Replace("|", "");
                                            attModel.role = contactData.role;
                                            attModel.company = contactData.company;
                                            Guid cId = (Guid)attItem.ContactId;
                                            var contactContextBuilder =
                                                new DbContextOptionsBuilder<ContactContext>();
                                            contactContextBuilder.UseSqlServer(
                                                _configuration["DBConnections:WEContactConnectionString"]);
                                            using (var dbdb = new ContactContext(contactContextBuilder.Options))
                                            {
                                                if (!isFreeDomain)
                                                {
                                                    var contactPersonExists = await dbdb.OrgPeople.AsNoTracking().AnyAsync(w =>
                                                        w.OrgId == orgId && w.EmailId.ToLower() == stringReplace1);
                                                    if (contactPersonExists)
                                                    {
                                                        try
                                                        {
                                                            var cp = await dbdb.OrgPeople.AsNoTracking().FirstOrDefaultAsync(w => w.OrgId == orgId && w.EmailId.ToLower() == stringReplace1);
                                                            attModel.Status = _requestResearchService.GetResearchStatus(cp);
                                                            attModel.ProfileImage = CommonData.S3BucketUrlForPic + cp.PersonId;
                                                        }
                                                        catch (Exception ex)
                                                        {

                                                        }
                                                    }
                                                    else
                                                    {
                                                        attModel.Status = 0;
                                                    }
                                                    attModel.UserId = (Guid)attItem.UserId;
                                                    attendeeList.Add(attModel);
                                                    totalAttendes.Add(attModel);
                                                }
                                                else
                                                {
                                                    var contactPersonExists = await db1.ContactPeople.AsNoTracking().AnyAsync(w => w.ContactId == attItem.ContactId);
                                                    if (contactPersonExists)
                                                    {
                                                        try
                                                        {
                                                            var cp = await dbdb.ContactPeople.AsNoTracking().FirstOrDefaultAsync(w => w.ContactId == attItem.ContactId);
                                                            attModel.Status = _requestResearchService.GetResearchStatus(cp);
                                                            attModel.ProfileImage = CommonData.S3BucketUrlForPic + cp.PersonId;
                                                        }
                                                        catch (Exception ex)
                                                        {

                                                        }
                                                    }
                                                    else
                                                    {
                                                        attModel.Status = 0;
                                                    }
                                                    attModel.UserId = (Guid)attItem.UserId;
                                                    attendeeList.Add(attModel);
                                                    totalAttendes.Add(attModel);
                                                }
                                            }
                                        }
                                    }
                                }
                                telemetryTracker.TrackTrace("Process Today  Event Completed " + " " + userId.ToString(),
                                    SeverityLevel.Information);
                                returnModel.AttendeesList = totalAttendes;
                            }
                        }
                        returnModel.ReminderList = returnModel.ReminderList.OrderBy(w => w.Start).ToList();
                        if (returnModel != null)
                        {
                            telemetryTracker.TrackTrace("returnModel is full" + userId.ToString(), SeverityLevel.Information);
                        }
                        List<WebAppWithCardIdsEventModel> returnWithCardIds = new List<WebAppWithCardIdsEventModel>();
                        foreach (var item in homePageCards)
                        {
                            WebAppWithCardIdsEventModel cardDataItem = new WebAppWithCardIdsEventModel();
                            switch (item.ToString().ToUpper())
                            {
                                case "A2A0CC8A-06A2-4A8C-B6E9-14B586CAAEA8": // People Today
                                    cardDataItem.CardId = item;
                                    cardDataItem.CardContent = returnModel.AttendeesList.DistinctBy(w => w.Address).ToList();
                                    returnWithCardIds.Add(cardDataItem);
                                    break;
                                case "B1B3FB2C-227F-447D-BCAF-7FE5095F9DCD": // Reminders
                                    cardDataItem.CardId = item;
                                    cardDataItem.CardContent = returnModel.ReminderList;
                                    returnWithCardIds.Add(cardDataItem);
                                    break;
                                case "E35F0620-C2EA-4373-98D0-8480498D8628": // Today
                                    cardDataItem.CardId = item;
                                    cardDataItem.CardContent = returnModel.TodayList;
                                    returnWithCardIds.Add(cardDataItem);
                                    break;
                                case "A2659706-289E-4830-9DD7-F0D1A9BBBDDB": // Now
                                    cardDataItem.CardId = item;
                                    cardDataItem.CardContent = returnModel.NowList;
                                    returnWithCardIds.Add(cardDataItem);
                                    break;
                                case "E35F0620-C2EA-4373-98D0-8480498D8629": // Tomorrow
                                    cardDataItem.CardId = item;
                                    cardDataItem.CardContent = returnModel.TomorrowList;
                                    returnWithCardIds.Add(cardDataItem);
                                    break;
                                default:
                                    break;
                            }
                        }

                        trackMetadata.numberOfAttendees = returnModel.AttendeesList.Count.ToString();
                        trackMetadata.loginWebsite = "WebApp";
                        metricsService.TrackUsuage(trackMetadata, "events");
                        return Ok(returnWithCardIds);
                    }
                }
            }
            catch (Exception ex)
            {
                Models.Models.LogModel logModel = new Models.Models.LogModel()
                {
                    ErrorMessage = ex.Message,
                    Id = userId.ToString(),
                    //InputUsed = jsonKey,
                    Source = "Web App",
                    StackTrace = ex.StackTrace
                };

                await SESService.SendErrorMailsWebApp(logModel);
                string errormessage = ex.StackTrace.ToString() + " " + ex.Message;
                return BadRequest(errormessage);
            }
        }
        [HttpPost]
        [Route("FB9F334D")]
        public async Task<IActionResult> ProcessEventForMobile(bool fetchOnline = false)
        {
            try
            {
                var userId = _authService.GetUser(Request);

                var userEmail = await wepdb.Users.AsNoTracking().Where(x => x.Id == userId).Select(x => x.Email).AsNoTracking().FirstOrDefaultAsync();
                var eventDb = new DbContextOptionsBuilder<EventRDSContext>();
                eventDb.UseSqlServer(
                    _configuration["DBConnections:WEEventRDSConnectionString"]);
                using (var evdb = new EventRDSContext(eventDb.Options))
                {
                    var EmailSplit = userEmail.Split('@');
                    var domain = EmailSplit[1].ToLower();
                    var isFreeDomain = await weddb.FreeDomains.AsNoTracking().AnyAsync(w => w.DomainName.ToLower() == domain.ToLower());
                    if (fetchOnline)
                    {
                        var profileEmails = await _redisCaching.GetListAsync<string>("ProfileEmailsList" + userId);

                        var profileId = await evdb.Contacts.AsNoTracking().Where(w => w.UserId == userId && w.OtherUserId == userId)
                            .Select(x => x.Id).FirstOrDefaultAsync();
                        var profileDisplay = await evdb.Contacts.AsNoTracking().Where(w => w.UserId == userId && w.OtherUserId == userId)
                            .Select(x => x.EmailDisplay)
                            .FirstOrDefaultAsync();
                        if (string.IsNullOrEmpty(profileDisplay))
                        {
                            profileDisplay = await evdb.ContactEmails.AsNoTracking().Where(x => x.ContactId == profileId && x.Email.ToLower() == userEmail.ToLower()).Select(x => x.Email).FirstOrDefaultAsync();
                        }

                        string newEventsCacheKey = RedisKeyManager.GetNewEventsKey(userId.ToString());
                        var EventsList = await _redisCaching.GetListAsync<EventModel>(newEventsCacheKey);
                        //var EventsList = weedb.EventModels.Where(x => x.Id == Guid.Parse("7e5ebf84-48aa-40fc-b0f7-db28af38e4ce")).ToList();
                        TrackMetadata trackMetadata1 = new TrackMetadata();
                        trackMetadata1.user_id = " Events List";
                        if (EventsList != null)
                            trackMetadata1.numberOfEvents = EventsList.Count.ToString();
                        else
                            trackMetadata1.numberOfEvents = "0";
                        var deviceId = await wepdb.UserDevices.AsNoTracking().Where(x => x.UserId == userId && x.IsActive == true)
                        .Select(x => x.Id).FirstOrDefaultAsync();
                        trackMetadata1.deviceId = deviceId.ToString();
                        var deviceTypeId = await wepdb.UserDevices.AsNoTracking().Where(x => x.UserId == userId && x.IsActive == true)
                            .Select(x => x.DeviceTypeId).FirstOrDefaultAsync();
                        trackMetadata1.deviceName = await weddb.DeviceTypes.AsNoTracking().Where(x => x.Id == deviceTypeId)
                            .Select(x => x.Name).FirstOrDefaultAsync();

                        int i = 0;
                        Stopwatch s = new Stopwatch();
                        s.Start();
                        ThreadPool.SetMinThreads(Environment.ProcessorCount, Environment.ProcessorCount);
                        if (EventsList != null)
                        {
                            int mid = EventsList.Count / 2;
                            List<EventModel> FirstHalfEvent = new List<EventModel>();
                            List<EventModel> SecondHalf = new List<EventModel>();
                            List<System.Action> actions = new List<System.Action>();
                            if (EventsList.Count == 1)
                            {
                                FirstHalfEvent.Add(EventsList.First());
                            }
                            else
                            {
                                FirstHalfEvent = EventsList.Take(mid).ToList();
                                SecondHalf = EventsList.Skip(mid).ToList();
                            }

                            List<Task> tasks = new List<Task>();
                            await calendarService.FirstHalfEvent2New(userId, SecondHalf, profileEmails, profileId,
                                  profileDisplay);
                            await calendarService.FirstHalfEventNew(userId, FirstHalfEvent, profileEmails, profileId,
                                    profileDisplay);
                        }
                    }
                    var userDetail = await wepdb.Users.Where(x => x.Id == userId).AsNoTracking().FirstOrDefaultAsync();
                    var timezoneSetting = wepdb.UserSettings.AsNoTracking()
                        .FirstAsync(x => x.SettingId == CommonData.TimeZoneSettingId && x.UserId == userId).Result.Value;
                    int hourToAdd = 0;
                    int minutesToAdd = 0;

                    if (!string.IsNullOrEmpty(timezoneSetting))
                    {
                        var split = timezoneSetting.Split(":");
                        hourToAdd = int.Parse(split[0]);
                        minutesToAdd = int.Parse(split[1]);
                    }

                    TimeSpan timespan = new TimeSpan(hourToAdd, minutesToAdd, 0);
                    DateTimeOffset utcNow = DateTime.UtcNow.Date;
                    DateTimeOffset today = utcNow.ToOffset(timespan);

                    DateTimeOffset tomorrow = today.AddHours(24);
                    DateTimeOffset yesterday = today.AddHours(-24);
                    long yLong = yesterday.ToUnixTimeSeconds();
                    DateTimeOffset minusOne = today.AddMinutes(-1);
                    long mLong = minusOne.ToUnixTimeSeconds();
                    long tomorrowSecs = tomorrow.AddHours(24).ToUnixTimeSeconds();
                    List<CustomEventModel> events = new List<CustomEventModel>();
                    var activeUserProviderList = await wepdb.UserProviders.AsNoTracking().Where(x => x.UserId == userId && x.IsActive == true).Select(x => x.Id).ToListAsync();
                    Stopwatch stopwatch = new Stopwatch();
                    stopwatch.Start();
                    events = await evdb.EventModels.AsNoTracking().Where(w =>
                        w.UserId == userId && w.IsActive == true && w.BTwo == true && w.LEnd > yLong &&
                        w.LStart < tomorrowSecs).Select(w => new CustomEventModel
                        {
                            Id = w.Id,
                            Desc = w.Desc,
                            End = (DateTimeOffset)w.End,
                            EventId = w.EventId,
                            Notes = w.Notes,
                            ProviderId = w.ProviderId,
                            Start = w.Start,
                            Subject = w.Subject,
                            UserId = (Guid)w.UserId,
                            UserProviderId = w.UserProviderId,
                            LEnd = w.LEnd,
                            LStart = w.LStart,
                            BTwo = w.BTwo,
                            IsAllDay = w.IsAllDay,
                            CalId = w.CalId
                        }).ToListAsync();
                    var activeUserProviderSet = new HashSet<Guid>(activeUserProviderList);
                    events = events.Where(e => activeUserProviderSet.Contains((Guid)e.UserProviderId)).ToList();
                    TrackMetadata trackMetadata = new TrackMetadata();
                    try
                    {

                        trackMetadata.user_id = userId.ToString();
                        trackMetadata.action = "Event";
                        var userDeviceId = await wepdb.UserDevices.Where(x => x.UserId == userId && x.IsActive == true).Select(x => x.Id)
                            .FirstOrDefaultAsync();
                        var userDeviceTypeId = await wepdb.UserDevices.Where(x => x.UserId == userId && x.IsActive == true).Select(x => x.DeviceTypeId)
                        .FirstOrDefaultAsync();
                        trackMetadata.numberOfEvents = events.Count.ToString();
                        trackMetadata.deviceId = userDeviceId.ToString();
                        trackMetadata.deviceTypeId = userDeviceTypeId.ToString();
                        trackMetadata.deviceName = weddb.DeviceTypes.Where(x => x.Id == userDeviceTypeId)
                            .Select(x => x.Name).FirstOrDefault();
                        trackMetadata.signInTime = DateTime.Now.ToString();
                        trackMetadata.loginWebsite = "WebApp";
                        trackMetadata.isFreeDomain = isFreeDomain.ToString();

                        if (userDetail.IsTestUser != null)
                        {
                            trackMetadata.isTestUser = userDetail.IsTestUser.ToString();
                        }
                        metricsService.TrackUsuage(trackMetadata, "events");
                    }
                    catch (Exception ex)
                    {

                    }

                    foreach (var events1 in events)
                    {
                        events.FirstOrDefault(x => x.EventId == events1.EventId).EventAttendees = await evdb.EventAttendees.AsNoTracking()
                            .Where(x => x.EventId == events1.EventId && x.UserId == userId).Select(w => new CustomEventAttendeeModel
                            {
                                ContactId = w.ContactId,
                                Address = w.Address,
                                EventId = w.EventId,
                                Name = w.Name,
                                Id = w.Id,
                                UserId = (Guid)w.UserId
                            }).ToListAsync();
                    }


                    if (events != null)
                    {
                        telemetryTracker.TrackTrace("events Received" + userId.ToString(), SeverityLevel.Information);
                    }
                    var homePageCards = await weddb.Cards.AsNoTracking().Where(w => w.IsActive == true && w.PageType == 1)
                        .Select(w => w.Id).ToListAsync();
                    WebAppEventModelNew returnModel = new();
                    var task1 = calendarService.GetEventsNew(false, events, userId, false);
                    var task2 = calendarService.GetEventsNew(true, events, userId, false);
                    var task3 = calendarService.GetEventsNew(false, events, userId, true);

                    await Task.WhenAll(task1, task2, task3);

                    var todayEvents = await task1;
                    var nowEvents = await task2;
                    var tomorrowEvents = await task3;
                    var mLong2 = DateTimeOffset.UtcNow.ToOffset(timespan).AddMinutes(-1).ToUnixTimeSeconds();
                    var yEvents = events
                        .Where(w => w.BTwo == true && w.LStart > yLong && w.LEnd <= mLong2).ToList();
                    List<CustomEventModel> yestEvent = new List<CustomEventModel>();
                    foreach (var yItem in yEvents)
                    {
                        var isCalendarExists =
                         await evdb.CalendarModels.AnyAsync(x => x.IsSelected == true && x.RefId == yItem.CalId);
                        if (isCalendarExists)
                        {
                            yestEvent.Add(yItem);
                        }
                    }
                    List<CustomEventModel> reminderList = new List<CustomEventModel>();
                    if (yestEvent != null)
                    {
                        reminderList = yestEvent.ToList();
                    }
                    else
                    {
                        reminderList = new List<CustomEventModel>();
                    }
                    if (todayEvents == null)
                        todayEvents = new List<CustomEventModel>();
                    if (nowEvents == null)
                        nowEvents = new List<CustomEventModel>();
                    if (tomorrowEvents == null)
                        tomorrowEvents = new List<CustomEventModel>();
                    try
                    {

                        returnModel.TodayList = todayEvents.ToList();
                        TelegramService.SendMessageToTestBot($"today event count: " + todayEvents.ToList().Count);
                        telemetryTracker.TrackTrace("Process Now  Event Started= " + userId.ToString(),
                            SeverityLevel.Information);
                        int nowCount = 0;
                        returnModel.NowList = nowEvents.ToList();
                        TelegramService.SendMessageToTestBot($"nowEvents  count: " + nowEvents.ToList().Count);
                        stopwatch.Start();
                        foreach (var titem in returnModel.TodayList.ToList())
                        {
                            nowCount++;
                            telemetryTracker.TrackTrace("Process Now  Event Count= " + nowCount.ToString() + " " + userId.ToString(), SeverityLevel.Information);

                            if (!titem.Desc.IsNullOrEmpty())
                            {
                                if (titem.Desc.Contains("\n"))
                                {
                                    var splitValue = titem.Desc.Split('\n');
                                    var str1 = " ";
                                    var str2 = " ";

                                    foreach (var item in splitValue)
                                    {
                                        if (item.Contains("meet.google.com") || item.Contains("zoom.us") ||
                                            item.Contains("teams.microsoft.com") || item.Contains(".....") ||
                                            item.Contains("join.skype.com") || item.Contains("calendly.com"))
                                        {
                                            continue;
                                        }
                                        else
                                        {
                                            str1 = item + "\n";
                                            str2 = str2 + str1;
                                        }
                                    }

                                    titem.Desc = str2;
                                }
                            }
                        }

                    }
                    catch (Exception ex)
                    {
                        TelegramService.SendMessageToTestBot("user: " + userId.ToString() + "....." + ex.StackTrace);
                    }
                    TelegramService.SendMessageToTestBot("Total Time to Events From summary=" + stopwatch.ElapsedMilliseconds);
                    stopwatch.Stop();
                    telemetryTracker.TrackTrace("Process Now  Event Completed= " + " " + userId.ToString(),
                        SeverityLevel.Information);
                    var reminderList2 = reminderList.Where(w => w.Notes == "" || w.Notes == null).ToList();
                    stopwatch.Start();
                    returnModel.ReminderList = reminderList2;

                    TelegramService.SendMessageToTestBot("Total Time to reminder attenddess" + stopwatch.ElapsedMilliseconds);
                    stopwatch.Stop();
                    returnModel.TomorrowList = tomorrowEvents;
                    telemetryTracker.TrackTrace("Process Today  Event Started=  " + userId.ToString(),
                        SeverityLevel.Information);
                    int todayEventCount = 0;
                    var totalAttendes = new List<CustomEventAttendeeModel>();

                    //  var userDetails = await wepdb.Users.AsNoTracking().Where(x => x.Id == userId).FirstOrDefaultAsync();
                    var slice = userDetail.Email.Split('@');

                    var orgUser = await weodb.OrgUsers.AsNoTracking().Where(w =>
                            w.UserId == userId && w.Org.Name.ToLower() == slice[1].ToLower() && w.IsActive == true)
                        .Include(x => x.Org).ToListAsync();
                    var orgId = orgUser
                        .Where(x => x.Org.AppId.ToLower() == userDetail.AppId.ToString().ToLower() &&
                                    x.Org.IsActive == true).Select(x => x.Org.Id).FirstOrDefault();
                    var contactContext = new DbContextOptionsBuilder<EventRDSContext>();
                    contactContext.UseSqlServer(_configuration["DBConnections:WEEventRDSConnectionString"]);
                    var contsbContext = new DbContextOptionsBuilder<ContactContext>();
                    contsbContext.UseSqlServer(_configuration["DBConnections:WEContactConnectionString"]);
                    stopwatch.Start();
                    using (var db1 = new ContactContext(contsbContext.Options))
                    {
                        if (todayEvents.Count > 0)
                        {
                            foreach (var eventItem in todayEvents)
                            {
                                int tempCount = 0;
                                bool isPresent = false;
                                var attendeeList = new List<CustomEventAttendeeModel>();
                                todayEventCount++;

                                telemetryTracker.TrackTrace(
                                    "Process Today  Event Count= " + todayEventCount.ToString() + " " +
                                    userId.ToString(), SeverityLevel.Information);
                                if (eventItem.EventAttendees.Count > 0)
                                {
                                    foreach (var attItem in eventItem.EventAttendees)
                                    {
                                        //  var researchExists = false;
                                        var contactDataExists = await evdb.Contacts.AsNoTracking().AnyAsync(w => w.Id == attItem.ContactId);
                                        if (contactDataExists)
                                        {
                                            var contactData = await evdb.Contacts.AsNoTracking().Where(w => w.Id == attItem.ContactId).Select(w => new CustomContactModel
                                            {
                                                Id = w.Id,
                                                CRMId = w.CRMId,
                                                EmailAddresse = w.EmailDisplay,
                                                FullName = w.FirstName + " " + w.LastName,
                                                OtherUserId = w.OtherUserId,
                                                PersonId = w.SOne,
                                                UserId = (Guid)w.UserId,
                                                IsCContact = w.IsCContact,
                                                IsCLead = w.IsCLead,
                                                IsProvider = w.IsProvider
                                            })
                                                .FirstOrDefaultAsync();

                                            CustomEventAttendeeModel attModel = new();
                                            attModel.Address = attItem.Address;
                                            attModel.ContactId = (Guid)attItem.ContactId;
                                            attModel.EventId = attItem.EventId;
                                            attModel.Id = attItem.Id;
                                            attModel.Name = contactData.FullName;
                                            var stringReplace1 =
                                                contactData.EmailAddresse.Replace("|", "");
                                            Guid cId = (Guid)attItem.ContactId;
                                            var contactContextBuilder =
                                                new DbContextOptionsBuilder<ContactContext>();
                                            contactContextBuilder.UseSqlServer(
                                                _configuration["DBConnections:WEContactConnectionString"]);
                                            using (var dbdb = new ContactContext(contactContextBuilder.Options))
                                            {
                                                if (!isFreeDomain)
                                                {
                                                    var contactPersonExists = await dbdb.OrgPeople.AsNoTracking().AnyAsync(w =>
                                                        w.OrgId == orgId && w.EmailId.ToLower() == stringReplace1);
                                                    if (contactPersonExists)
                                                    {
                                                        var cp = await dbdb.OrgPeople.AsNoTracking().FirstOrDefaultAsync(w => w.OrgId == orgId && w.EmailId.ToLower() == stringReplace1);
                                                        attModel.Status = _requestResearchService.GetResearchStatus(cp);
                                                        attModel.ProfileImage = CommonData.S3BucketUrlForPic + cp.PersonId;
                                                    }
                                                    else
                                                    {
                                                        attModel.Status = 0;
                                                    }
                                                    attModel.UserId = (Guid)attItem.UserId;
                                                    attendeeList.Add(attModel);
                                                    totalAttendes.Add(attModel);
                                                }
                                                else
                                                {
                                                    var contactPersonExists = await db1.ContactPeople.AsNoTracking().AnyAsync(w => w.ContactId == attItem.ContactId);
                                                    if (contactPersonExists)
                                                    {
                                                        var cp = await dbdb.ContactPeople.AsNoTracking().FirstOrDefaultAsync(w => w.ContactId == attItem.ContactId);
                                                        attModel.Status = _requestResearchService.GetResearchStatus(cp);
                                                        attModel.ProfileImage = CommonData.S3BucketUrlForPic + cp.PersonId;
                                                    }
                                                    else
                                                    {
                                                        attModel.Status = 0;
                                                    }
                                                    attModel.UserId = (Guid)attItem.UserId;
                                                    attendeeList.Add(attModel);
                                                    totalAttendes.Add(attModel);
                                                }
                                            }
                                        }
                                    }
                                }
                                telemetryTracker.TrackTrace("Process Today  Event Completed " + " " + userId.ToString(),
                                    SeverityLevel.Information);
                                returnModel.AttendeesList = totalAttendes;
                            }
                        }
                        returnModel.ReminderList = returnModel.ReminderList.OrderBy(w => w.Start).ToList();
                        if (returnModel != null)
                        {
                            telemetryTracker.TrackTrace("returnModel is full" + userId.ToString(), SeverityLevel.Information);
                        }
                        List<MobileAppWithCardIdsEventModel> returnWithCardIds = new List<MobileAppWithCardIdsEventModel>();
                        foreach (var item in homePageCards)
                        {
                            MobileAppWithCardIdsEventModel cardDataItem = new MobileAppWithCardIdsEventModel();
                            switch (item.ToString().ToUpper())
                            {
                                case "A2A0CC8A-06A2-4A8C-B6E9-14B586CAAEA8": // People Today
                                    cardDataItem.CardId = item;
                                    cardDataItem.AttendeeList = returnModel.AttendeesList.DistinctBy(w => w.Address).ToList();
                                    returnWithCardIds.Add(cardDataItem);
                                    break;
                                case "B1B3FB2C-227F-447D-BCAF-7FE5095F9DCD": // Reminders
                                    cardDataItem.CardId = item;
                                    cardDataItem.EventList = returnModel.ReminderList;
                                    returnWithCardIds.Add(cardDataItem);
                                    break;
                                case "E35F0620-C2EA-4373-98D0-8480498D8628": // Today
                                    cardDataItem.CardId = item;
                                    cardDataItem.EventList = returnModel.TodayList;
                                    returnWithCardIds.Add(cardDataItem);
                                    break;
                                case "A2659706-289E-4830-9DD7-F0D1A9BBBDDB": // Now
                                    cardDataItem.CardId = item;
                                    cardDataItem.EventList = returnModel.NowList;
                                    returnWithCardIds.Add(cardDataItem);
                                    break;
                                case "E35F0620-C2EA-4373-98D0-8480498D8629": // Tomorrow
                                    cardDataItem.CardId = item;
                                    cardDataItem.EventList = returnModel.TomorrowList;
                                    returnWithCardIds.Add(cardDataItem);
                                    break;
                                default:
                                    break;
                            }
                        }

                        trackMetadata.numberOfAttendees = returnModel.AttendeesList.Count.ToString();
                        trackMetadata.loginWebsite = "WebApp";
                        metricsService.TrackUsuage(trackMetadata, "events");
                        return Ok(returnWithCardIds);
                    }
                }
            }
            catch (Exception ex)
            {
                string errormessage = ex.StackTrace.ToString() + " " + ex.Message;
                return BadRequest(errormessage);
            }
        }
        [HttpPost]
        [Route("FE91337C")]
        public async Task<IActionResult> GetEvents(bool fetchOnline, string deviceId)
        {
            Guid userId = Guid.Empty;
            try
            {
                Dictionary<string, string> properties = new Dictionary<string, string>();
                Response.Headers.Add("X-Frame-Options", "DENY");
                Response.Headers.Add("X-Content-Type-Options", "nosniff");
                Response.Headers.Add("Content-Security-Policy", "default-src 'self'");
                Response.Headers.Add("Referrer-Policy", "no-referrer");
                Response.Headers.Add("Strict-Transport-Security", "max-age=31536000; includeSubDomains");
                userId = _authService.GetUser(Request);
                var token = _authService.GetToken(Request);
                if (userId == Guid.Empty)
                {
                    return Unauthorized("Wrong Token");
                }

                properties.Add("userId", userId.ToString());


                if (fetchOnline)
                {
                    TelegramService.SendMessageToTestBot2("Fetching events online");
                    await calendarService.ProcessEvents(userId, token, "", true, true);
                    telemetryTracker.TrackTrace("Process Events Done " + userId.ToString(), SeverityLevel.Information);
                    properties.Add("Process Events Done ", "true");
                }
                //var isRefreshed = _redisCaching.GetData<bool>("Wrong Refresh Token" + userId);
                //if (!isRefreshed)
                //{

                return Ok();
                //}
                //else
                //{
                //    return BadRequest("Re Login Required");
                //}
            }
            catch (Exception ex)
            {
                telemetryTracker.TrackException(ex, userId);
                return BadRequest("Error Occured " + ex.Message + " stack trace: " + ex.StackTrace.ToString());
            }
        }

        //Add Contact In ContactDetail
        public class AddContactModel
        {
            [Required(ErrorMessage = "First Name is Required")]
            public required string FirstName { get; set; }

            public string? LastName { get; set; }

            public string? Email { get; set; }

            public string? eventId { get; set; } = "";

            public string? Role { get; set; }

            public string? LinkedinUrl { get; set; }
        }

        public class ContactWithProfile
        {
            public Contact contactList { get; set; }

            public string peopleImage { get; set; }

            public int RequestStatus { get; set; }
        }

        public async Task<ContactInsightsReturnModel> GetInsightsByPersonId1(Guid userId, Guid personId)
        {
            try
            {
                var aiUrl = _configuration["ServerSettings:OnePageAIUrl"];
                var response = await restClient.GetAsync<ContactInsightsReturnModel>(aiUrl + "person/" + personId + "/insights", useAuthToken: false, isPDLCall: false);
                return response;
            }
            catch (Exception ex)
            {
                return null;
            }
        }
        public class EventsPaginationReturnModel
        {
            public string cardId { get; set; }

            public object value { get; set; }

            public bool? isNext { get; set; }
        }
        [HttpPost]
        [Route("116A952D")]
        public async Task<IActionResult> EventsPagination([FromQuery] Guid contactId, int pageNumber = 1, int size = 10, bool isPast = false)
        {

            try
            {
                var userId = _authService.GetUser(Request);
                if (userId == Guid.Empty)
                {
                    return BadRequest(CommonData.ErrorCodes.E0008);
                }
                EventsPaginationReturnModel returnModel = new EventsPaginationReturnModel();
                var eventsForContact = await weedb.EventAttendees.AsNoTracking().Where(w => w.ContactId == contactId).Select(w => w.EventId).ToListAsync();
                var events = await weedb.EventModels.AsNoTracking().Where(w => eventsForContact.Contains(w.EventId) && w.UserId == userId).ToListAsync();
                if (events.Count > 0)
                {
                    if (!isPast)
                    {
                        var futureEvents = events
                       .Where(w => w.End > DateTimeOffset.Now.Subtract(TimeSpan.FromMinutes(5)))
                       .ToList();
                        if (futureEvents.Count > 0)
                        {
                            QuickSort(futureEvents, 0, futureEvents.Count - 1);
                            var allEvents = futureEvents;
                            futureEvents = futureEvents.Skip((pageNumber - 1) * size).Take(size).ToList();
                            var nextContactList = allEvents.Skip(((pageNumber + 1) - 1) * size).Take(size).ToList();
                            if (nextContactList.Count() > 0)
                            {
                                returnModel.isNext = true;
                            }
                            else
                            {
                                returnModel.isNext = false;
                            }


                            returnModel.cardId = "6E043DD5-20A3-41B0-BE9B-B51D6D1DF469";
                            returnModel.value = futureEvents;
                            return Ok(returnModel);


                        }
                        else
                        {
                            return BadRequest(CommonData.ErrorCodes.E00010);
                        }

                    }
                    else
                    {

                        var pastEvents = events.Where(w => w.End < DateTimeOffset.Now)
                        .ToList();
                        if (pastEvents.Count > 0)
                        {
                            QuickSort(pastEvents, 0, pastEvents.Count - 1, true);
                            var allEvents = pastEvents;
                            pastEvents = pastEvents.Skip((pageNumber - 1) * size).Take(size).ToList();
                            var nextContactList = allEvents.Skip(((pageNumber + 1) - 1) * size).Take(size).ToList();
                            if (nextContactList.Count() > 0)
                            {
                                returnModel.isNext = true;
                            }
                            else
                            {
                                returnModel.isNext = false;
                            }
                            returnModel.cardId = "19C14CA5-41BE-4ED7-BAB6-CEBA398746A5";
                            returnModel.value = pastEvents;
                            return Ok(returnModel);
                        }
                        else
                        {
                            return BadRequest(CommonData.ErrorCodes.E00011);
                        }

                    }
                }
                return BadRequest(CommonData.ErrorCodes.E0009);

            }
            catch (Exception ex)
            {

                return BadRequest(ex);
            }

        }
        public static void QuickSort(List<EventModel> eventModels, int left, int right, bool isDescending = false)
        {
            if (left < right)
            {
                int pivotIndex = Partition(eventModels, left, right, isDescending);
                QuickSort(eventModels, left, pivotIndex - 1, isDescending);
                QuickSort(eventModels, pivotIndex + 1, right, isDescending);
            }

        }

        public static int Partition(List<EventModel> eventModels, int left, int right, bool isDescending = false)
        {
            EventModel pivotValue = eventModels[right];
            int pivotIndex = left;

            for (int i = left; i < right; i++)
            {
                if (isDescending)
                {
                    if (eventModels[i].LStart > pivotValue.LStart)

                    {
                        Swap(eventModels, i, pivotIndex);
                        pivotIndex++;
                    }
                }
                else
                {
                    if (eventModels[i].LStart > pivotValue.LStart)

                    {
                        Swap(eventModels, i, pivotIndex);
                        pivotIndex++;
                    }

                }
            }

            Swap(eventModels, pivotIndex, right);
            return pivotIndex;

        }
        public static void Swap(List<EventModel> eventModels, int i, int j)
        {
            EventModel temp = eventModels[i];
            eventModels[i] = eventModels[j];
            eventModels[j] = temp;
        }
        public class RequestInsightsModel1
        {
            public string contact_id { get; set; }
            public string email { get; set; }
            public string first_name { get; set; }
            public string last_name { get; set; }
            public string linkedin_url { get; set; }
            public string ln { get; set; }
            public string user_id { get; set; }
            public string user_first_name { get; set; }
            public string user_last_name { get; set; }
            public string user_email { get; set; }
            public string user_linkedin_url { get; set; }
        }

        public class ErrorStatus
        {
            public string errorMessage { get; set; }
        }


        public class UpdateContactModel
        {
            [Required(ErrorMessage = "ContactId is Required")]
            public required string ContactId { get; set; }

            public string? FirstName { get; set; }

            public string? LastName { get; set; }

            public string? Email { get; set; }

            public string? LinkedinUrl { get; set; }
        }

        [Route("69699EAF")]
        [HttpPost]
        public async Task<IActionResult> UpdateContact([FromBody] UpdateContactModel contactModel)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var userId = _authService.GetUser(Request);
                if (userId == Guid.Empty)
                {
                    return Unauthorized();
                }

                // Get the contact to update based on ContactId
                var contact = await weedb.Contacts.Where(x => x.Id == Guid.Parse(contactModel.ContactId))
                    .FirstOrDefaultAsync();

                if (contact == null)
                {
                    return NotFound("Contact not found");
                }


                if (!string.IsNullOrEmpty(contactModel.FirstName))
                {
                    contact.FirstName = contactModel.FirstName;
                }

                if (!string.IsNullOrEmpty(contactModel.LastName))
                {
                    contact.LastName = contactModel.LastName;
                }

                if (!string.IsNullOrEmpty(contactModel.Email))
                {
                    contact.EmailDisplay = contactModel.Email;
                }

                if (!string.IsNullOrEmpty(contactModel.LinkedinUrl))
                {
                    var socialTypeId = await weddb.SocialTypes.Where(w => w.Name == "LinkedIn").Select(x => x.Id)
                        .FirstOrDefaultAsync();

                    var contactSocials = await weedb.ContactSocials
                        .Where(c => c.ContactId == Guid.Parse(contactModel.ContactId) && c.SocialId == socialTypeId)
                        .ToListAsync();
                    foreach (var cs in contactSocials)
                    {
                        cs.Url = contactModel.LinkedinUrl;
                    }

                    var orgPContacts = await db.OrgPeople.Where(o => o.ContactId == Guid.Parse(contactModel.ContactId))
                        .ToListAsync();
                    foreach (var op in orgPContacts)
                    {
                        op.SLinkedIn = contactModel.LinkedinUrl;
                    }

                    await Task.WhenAll(weedb.SaveChangesAsync(), db.SaveChangesAsync());
                }


                await weedb.SaveChangesAsync();

                return Ok();
            }
            catch (Exception e)
            {
                return BadRequest();
            }
        }


        [Route("3113268A")]
        [HttpPost]

        public async Task<IActionResult> AddContact([FromBody] AddContactModel contactModel, bool isContactPage = false)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            ErrorStatus errorStatus = new ErrorStatus();
            if (string.IsNullOrEmpty(contactModel?.Email) && string.IsNullOrEmpty(contactModel?.LinkedinUrl))
            {
                errorStatus.errorMessage = "Either Email or Linkedin URL must be provided.";
                return BadRequest(errorStatus);
            }

            var userId = _authService.GetUser(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized();
            }

            var userDetails = await wepdb.Users.AsNoTracking().Where(x => x.Id == userId).FirstOrDefaultAsync();
            var slice = userDetails.Email.Split('@');
            var isFreeDomain = await weddb.FreeDomains.AsNoTracking().AnyAsync(x => x.DomainName.ToLower() == slice[1].ToLower());
            var contactDetail = new Contact();

            var orgUser = weodb.OrgUsers.Where(w =>
                    w.UserId == userId && w.Org.Name.ToLower() == slice[1].ToLower() && w.IsActive == true)
                .Include(x => x.Org).ToList();
            var orgId = orgUser
                .Where(x => x.Org.AppId.ToLower() == userDetails.AppId.ToString().ToLower() &&
                            x.Org.IsActive == true).Select(x => x.Org.Id).FirstOrDefault();

            ContactWithProfile returnModel = new ContactWithProfile();
            if (isContactPage)
            {
                var isContact = false;
                if (!string.IsNullOrEmpty(contactModel.Email))
                {

                    isContact = await weedb.Contacts.AsNoTracking().AnyAsync(x => x.EmailDisplay.Replace("|", "").ToLower() == contactModel.Email.ToLower() && x.UserId == userId);
                }
                else
                {
                    isContact = await weedb.ContactSocials.AsNoTracking().AnyAsync(x => x.Url.ToLower() == contactModel.LinkedinUrl.ToLower() && x.UserId == userId);
                }
                if (isContact)
                {
                    if (!string.IsNullOrEmpty(contactModel.Email))
                    {

                        var contactList = await weedb.Contacts.AsNoTracking().Where(x => x.EmailDisplay.Replace("|", "").ToLower() == contactModel.Email.Replace("|", "").ToLower() && x.UserId == userId).FirstOrDefaultAsync();
                        if (!isFreeDomain)
                        {
                            var checkForOrgPerson = await db.OrgPeople.AsNoTracking().AnyAsync(w => w.EmailId.ToLower() == contactList.EmailDisplay.Replace("|", "").ToLower() && w.OrgId == orgId);

                            if (checkForOrgPerson)
                            {
                                var orgPerson = await db.OrgPeople.AsNoTracking().FirstOrDefaultAsync(w => w.EmailId.ToLower().Replace("|", "") == contactList.EmailDisplay.Replace("|", "").ToLower() && w.OrgId == orgId);
                                var cp = await db.OrgPeople.AsNoTracking().FirstOrDefaultAsync(w => w.OrgId == orgId && w.EmailId.ToLower() == contactList.EmailDisplay.Replace("|", "").ToLower());
                                returnModel.RequestStatus = _requestResearchService.GetResearchStatus(cp);

                                if (orgPerson.PersonId != null)
                                {

                                    Guid personId = (Guid)(orgPerson.PersonId);
                                    var insightResponse = await GetInsightsByPersonId1(userId, personId);
                                    if (insightResponse.data != null)
                                    {
                                        returnModel.peopleImage = insightResponse.data.profile_pic;
                                    }
                                }

                            }
                            else
                            {
                                contactDetail = contactList;
                                OrgPerson orgPerson = new OrgPerson();
                                orgPerson.Id = Guid.NewGuid();
                                orgPerson.EmailId = contactDetail.EmailDisplay;
                                orgPerson.UserId = userId;
                                orgPerson.OrgId = orgId;
                                orgPerson.ContactId = contactList.Id;
                                orgPerson.SLinkedIn = contactDetail.Linked;
                                orgPerson.CreatedDate = DateTime.UtcNow;
                                orgPerson.ModifiedDate = DateTime.UtcNow;
                                orgPerson.IsInsightsFound = false;
                                orgPerson.IsRedeemed = false;
                                orgPerson.IsActive = true;
                                orgPerson.IsRequested = false;
                                orgPerson.IsSearchComplete = false;
                                db.OrgPeople.Add(orgPerson);
                                await db.SaveChangesAsync();

                            }
                            returnModel.contactList = contactList;

                            return Ok(returnModel);
                        }
                        else
                        {

                            var checkForOrgPerson = await db.ContactPeople.AsNoTracking().AnyAsync(w => w.ContactId == contactList.Id);

                            if (checkForOrgPerson)
                            {
                                var orgPerson = await db.ContactPeople.AsNoTracking().FirstOrDefaultAsync(w => w.ContactId == contactList.Id && w.Email.ToLower() == contactModel.Email.Replace("|", ""));

                                var cp = await db.ContactPeople.AsNoTracking().FirstOrDefaultAsync(w => w.ContactId == contactList.Id && w.Email.ToLower() == contactModel.Email.Replace("|", ""));
                                returnModel.RequestStatus = _requestResearchService.GetResearchStatus(cp);

                                if (orgPerson.PersonId != null)
                                {

                                    Guid personId = (Guid)(orgPerson.PersonId);
                                    var insightResponse = await GetInsightsByPersonId1(userId, personId);
                                    if (insightResponse.data != null)
                                    {
                                        returnModel.peopleImage = insightResponse.data.profile_pic;
                                    }
                                }

                            }
                            else
                            {
                                contactDetail = contactList;
                                ContactPerson orgPerson = new ContactPerson();
                                orgPerson.Id = Guid.NewGuid();
                                orgPerson.Email = contactDetail.EmailDisplay;
                                // orgPerson.UserId = userId;
                                //orgPerson.OrgId = orgId;
                                orgPerson.ContactId = contactList.Id;
                                // orgPerson.SLinkedIn = contactDetail.Linked;
                                orgPerson.CreatedDate = DateTime.UtcNow;
                                //orgPerson.ModifiedDate = DateTime.UtcNow;
                                orgPerson.InsightsFound = false;
                                orgPerson.IsRedeemed = false;
                                //orgPerso = true;
                                orgPerson.IsRequested = false;
                                orgPerson.IsSearchComplete = false;
                                db.ContactPeople.Add(orgPerson);
                                await db.SaveChangesAsync();

                            }
                            returnModel.contactList = contactList;

                            return Ok(returnModel);

                        }
                    }
                    else
                    {
                        var contactDetail1 = new Contact();
                        bool checkForOrgPerson = false;
                        List<Contact> ContactList = new List<Contact>();
                        Guid contactId = Guid.Empty;
                        var isContactSocialExists = await weedb.ContactSocials.AsNoTracking().AnyAsync(x => x.Url.ToLower() == contactModel.LinkedinUrl.ToLower() && x.IsActive == true);
                        if (isContactSocialExists)
                        {
                            var contactSocial = await weedb.ContactSocials.FirstOrDefaultAsync(x => x.Url.ToLower() == contactModel.LinkedinUrl.ToLower() && x.IsActive == true);
                            if (contactSocial != null)
                            {
                                contactId = (Guid)contactSocial.ContactId;
                            }
                            // var query = from contact in weedb.Contacts
                            //             where (from cs in contactSOCIAL
                            //                    select cs.ContactId).Contains(contact.Id)
                            //             && contact.UserId == userId
                            //             select contact;
                            // if (query.Count() > 0)
                            // {
                            //     ContactList = query.ToList();
                            // }
                        }
                        if (ContactList.Count > 0)
                        {
                            contactId = ContactList.Select(x => x.Id).FirstOrDefault();
                            checkForOrgPerson = await db.OrgPeople.AsNoTracking().AnyAsync(w => w.ContactId == contactId);
                            contactDetail = await weedb.Contacts.AsNoTracking().Where(x => x.Id == contactId).FirstOrDefaultAsync();
                        }
                        else
                        {
                            OnePage.Models.Models.Contact contact = new OnePage.Models.Models.Contact();
                            contact.Id = Guid.NewGuid();
                            contact.FirstName = contactModel.FirstName;
                            contact.LastName = contactModel?.LastName ?? string.Empty;
                            contact.EmailDisplay = contactModel?.Email ?? string.Empty;
                            contact.ProviderId = CommonData.ContactAddedByUser;
                            contact.UserId = userId;
                            contact.Created = DateTime.UtcNow;
                            contact.Modified = DateTime.UtcNow;
                            contact.Display = contactModel.FirstName + " " + contactModel?.LastName;
                            contact.Desig = contactModel?.Role;
                            contact.IsActive = true;
                            weedb.Contacts.Add(contact);
                            await weedb.SaveChangesAsync();
                            contactId = contact.Id;

                            if (!String.IsNullOrEmpty(contactModel?.LinkedinUrl))
                            {
                                OnePage.Models.Models.ContactSocial contactSocial = new OnePage.Models.Models.ContactSocial();
                                contactSocial.Id = Guid.NewGuid();
                                contactSocial.ContactId = contactId;
                                contactSocial.Created = DateTime.Now;
                                contactSocial.IsActive = true;
                                contactSocial.Url = contactModel?.LinkedinUrl;
                                contactSocial.UserId = userId;
                                contactSocial.Name = "LinkedIn";
                                contactSocial.SocialId = Guid.Parse("0EA37808-1506-43A3-9ABD-6FF561941FF5");
                                weedb.ContactSocials.Add(contactSocial);
                                await weedb.SaveChangesAsync();
                            }
                            contactDetail = contact;
                        }


                        if (!isFreeDomain)
                        {

                            if (checkForOrgPerson)
                            {

                                var cp = db.OrgPeople.First(w => w.ContactId == contactId);
                                returnModel.RequestStatus = _requestResearchService.GetResearchStatus(cp);


                                if (cp.PersonId != null)
                                {

                                    Guid personId = (Guid)(cp.PersonId);
                                    var insightResponse = await GetInsightsByPersonId1(userId, personId);
                                    if (insightResponse.data != null)
                                    {
                                        returnModel.peopleImage = insightResponse.data.profile_pic;
                                    }
                                }

                            }
                            else
                            {
                                OrgPerson orgPerson = new OrgPerson();
                                orgPerson.Id = Guid.NewGuid();
                                orgPerson.EmailId = contactDetail.EmailDisplay;
                                orgPerson.OrgId = orgId;
                                orgPerson.UserId = userId;
                                orgPerson.ContactId = contactDetail.Id;
                                orgPerson.SLinkedIn = contactDetail.Linked;
                                orgPerson.CreatedDate = DateTime.UtcNow;
                                orgPerson.ModifiedDate = DateTime.UtcNow;
                                orgPerson.IsInsightsFound = false;
                                orgPerson.IsRedeemed = false;
                                orgPerson.IsActive = true;
                                orgPerson.IsRequested = false;
                                orgPerson.IsSearchComplete = false;
                                db.OrgPeople.Add(orgPerson);
                                db.SaveChanges();

                            }
                            returnModel.contactList = contactDetail;
                            return Ok(returnModel);
                        }
                        else
                        {
                            checkForOrgPerson = await db.ContactPeople.AsNoTracking().AnyAsync(w => w.ContactId == contactDetail.Id);

                            if (checkForOrgPerson)
                            {
                                var orgPerson = await db.ContactPeople.AsNoTracking().FirstOrDefaultAsync(w => w.ContactId == contactDetail.Id && w.Email.ToLower() == contactModel.Email.Replace("|", ""));

                                var cp = await db.ContactPeople.AsNoTracking().FirstOrDefaultAsync(w => w.ContactId == contactDetail.Id && w.Email.ToLower() == contactModel.Email.Replace("|", ""));
                                returnModel.RequestStatus = _requestResearchService.GetResearchStatus(cp);

                                if (orgPerson.PersonId != null)
                                {

                                    Guid personId = (Guid)(orgPerson.PersonId);
                                    var insightResponse = await GetInsightsByPersonId1(userId, personId);
                                    if (insightResponse.data != null)
                                    {
                                        returnModel.peopleImage = insightResponse.data.profile_pic;
                                    }
                                }

                            }
                            else
                            {
                                contactDetail = contactDetail;
                                ContactPerson orgPerson = new ContactPerson();
                                orgPerson.Id = Guid.NewGuid();
                                orgPerson.Email = contactDetail.EmailDisplay;
                                // orgPerson.UserId = userId;
                                //orgPerson.OrgId = orgId;
                                orgPerson.ContactId = contactDetail.Id;
                                // orgPerson.SLinkedIn = contactDetail.Linked;
                                orgPerson.CreatedDate = DateTime.UtcNow;
                                //orgPerson.ModifiedDate = DateTime.UtcNow;
                                orgPerson.InsightsFound = false;
                                orgPerson.IsRedeemed = false;
                                //orgPerso = true;
                                orgPerson.IsRequested = false;
                                orgPerson.IsSearchComplete = false;
                                db.ContactPeople.Add(orgPerson);
                                await db.SaveChangesAsync();

                            }
                            returnModel.contactList = contactDetail;

                            return Ok(returnModel);

                        }
                    }

                }
                else
                {
                    var op = new OrgPerson();
                    OnePage.Models.Models.Contact contact = new OnePage.Models.Models.Contact();
                    contact.Id = Guid.NewGuid();
                    contact.FirstName = contactModel.FirstName;
                    contact.LastName = contactModel?.LastName ?? string.Empty;
                    contact.EmailDisplay = contactModel?.Email ?? string.Empty;
                    contact.ProviderId = CommonData.ContactAddedByUser;
                    contact.UserId = userId;
                    contact.Created = DateTime.UtcNow;
                    contact.Modified = DateTime.UtcNow;
                    contact.Display = contactModel.FirstName + " " + contactModel?.LastName;
                    contact.Desig = contactModel?.Role;
                    contact.IsActive = true;
                    weedb.Contacts.Add(contact);
                    await weedb.SaveChangesAsync();
                    returnModel.contactList = contact;
                    if (!string.IsNullOrEmpty(contactModel?.LinkedinUrl))
                    {
                        var socialTypeId = weddb.SocialTypes.FirstOrDefault(w => w.Name == "LinkedIn");
                        OnePage.Models.Models.ContactSocial social = new()
                        {
                            ContactId = contact.Id,
                            Url = contactModel.LinkedinUrl,
                            UserId = userId,
                            IsActive = true,
                            AskMe = false,
                            CanRequest = false,
                            Name = socialTypeId.Name,
                            Request = false,
                            Show = false,
                            SocialId = socialTypeId.Id,
                            Id = Guid.NewGuid()
                        };
                        weedb.ContactSocials.Add(social);
                        await weedb.SaveChangesAsync();
                        if (isFreeDomain)
                        {
                            var orgPerson = new ContactPerson()
                            {
                                ContactId = contact.Id,
                                CreatedDate = DateTime.UtcNow,
                                Email = contact.EmailDisplay,
                                Id = Guid.NewGuid(),

                                InsightsFound = false,
                                IsRedeemed = false,
                                IsRequested = false,
                                IsSearchComplete = false,

                                LinkedIn = contactModel.LinkedinUrl,

                            };

                            db.ContactPeople.Add(orgPerson);
                            await db.SaveChangesAsync();
                        }
                        else
                        {
                            var orgPerson = new OrgPerson()
                            {
                                ContactId = contact.Id,
                                CreatedDate = DateTime.UtcNow,
                                EmailId = contact.EmailDisplay,
                                Id = Guid.NewGuid(),
                                IsActive = true,
                                IsInsightsFound = false,
                                IsRedeemed = false,
                                IsRequested = false,
                                IsSearchComplete = false,
                                ModifiedDate = DateTime.UtcNow,
                                OrgId = orgId,
                                SLinkedIn = contactModel.LinkedinUrl,
                                UserId = userId
                            };

                            db.OrgPeople.Add(orgPerson);
                            await db.SaveChangesAsync();
                        }
                    }
                    int status = 0;
                    returnModel.RequestStatus = status;
                }
                return Ok(returnModel);

            }
            var isContactPresent = false;
            if (!string.IsNullOrEmpty(contactModel.Email))
            {
                isContactPresent = await weedb.Contacts.AsNoTracking().AnyAsync(x => x.EmailDisplay.ToLower() == contactModel.Email.ToLower() && x.UserId == userId);

            }
            else
            {

                List<Contact> ContactList = new List<Contact>();
                Guid contactId = Guid.Empty;
                var isContactSocialExists = await weedb.ContactSocials.AsNoTracking().AnyAsync(x => x.Url.ToLower() == contactModel.LinkedinUrl.ToLower() && x.IsActive == true);
                if (isContactSocialExists)
                {
                    var contactSocial = await weedb.ContactSocials.FirstOrDefaultAsync(x => x.Url.ToLower() == contactModel.LinkedinUrl.ToLower() && x.IsActive == true);
                    if (contactSocial != null)
                    {
                        contactId = (Guid)contactSocial.ContactId;
                    }
                    // var query = from contact in weedb.Contacts
                    //             where (from cs in contactSOCIAL
                    //                    select cs.ContactId).Contains(contact.Id)
                    //             && contact.UserId == userId
                    //             select contact;
                    // if (query.Count() > 0)
                    // {
                    //     ContactList = query.ToList();
                    // }
                }
                // Guid contactId = Guid.Empty;
                // if (ContactList.Count > 0)
                // {
                //     contactId = ContactList.Select(x => x.Id).FirstOrDefault();
                // }
                if (contactId == Guid.Empty)
                {
                    isContactPresent = false;
                }
                else
                {
                    isContactPresent = true;
                }
            }

            if (!isContactPresent)
            {
                var op = new OrgPerson();
                OnePage.Models.Models.Contact contact = new OnePage.Models.Models.Contact();
                contact.Id = Guid.NewGuid();
                contact.FirstName = contactModel.FirstName;
                contact.LastName = contactModel.LastName;
                contact.EmailDisplay = contactModel.Email;
                contact.ProviderId = CommonData.ContactAddedByUser;
                contact.UserId = userId;
                contact.Created = DateTime.UtcNow;
                contact.Modified = DateTime.UtcNow;
                contact.Desig = contactModel.Role;
                contact.Display = contactModel.FirstName + " " + contactModel.LastName;
                contact.IsActive = true;
                weedb.Contacts.Add(contact);
                await weedb.SaveChangesAsync();
                returnModel.contactList = contact;
                if (!string.IsNullOrEmpty(contactModel.LinkedinUrl))
                {
                    var socialTypeId = await weddb.SocialTypes.AsNoTracking().FirstOrDefaultAsync(w => w.Name == "LinkedIn");
                    var isUrlExists = await weedb.ContactSocials.AsNoTracking().AnyAsync(w => w.UserId == userId && w.Url == contactModel.LinkedinUrl && w.IsActive == true);
                    if (!isUrlExists)
                    {
                        OnePage.Models.Models.ContactSocial social = new()
                        {
                            ContactId = contact.Id,
                            Url = contactModel.LinkedinUrl,
                            UserId = userId,
                            IsActive = true,
                            AskMe = false,
                            CanRequest = false,
                            Name = socialTypeId.Name,
                            Request = false,
                            Show = false,
                            SocialId = socialTypeId.Id,
                            Id = Guid.NewGuid()
                        };
                        weedb.ContactSocials.Add(social);
                        await weedb.SaveChangesAsync();
                    }
                }
                var orgPerson = new OrgPerson()
                {
                    ContactId = contact.Id,
                    CreatedDate = DateTime.UtcNow,
                    EmailId = contact.EmailDisplay,
                    Id = Guid.NewGuid(),
                    IsActive = true,
                    IsInsightsFound = false,
                    IsRedeemed = false,
                    IsRequested = false,
                    IsSearchComplete = false,
                    ModifiedDate = DateTime.UtcNow,
                    OrgId = orgId,
                    SLinkedIn = contactModel.LinkedinUrl,
                    UserId = userId
                };

                db.OrgPeople.Add(orgPerson);
                await db.SaveChangesAsync();
                op = orgPerson;
                int status = 0;
                returnModel.RequestStatus = status;
                var isEventPresent = await weedb.EventModels.AsNoTracking().Where(x => x.EventId == contactModel.eventId && x.UserId == userId && x.IsActive == true).FirstOrDefaultAsync();
                if (isEventPresent != null)
                {
                    var isEventAttendee = await weedb.EventAttendees.AsNoTracking().Where(x => x.EventId == isEventPresent.EventId && x.ContactId == contact.Id).FirstOrDefaultAsync();
                    if (isEventAttendee != null)
                    {
                        return Ok(returnModel);
                    }
                    OnePage.Models.Models.EventAttendee eventAttendee = new OnePage.Models.Models.EventAttendee();
                    eventAttendee.Id = Guid.NewGuid();
                    eventAttendee.Address = contactModel.Email;
                    eventAttendee.EventId = contactModel.eventId;
                    eventAttendee.UserId = userId;
                    eventAttendee.ContactId = contact.Id;
                    eventAttendee.IsActive = true;
                    eventAttendee.Name = contactModel.FirstName + " " + contactModel.LastName;
                    eventAttendee.Created = DateTime.UtcNow;
                    eventAttendee.Modified = DateTime.UtcNow;
                    eventAttendee.IsAdhocUser = true;
                    weedb.EventAttendees.Add(eventAttendee);
                    await weedb.SaveChangesAsync();
                }
                else
                {

                    errorStatus.errorMessage = "Event does not exists.";
                    return BadRequest(errorStatus);


                }
                return Ok(returnModel);

            }
            else
            {
                if (!string.IsNullOrEmpty(contactModel.Email))
                {
                    var op = new OrgPerson();
                    var contactList = await weedb.Contacts.AsNoTracking().Where(x => x.EmailDisplay.ToLower() == contactModel.Email.ToLower() && x.UserId == userId).FirstOrDefaultAsync();
                    var isEventPresent = await weedb.EventModels.AsNoTracking().Where(x => x.EventId == contactModel.eventId && x.UserId == userId && x.IsActive == true).FirstOrDefaultAsync();
                    if (isEventPresent != null)
                    {
                        var isEventAttendee = await weedb.EventAttendees.AsNoTracking().Where(x => x.EventId == isEventPresent.EventId && x.ContactId == contactList.Id).FirstOrDefaultAsync();
                        if (isEventAttendee != null)
                        {
                            errorStatus.errorMessage = "Event Attendee Already Present";
                            return BadRequest(errorStatus);


                        }
                        OnePage.Models.Models.EventAttendee eventAttendee = new OnePage.Models.Models.EventAttendee();
                        eventAttendee.Id = Guid.NewGuid();
                        eventAttendee.Address = contactModel.Email;
                        eventAttendee.EventId = contactModel.eventId;
                        eventAttendee.UserId = userId;
                        eventAttendee.ContactId = contactList.Id;
                        eventAttendee.IsActive = true;
                        eventAttendee.Name = contactModel.FirstName + " " + contactModel.LastName;
                        eventAttendee.Created = DateTime.UtcNow;
                        eventAttendee.Modified = DateTime.UtcNow;
                        eventAttendee.IsAdhocUser = true;
                        weedb.EventAttendees.Add(eventAttendee);
                        await weedb.SaveChangesAsync();
                    }
                    else
                    {
                        errorStatus.errorMessage = "Event does not exists";
                        return BadRequest(errorStatus);

                    }
                    returnModel.contactList = contactList;
                    int status = 0;
                    returnModel.RequestStatus = status;
                    return Ok(returnModel);

                }
                else
                {
                    var contactDetail1 = new Contact();
                    bool checkForOrgPerson = false;
                    List<Contact> ContactList = new List<Contact>();
                    Guid contactId = Guid.Empty;
                    var isContactSocialExists = await weedb.ContactSocials.AsNoTracking().AnyAsync(x => x.Url.ToLower() == contactModel.LinkedinUrl.ToLower() && x.IsActive == true);
                    if (isContactSocialExists)
                    {
                        var contactSocial = await weedb.ContactSocials.FirstOrDefaultAsync(x => x.Url.ToLower() == contactModel.LinkedinUrl.ToLower() && x.IsActive == true);
                        if (contactSocial != null)
                        {
                            contactId = (Guid)contactSocial.ContactId;
                        }
                        // var query = from contact in weedb.Contacts
                        //             where (from cs in contactSOCIAL
                        //                    select cs.ContactId).Contains(contact.Id)
                        //             && contact.UserId == userId
                        //             select contact;
                        // if (query.Count() > 0)
                        // {
                        //     ContactList = query.ToList();
                        // }
                    }
                    if (contactId != Guid.Empty)
                    {
                        checkForOrgPerson = await db.OrgPeople.AsNoTracking().AnyAsync(w => w.ContactId == contactId);
                        contactDetail1 = await weedb.Contacts.AsNoTracking().Where(x => x.Id == contactId).FirstOrDefaultAsync();
                    }


                    var op = new OrgPerson();
                    var contactList = await weedb.Contacts.AsNoTracking().Where(x => x.Id == contactId).FirstOrDefaultAsync();
                    var isEventPresent = await weedb.EventModels.AsNoTracking().Where(x => x.EventId == contactModel.eventId && x.UserId == userId && x.IsActive == true).FirstOrDefaultAsync();
                    if (isEventPresent != null)
                    {
                        var isEventAttendee = await weedb.EventAttendees.AsNoTracking().Where(x => x.EventId == isEventPresent.EventId && x.ContactId == contactList.Id).FirstOrDefaultAsync();
                        if (isEventAttendee != null)
                        {
                            errorStatus.errorMessage = "Event Attendee Already Present";
                            return BadRequest(errorStatus);
                        }
                        OnePage.Models.Models.EventAttendee eventAttendee = new OnePage.Models.Models.EventAttendee();
                        eventAttendee.Id = Guid.NewGuid();
                        eventAttendee.Address = contactModel.Email;
                        eventAttendee.EventId = contactModel.eventId;
                        eventAttendee.UserId = userId;
                        eventAttendee.ContactId = contactList.Id;
                        eventAttendee.IsActive = true;
                        eventAttendee.Name = contactModel.FirstName + " " + contactModel.LastName;
                        eventAttendee.Created = DateTime.UtcNow;
                        eventAttendee.Modified = DateTime.UtcNow;
                        eventAttendee.IsAdhocUser = true;
                        weedb.EventAttendees.Add(eventAttendee);
                        await weedb.SaveChangesAsync();
                    }
                    else
                    {
                        errorStatus.errorMessage = "Event does not exists";
                        return BadRequest(errorStatus);
                    }
                    returnModel.contactList = contactList;
                    int status = 0;
                    returnModel.RequestStatus = status;
                    return Ok(returnModel);
                }
            }
        }

        public class ExsistsEventAttedndee
        {
            public Guid ContactId { get; set; }
            public string EventId { get; set; }
        }

        [HttpPost]
        [Route("6C2702C4")]
        public async Task<IActionResult> AddExistContactToEvent([FromBody] ExsistsEventAttedndee eventAttedndee)
        {
            var userId = _authService.GetUser(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized();
            }

            var contactDetail = await weedb.Contacts.AsNoTracking().Where(x => x.Id == eventAttedndee.ContactId).FirstOrDefaultAsync();
            if (contactDetail != null)
            {
                var isEventPresent = await weedb.EventModels.AsNoTracking()
                    .AnyAsync(x => x.EventId == eventAttedndee.EventId && x.UserId == userId && x.IsActive == true);
                if (isEventPresent)
                {
                    var isExists = await weedb.EventAttendees.AnyAsync(x =>
                            x.ContactId == eventAttedndee.ContactId && x.EventId == eventAttedndee.EventId);
                    if (!isExists)
                    {
                        OnePage.Models.Models.EventAttendee eventAttendee = new OnePage.Models.Models.EventAttendee();
                        eventAttendee.Id = Guid.NewGuid();
                        eventAttendee.Address = contactDetail.EmailDisplay;
                        eventAttendee.EventId = eventAttedndee.EventId;
                        eventAttendee.UserId = userId;
                        eventAttendee.ContactId = eventAttedndee.ContactId;
                        eventAttendee.IsActive = true;
                        eventAttendee.Name = contactDetail.FirstName + " " + contactDetail.LastName;
                        eventAttendee.Created = DateTime.UtcNow;
                        eventAttendee.Modified = DateTime.UtcNow;
                        eventAttendee.IsAdhocUser = true;
                        weedb.EventAttendees.Add(eventAttendee);
                        await weedb.SaveChangesAsync();
                        return Ok("Contact added to this event");
                    }
                    else
                    {
                        return BadRequest("This Contact is already present in the eventID");
                    }
                }
            }

            return Ok();
        }

        [Route("5FD4BE08")]
        [HttpPost]
        public async Task<IActionResult> UpdateTheContact([FromBody] AddContactModel contactModel)
        {
            var userId = _authService.GetUser(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized();
            }

            var isContact = weedb.Contacts
                .Where(x => x.EmailDisplay == contactModel.Email && x.UserId == userId && x.IsActive == true)
                .FirstOrDefault();
            if (isContact != null)
            {
                isContact.FirstName = contactModel.FirstName;
                isContact.LastName = contactModel.LastName;
                isContact.Modified = DateTime.UtcNow;
                isContact.IsActive = true;
                weedb.SaveChanges();
                OnePage.Models.Models.EventAttendee eventAttendee = new OnePage.Models.Models.EventAttendee();
                eventAttendee.Id = Guid.NewGuid();
                eventAttendee.Address = contactModel.Email;
                eventAttendee.EventId = contactModel.eventId;
                eventAttendee.UserId = userId;
                eventAttendee.ContactId = isContact.Id;
                eventAttendee.IsActive = true;
                eventAttendee.Name = contactModel.FirstName;
                eventAttendee.Created = DateTime.UtcNow;
                eventAttendee.Modified = DateTime.UtcNow;
                eventAttendee.IsAdhocUser = true;
                weedb.EventAttendees.Add(eventAttendee);
                weedb.SaveChanges();
            }

            return Ok("Updated SuccessFully");
        }

        [Route("541F8865")]
        [HttpPost]
        public async Task<IActionResult> UpdateSelectedCaLEndars([FromBody] List<CalendarModel> callList)
        {
            var userId = _authService.GetUser(Request);
            var token = _authService.GetToken(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized();
            }

            try
            {
                var eventOptionsBuilder = new DbContextOptionsBuilder<EventRDSContext>();
                eventOptionsBuilder.UseSqlServer(_configuration["DBConnections:WEEventRDSConnectionString"]);

                using (var db = new EventRDSContext(eventOptionsBuilder.Options))
                {
                    foreach (var dt in callList)
                    {
                        var calendarModelData = db.CalendarModels.Where(w => w.UserId == userId && w.RefId == dt.RefId)
                            .ToList();
                        if (calendarModelData != null)
                        {
                            foreach (var data in calendarModelData)
                            {
                                data.IsSelected = dt.IsSelected;
                                db.SaveChanges();
                            }
                        }
                    }
                }

                return Ok();
            }
            catch (Exception ex)
            {
                telemetryTracker.TrackException(ex, userId);
                return BadRequest(ex.StackTrace.ToString());
            }
        }

        [HttpPost]
        [Route("37979629")]
        public async Task<IActionResult> GetPeopleList(bool fetchOnline = false)
        {
            var userId = _authService.GetUser(Request);
            var token = _authService.GetToken(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized("Wrong Token");
            }

            try
            {
                if (fetchOnline)
                {
                    await calendarService.ProcessEvents(userId, token, "", true, false);
                }

                DateTimeOffset today = new DateTime(DateTime.Today.Year, DateTime.Today.Month, DateTime.Today.Day);
                DateTimeOffset tomorrow = today.AddHours(24);
                DateTimeOffset now = DateTimeOffset.Now;
                DateTimeOffset yesterday = today.AddHours(-24);
                long yLong = yesterday.ToUnixTimeSeconds();
                DateTimeOffset minusOne = today.AddMinutes(-1);
                long mLong = minusOne.ToUnixTimeSeconds();
                DateTimeOffset startLimit = now.Subtract(TimeSpan.FromMinutes(-15));
                DateTimeOffset endLimit = DateTimeOffset.Now.Subtract(TimeSpan.FromMinutes(1));
                var events = weedb.EventModels.Where(w => w.LEnd > yLong && w.UserId == userId
                                                                         && w.LStart < tomorrow.ToUnixTimeSeconds() &&
                                                                         w.IsActive == true && w.BTwo == true).ToList();

                // var events = await cosmosService.GetItems<EventModel>(userId, w => w.LEnd > yLong && w.UserId == userId
                // && w.LStart < tomorrow.ToUnixTimeSeconds() && w.IsActive && w.BTwo == true);

                List<AttendeesModel> returnList = new List<AttendeesModel>();
                var userDetails = wepdb.Users.Where(x => x.Id == userId).FirstOrDefault();
                var slice = userDetails.Email.Split('@');

                var orgUser = weodb.OrgUsers.Where(w =>
                        w.UserId == userId && w.Org.Name.ToLower() == slice[1].ToLower() && w.IsActive == true)
                    .Include(x => x.Org).ToList();
                var orgId = orgUser
                    .Where(x => x.Org.AppId.ToLower() == userDetails.AppId.ToString().ToLower() &&
                                x.Org.IsActive == true).Select(x => x.Org.Id).FirstOrDefault();
                List<OnePage.Models.Models.EventModel> tempEvents = new List<OnePage.Models.Models.EventModel>();
                DateTimeOffset endLimit2 = DateTimeOffset.Now.Subtract(TimeSpan.FromMinutes(1));
                bool todayEvents = events.Any(w =>
                    w.End > endLimit2 && w.Start < tomorrow && w.IsActive == true && w.BTwo == true);
                if (todayEvents)
                {
                    tempEvents =
                        (events.Where(w =>
                            w.End > endLimit2 && w.Start < tomorrow && w.IsActive == true && w.BTwo == true))
                        .OrderBy(w => w.Start).ToList();
                }

                if (tempEvents.Count > 0)
                {
                    foreach (var eventItem in tempEvents)
                    {
                        if (eventItem.EventAttendees.Count == 0)
                        {
                            var attendees = weedb.EventAttendees.Where(w =>
                                w.EventId == eventItem.EventId && w.UserId == userId &&
                                (w.IsUser == false || w.IsUser == null)).ToList();
                            //var attendees = await cosmosService.GetItems<EventAttendee>(userId, w => w.EventId == eventItem.EventId && w.IsUser == false);
                            eventItem.EventAttendees = attendees;
                        }

                        if (eventItem.EventAttendees.Count > 0)
                        {
                            foreach (var attItem in eventItem.EventAttendees)

                            {
                                var contactData = weedb.Contacts.Where(w => w.Id == attItem.ContactId).FirstOrDefault();
                                //var contactData = cosmosService.GetItem<Contact>(userId, w => w.ContactId == attItem.ContactId.ToString());
                                AttendeesModel attModel = new();

                                attModel.Address = attItem.Address;
                                attModel.ContactId = (Guid)attItem.ContactId;
                                attModel.Created = (DateTimeOffset)attItem.Created;
                                attModel.EventId = attItem.EventId;
                                attModel.id = attItem.Id;
                                attModel.IsActive = (bool)attItem.IsActive;
                                attModel.IsUser = (bool)attItem.IsUser;
                                attModel.Modified = (DateTimeOffset)attItem.Modified;
                                attModel.Name = contactData.FirstName + " " + contactData.LastName;
                                Guid cId = (Guid)attItem.ContactId;
                                var contactPersonExists = db.OrgPeople.Any(w =>
                                    w.OrgId == orgId && w.EmailId.ToLower() == attItem.Address.ToLower());
                                if (contactPersonExists)
                                {
                                    var cp = db.OrgPeople.First(w =>
                                        w.OrgId == orgId && w.EmailId.ToLower() == attItem.Address.ToLower());
                                    //if (cp.IsInsightsFound == false)
                                    //{
                                    //    attModel.Status = 4;
                                    //}

                                    //if (attModel.Status != 4)
                                    //{
                                    if (cp.IsRequested == false && cp.IsRedeemed == false &&
                                        cp.IsSearchComplete == false)
                                        attModel.Status = 0;
                                    else if (cp.IsRequested == true && cp.IsRedeemed == false &&
                                             cp.IsSearchComplete == true && cp.IsInsightsFound == true)
                                        attModel.Status = 2;
                                    else if (cp.IsRequested == true && cp.IsRedeemed == true &&
                                             cp.IsSearchComplete == true && cp.IsInsightsFound == true)
                                        attModel.Status = 3;
                                    else if (cp.IsRequested == true && cp.IsRedeemed == true &&
                                             cp.IsSearchComplete == true && cp.IsInsightsFound == false)
                                        attModel.Status = 4;
                                    else if (cp.IsRequested == true && cp.IsRedeemed == false &&
                                             cp.IsSearchComplete == true && cp.IsInsightsFound == false)
                                        attModel.Status = 4;
                                    //  }
                                }
                                //var contactPersonExists = db.ContactPeople.Any(w => w.ContactId == cId);
                                //if (contactPersonExists)
                                //{
                                //    var cp = db.ContactPeople.First(w => w.ContactId == cId);
                                //    attModel.PersonId = cp.PersonId.ToString();
                                //    if (cp.IsRequested == false && cp.IsRedeemed == false && cp.IsSearchComplete == false)
                                //        attModel.Status = 0;
                                //    else if (cp.IsRequested == true && cp.IsRedeemed == false && cp.IsSearchComplete == false)
                                //        attModel.Status = 1;
                                //    else if (cp.IsRequested == true && cp.IsRedeemed == false && cp.IsSearchComplete == true)
                                //        attModel.Status = 2;
                                //    else if (cp.IsRequested == true && cp.IsRedeemed == true && cp.IsSearchComplete == true)
                                //        attModel.Status = 3;
                                //    else if (cp.IsRequested == true && cp.IsRedeemed == true && cp.IsSearchComplete == true && (string.IsNullOrEmpty(attModel.PersonId) || cp.PersonId == Guid.Empty))
                                //        attModel.Status = 4;
                                //}
                                else
                                {
                                    attModel.Status = 0;
                                }

                                attModel.UserId = (Guid)attItem.UserId;
                                returnList.Add(attModel);
                            }
                        }
                    }
                }

                return Ok(returnList.Distinct().ToList());
            }
            catch (Exception ex)
            {
                telemetryTracker.TrackException(ex, userId);
                return BadRequest(ex);
            }
        }

        [HttpPost]
        [Route("748C8196")]
        public async Task<IActionResult> PostSocialDetails([FromBody] WebAppSocialModel model)
        {
            var userId = _authService.GetUser(Request);
            var token = _authService.GetToken(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized("Wrong Token");
            }

            try
            {
                //var user = wepdb.Users.First(w => w.Id == userId);
                if (model != null)
                {
                    Guid cid = Guid.Parse(model.ContactId);
                    if (!string.IsNullOrEmpty(model.LinkedIn))
                    {
                        var checkForExisting =
                            weedb.ContactSocials.Any(w => w.ContactId == cid && w.Url == model.LinkedIn);
                        if (!checkForExisting)
                        {
                            OnePage.Models.Models.ContactSocial cs1 = new OnePage.Models.Models.ContactSocial();
                            cs1.ContactId = Guid.Parse(model.ContactId);
                            cs1.Created = DateTime.UtcNow;
                            cs1.Id = Guid.NewGuid();
                            cs1.IsActive = true;
                            cs1.Modified = DateTime.UtcNow;
                            cs1.Name = "fab fa-linkedin-in";
                            cs1.SocialId = Guid.Parse("0ea37808-1506-43a3-9abd-6ff561931ff5");
                            cs1.Url = model.LinkedIn;
                            cs1.UserId = Guid.Parse(model.UserId);
                            cs1.ShowOrder = 0;
                            weedb.ContactSocials.Add(cs1);
                            weedb.SaveChanges();
                        }
                    }

                    if (!string.IsNullOrEmpty(model.Website))
                    {
                        var checkForExisting =
                            weedb.ContactSocials.Any(w => w.ContactId == cid && w.Url == model.Website);
                        if (!checkForExisting)
                        {
                            OnePage.Models.Models.ContactSocial cs2 = new OnePage.Models.Models.ContactSocial();
                            cs2.Name = "fal-globe";
                            cs2.ContactId = Guid.Parse(model.ContactId);
                            cs2.Created = DateTime.UtcNow;
                            cs2.Id = Guid.NewGuid();
                            cs2.IsActive = true;
                            cs2.Modified = DateTime.UtcNow;
                            cs2.SocialId = Guid.Parse("ac45263f-dfd8-4482-9987-478537598d26");
                            cs2.Url = model.Website;
                            cs2.UserId = Guid.Parse(model.UserId);
                            cs2.ShowOrder = 0;
                            weedb.ContactSocials.Add(cs2);
                            weedb.SaveChanges();
                        }
                    }

                    if (!string.IsNullOrEmpty(model.Twitter))
                    {
                        var checkForExisting =
                            weedb.ContactSocials.Any(w => w.ContactId == cid && w.Url == model.Twitter);
                        if (!checkForExisting)
                        {
                            OnePage.Models.Models.ContactSocial cs3 = new OnePage.Models.Models.ContactSocial();
                            cs3.Name = "fa-brands fa-x-twitter";
                            cs3.ContactId = Guid.Parse(model.ContactId);
                            cs3.Created = DateTime.UtcNow;
                            cs3.Id = Guid.NewGuid();
                            cs3.IsActive = true;
                            cs3.Modified = DateTime.UtcNow;
                            cs3.SocialId = Guid.Parse("4fb5f8a7-4639-489f-9de1-5888ee69d4bc");
                            cs3.Url = model.Twitter;
                            cs3.UserId = Guid.Parse(model.UserId);
                            cs3.ShowOrder = 0;
                            weedb.ContactSocials.Add(cs3);
                            weedb.SaveChanges();
                        }
                    }

                    if (!string.IsNullOrEmpty(model.Facebook))
                    {
                        var checkForExisting =
                            weedb.ContactSocials.Any(w => w.ContactId == cid && w.Url == model.Facebook);
                        if (!checkForExisting)
                        {
                            OnePage.Models.Models.ContactSocial cs4 = new OnePage.Models.Models.ContactSocial();
                            cs4.Name = "fab-facebook-square";
                            cs4.ContactId = Guid.Parse(model.ContactId);
                            cs4.Created = DateTime.UtcNow;
                            cs4.Id = Guid.NewGuid();
                            cs4.IsActive = true;
                            cs4.Modified = DateTime.UtcNow;
                            cs4.SocialId = Guid.Parse("0f9b27c2-d649-4fa2-a698-90f7a08482d2");
                            cs4.Url = model.Facebook;
                            cs4.UserId = Guid.Parse(model.UserId);
                            cs4.ShowOrder = 0;
                            weedb.ContactSocials.Add(cs4);
                            weedb.SaveChanges();
                        }
                    }

                    // update the contactPerson 
                    Guid cId = Guid.Parse(model.ContactId);
                    var cpExists = db.ContactPeople.Any(w => w.ContactId == cId);
                    if (cpExists)
                    {
                        var cp = db.ContactPeople.First(w => w.ContactId == cId);
                        cp.IsRequested = true;
                        cp.IsRedeemed = false;
                        cp.IsSearchComplete = false;
                        db.SaveChanges();
                    }

                    // add research in mongo
                    /* ResearchModel rModel = new ResearchModel();
                     rModel.Company = model.Company;
                     rModel.ContactId = model.ContactId;
                     rModel.Email = model.EmailId;
                     rModel.FirstName = model.FirstName;
                     rModel.Instance = 1;
                     rModel.IsPersonFound = false;
                     rModel.LastName = model.LastName;
                     rModel.Prioritiy = 1;
                     rModel.RequiredBy = DateTime.UtcNow.AddHours(24);
                     rModel.IsMatchFound = false;
                     rModel.RequiredEmail = true;
                     rModel.IsResearchInProgress = false;
                     rModel.RequiredLong = DateTimeOffset.UtcNow.AddHours(24).ToUnixTimeSeconds();
                     rModel.Role = model.Designation;
                     rModel.UserId = model.UserId;
                     rModel.IsTest = user.IsTestUser.Value;
                     var personExists = db.PersonEmails.Any(w => w.Email == model.EmailId && w.IsActive == true);
                     if (personExists)
                         rModel.IsMatchFound = true;

                     var exists = cosmosService.Exists<ResearchModel>(CommonData.AdminUserId, w => w.ContactId == model.ContactId && w.Email == model.EmailId && w.RequiredLong == rModel.RequiredLong);
                     if (!exists)
                     {
                         await cosmosService.InsertItem<ResearchModel>(CommonData.AdminUserId, rModel);
                     }
                     else
                     {
                         await cosmosService.UpsertItem<ResearchModel>(CommonData.AdminUserId, rModel);
                     }*/
                }


                return Ok();
            }
            catch (Exception ex)
            {
                telemetryTracker.TrackException(ex, userId);
                return BadRequest(ex);
            }
        }

        [HttpPost]
        [Route("8DCCDE2C")]
        public async Task<IActionResult> UpdateContactPerson(Guid ContactId)
        {
            Guid userId = Guid.Empty;
            try
            {
                var cpExists = db.ContactPeople.Any(w => w.ContactId == ContactId);
                if (cpExists)
                {
                    var cp = db.ContactPeople.First(w => w.ContactId == ContactId);
                    cp.IsRequested = true;
                    cp.IsRedeemed = false;
                    cp.IsSearchComplete = false;
                    db.SaveChanges();
                    userId = (Guid)cp.ReportedUserId;
                }

                return Ok();
            }
            catch (Exception ex)
            {
                telemetryTracker.TrackException(ex, userId);
                return BadRequest(ex.Message.ToString());
            }
        }

        async Task<List<OnePage.Models.Models.EventModel>> GetEvents(bool isNow,
            List<OnePage.Models.Models.EventModel> eventList, Guid userId, bool isTomorrow = false)
        {

            List<OnePage.Models.Models.EventModel> output = new List<OnePage.Models.Models.EventModel>();
            try
            {
                var eventRds = new DbContextOptionsBuilder<EventRDSContext>();
                eventRds.UseSqlServer(_configuration["DBConnections:WEEventRDSConnectionString"]);
                var peopleRds = new DbContextOptionsBuilder<PeopleContext>();
                peopleRds.UseSqlServer(_configuration["DBConnections:WEPeopleConnectionString"]);
                using (var wepdb1 = new PeopleContext(peopleRds.Options))
                {
                    using (var weedb1 = new EventRDSContext(eventRds.Options))
                    {
                        var timezoneSetting = wepdb1.UserSettings.AsNoTracking()
                        .First(x => x.SettingId == CommonData.TimeZoneSettingId && x.UserId == userId).Value;
                        int hourToAdd = 0;
                        int minutesToAdd = 0;

                        if (!string.IsNullOrEmpty(timezoneSetting))
                        {
                            var split = timezoneSetting.Split(":");
                            hourToAdd = int.Parse(split[0]);
                            minutesToAdd = int.Parse(split[1]);
                        }
                        TimeSpan timespan = new TimeSpan(hourToAdd, minutesToAdd, 0);
                        DateTimeOffset utcNow = DateTime.Now.Date.ToUniversalTime();
                        DateTimeOffset today = utcNow.ToOffset(timespan);

                        DateTimeOffset utcnow2 = DateTime.UtcNow;


                        DateTimeOffset endLimit = utcnow2.ToOffset(timespan).Subtract(TimeSpan.FromMinutes(1));

                        DateTimeOffset tomorrow = today.AddHours(24);
                        List<OnePage.Models.Models.EventModel> tempEvents = new List<OnePage.Models.Models.EventModel>();
                        if (!isNow)
                        {
                            if (isTomorrow)
                            {
                                today = today.AddDays(1);
                                tomorrow = tomorrow.AddDays(1);
                                endLimit = new DateTimeOffset(today.Year, today.Month, today.Day, 0, 0, 0, timespan)
                                    .AddMinutes(-1);
                            }

                            long endLimitLong = endLimit.ToUnixTimeSeconds();
                            long tomorrowLong = tomorrow.ToUnixTimeSeconds();
                            bool todayEvents = eventList.Any(w =>
                                w.LEnd > endLimitLong && w.LStart < tomorrowLong && w.IsActive == true && w.BTwo == true);
                            if (todayEvents)
                            {
                                tempEvents =
                                    (eventList.Where(w =>
                                        w.LEnd > endLimitLong && w.LStart < tomorrowLong && w.IsActive == true &&
                                        w.BTwo == true)).OrderBy(w => w.Start).ToList();
                            }
                        }
                        else
                        {
                            DateTimeOffset now = utcnow2.ToOffset(timespan);
                            DateTimeOffset startLimit = utcnow2.ToOffset(timespan).Subtract(TimeSpan.FromMinutes(1));
                            long LstartLimit = startLimit.ToUnixTimeSeconds();
                            long LendLimit = now.ToUnixTimeMilliseconds();
                            bool todayEvents = eventList.Any(w =>
                                w.LEnd > LendLimit && w.LStart < LstartLimit && w.IsActive == true && w.BTwo == true);
                            if (todayEvents)
                            {
                                tempEvents =
                                    (eventList.Where(w =>
                                        w.LEnd > LendLimit && w.LStart < LstartLimit && w.IsActive == true && w.BTwo == true))
                                    .OrderBy(w => w.Start).ToList();
                            }
                        }

                        DateTimeOffset minusOne = today.AddMinutes(-1);
                        DateTimeOffset plusOne = tomorrow.AddMinutes(1);
                        var allDayEvents = eventList.Where(w =>
                                w.IsAllDay == true && w.Start > minusOne && w.End < plusOne && w.IsActive == true &&
                                w.BTwo == true)
                            .ToList();
                        if (allDayEvents.Count > 0)
                        {
                            foreach (var item in allDayEvents.ToList())
                            {
                                if (tempEvents.Any(w => w.Id == item.Id))
                                    continue;
                                else
                                    tempEvents.Add(item);
                            }

                            tempEvents = tempEvents.OrderBy(w => w.Start).ToList();
                        }

                        foreach (var tempEvent in tempEvents.ToList())
                        {
                            var iscalendarSelect = await weedb1.CalendarModels.AsNoTracking().AnyAsync(x =>
                                x.IsSelected == true && x.RefId == tempEvent.CalId && x.UserId == userId);
                            if (iscalendarSelect)
                            {
                                if (!string.IsNullOrEmpty(tempEvent.Subject))
                                    tempEvent.Subject = tempEvent.Subject.Trim();
                                if (!string.IsNullOrEmpty(tempEvent.Desc))
                                {
                                    tempEvent.Desc = tempEvent.Desc.Trim();
                                }

                                var checkForBTwoExists = await weedb1.CalendarModels.AsNoTracking().AnyAsync(w => w.RefId == tempEvent.CalId);
                                if (checkForBTwoExists)
                                {
                                    var checkForBTwo = await weedb1.CalendarModels.AsNoTracking().FirstOrDefaultAsync(w => w.RefId == tempEvent.CalId);
                                    tempEvent.BTwo = checkForBTwo.IsSelected;
                                }

                                if (tempEvent.EventAttendees.Count == 0)
                                {
                                    var attendees = await weedb1.EventAttendees.AsNoTracking().Where(w =>
                                        w.EventId == tempEvent.EventId && (w.BOne == true || w.BOne == null) &&
                                        (w.IsUser == false || w.IsUser == null) && w.UserId == tempEvent.UserId).ToListAsync();
                                    tempEvent.EventAttendees = attendees;
                                }

                                if (tempEvent.EventAttachments.Count == 0)
                                {
                                    var attachment = await weedb1.EventAttachments.AsNoTracking().Where(x => x.EventId == tempEvent.EventId).ToListAsync();
                                    tempEvent.EventAttachments = attachment;
                                }

                                if (output.Any(w => w.EventId == tempEvent.EventId))
                                    continue;
                                output.Add(tempEvent);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                telemetryTracker.TrackException(ex, userId);
                return null;
            }

            return output;
        }

        [HttpPost]
        [Route("EF0EC9A1")]
        public async Task<IActionResult> GetTeamsMeetingTranscriptions(string eventId)
        {
            var userId = _authService.GetUser(Request);
            var token = _authService.GetToken(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized("Wrong Token");
            }

            try
            {
                if (!string.IsNullOrEmpty(eventId))
                {
                    var userProvider = wepdb.UserProviders.FirstOrDefault(w =>
                        w.UserId == userId && w.ProviderId == CommonData.MicrosoftProviderId && w.IsActive == true);
                    Provider provider = weddb.Providers
                        .Include(a => a.ProviderUrls).ThenInclude(w => w.Url).ThenInclude(w => w.Headers)
                        .Include(a => a.ProviderUrls).ThenInclude(w => w.Url).ThenInclude(w => w.Posts)
                        .First(w => w.Id == CommonData.MicrosoftProviderId);
                    var identifiersToSend = await providerService.GetIdentifiersForUserProvider(userProvider);

                    List<OnePage.Models.Models.PropertyModels.ContactLinkingModel.IdentifiersModel> customIdentifiers =
                        new()
                        {
                            new OnePage.Models.Models.PropertyModels.ContactLinkingModel.IdentifiersModel
                            {
                                IId = Guid.NewGuid(),
                                Name = "meetingid",
                                Value = eventId
                            }
                        };

                    Url transcriptUrl = provider.ProviderUrls.Where(w => w.Url.ShowOrder == 13100 && w.IsActive.Value)
                        .Select(w => w.Url).FirstOrDefault();
                    if (transcriptUrl != null)
                    {
                        var output = await providerService.MakeRequest<JToken>(transcriptUrl, userProvider,
                            identifiersToSend, customIdentifiers: customIdentifiers, WEToken: token);
                        if (output["value"] != null)
                        {
                            List<JToken> tTokens = output["value"].ToList();
                            if (tTokens.Count > 0)
                            {
                                foreach (var tToken in tTokens)
                                {
                                    try
                                    {
                                        var transcriptId = tToken.SelectToken("$.id").Value<string>();

                                        List<OnePage.Models.Models.PropertyModels.ContactLinkingModel.IdentifiersModel>
                                            customIdentifiers2 = new()
                                            {
                                                new OnePage.Models.Models.PropertyModels.ContactLinkingModel.
                                                    IdentifiersModel
                                                    {
                                                        IId = Guid.NewGuid(),
                                                        Name = "meetingId",
                                                        Value = eventId
                                                    },
                                                new OnePage.Models.Models.PropertyModels.ContactLinkingModel.
                                                    IdentifiersModel
                                                    {
                                                        IId = Guid.NewGuid(),
                                                        Name = "transcriptId",
                                                        Value = transcriptId
                                                    }
                                            };
                                        Url transcriptUrl2 = provider.ProviderUrls
                                            .Where(w => w.Url.ShowOrder == 13200 && w.IsActive.Value).Select(w => w.Url)
                                            .FirstOrDefault();
                                        var transcriptOutput = await providerService.MakeRequest<JToken>(transcriptUrl2,
                                            userProvider, identifiersToSend, customIdentifiers: customIdentifiers2,
                                            WEToken: token);
                                        if (transcriptOutput != null)
                                        {
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                    }
                                }
                            }
                        }
                    }
                }

                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.ToString());
            }
        }

        [HttpPost]
        [Route("72AB8C04")]
        public async Task<IActionResult> GetEventDetailWithCardIds(string eventId, string id, string meetinglink = "")
        {
            var userId = _authService.GetUser(Request);
            var token = _authService.GetToken(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized("Wrong Token");
            }

            try
            {
                EventDetailReturnModel detailReturnModel = new EventDetailReturnModel();
                OnePage.Models.Models.EventModel eventData = new OnePage.Models.Models.EventModel();
                //var eventDetailCache = await _redisCaching.GetAsync<EventModel>("eventDetail_" + userId + "_" + id);


                var user = await wepdb.Users.AsNoTracking().Where(x => x.Id == userId).FirstOrDefaultAsync();
                var slice = user?.Email.Split('@');
                var isFreeDomain = await weddb.FreeDomains.AsNoTracking().AnyAsync(x => x.DomainName.ToLower() == slice[1].ToLower());

                try
                {
                    TrackMetadata trackMetadata = new TrackMetadata();
                    var deviceId = await wepdb.UserDevices.AsNoTracking().Where(x => x.UserId == userId && x.IsActive == true)
                        .Select(x => x.Id).FirstOrDefaultAsync();
                    trackMetadata.user_id = userId.ToString();
                    trackMetadata.action = "EventDetail";
                    trackMetadata.deviceId = deviceId.ToString();
                    trackMetadata.loginWebsite = "WebApp";
                    trackMetadata.isFreeDomain = isFreeDomain.ToString();

                    if (user?.IsTestUser != null)
                    {
                        trackMetadata.isTestUser = user.IsTestUser.ToString();
                    }
                    metricsService.TrackUsuage(trackMetadata, "events");
                }
                catch (Exception ex)
                {

                }


                if (!string.IsNullOrEmpty(meetinglink))
                {
                    var eventDataExists = await weedb.EventModels.AsNoTracking().AnyAsync(w => w.OnlineMeetingLink.Contains(meetinglink));
                    if (eventDataExists)
                    {
                        eventData = await weedb.EventModels.AsNoTracking().Where(w => w.OnlineMeetingLink.Contains(meetinglink))
                            .FirstOrDefaultAsync();
                    }
                    else if (!string.IsNullOrEmpty(id))
                    {
                        Guid eid = Guid.Parse(id);
                        var idExists = await weedb.EventModels.AsNoTracking().AnyAsync(w => w.Id == eid && w.UserId == userId);
                        if (idExists)
                        {
                            eventData = await weedb.EventModels.AsNoTracking().Where(w => w.Id == eid && w.UserId == userId).FirstOrDefaultAsync();
                        }
                        else
                        {
                            var meetingIdExists = await weedb.EventModels.AsNoTracking().AnyAsync(w => w.HTMLBody.Contains(eventId) && w.UserId == userId);
                            if (meetingIdExists)
                            {
                                eventData = await weedb.EventModels.AsNoTracking().Where(w => w.HTMLBody.Contains(eventId) && w.UserId == userId).FirstOrDefaultAsync();
                            }
                        }
                    }
                }
                else if (!string.IsNullOrEmpty(id))
                {
                    Guid eid = Guid.Parse(id);
                    var eventDataExists = await weedb.EventModels.AsNoTracking().AnyAsync(w => w.Id == eid);
                    if (eventDataExists)
                    {
                        eventData = await weedb.EventModels.AsNoTracking().Where(w => w.Id == eid).FirstOrDefaultAsync();
                    }
                    else
                    {
                        var meetingIdExists = await weedb.EventModels.AsNoTracking().AnyAsync(w => w.EventId.Contains(eventId) && w.UserId == userId);
                        if (meetingIdExists)
                        {
                            eventData = await weedb.EventModels.AsNoTracking().Where(w => w.EventId.Contains(eventId) && w.UserId == userId).FirstOrDefaultAsync();
                        }
                    }
                }
                else
                {
                    var meetingIdExists =
                        await weedb.EventModels.AsNoTracking().AnyAsync(w => w.HTMLBody.Contains(eventId) && w.UserId == userId);
                    if (meetingIdExists)
                    {
                        eventData = await weedb.EventModels.AsNoTracking().Where(w => w.HTMLBody.Contains(eventId) && w.UserId == userId)
                            .FirstOrDefaultAsync();
                    }
                }

                List<string> contactId = new List<string>();
                List<EventAttendee> eventAttendees = new List<EventAttendee>();
                if (!string.IsNullOrEmpty(eventData.EventId))
                {
                    if (eventData.EventAttendees.Count == 0)
                    {
                        var attendees = await weedb.EventAttendees.AsNoTracking().Where(w =>
                            w.EventId == eventData.EventId && w.UserId == userId && (w.IsActive == true || w.IsActive == null) &&
                            (w.IsUser == false || w.IsUser == null)).ToListAsync();
                        var emptyAddressAttendees = attendees.Where(w => string.IsNullOrEmpty(w.Address)).ToList();
                        var uniqueAttendees = attendees.DistinctBy(x => x.Address).ToList();
                        eventData.EventAttendees.AddRange(uniqueAttendees);
                        foreach (var emptyAddressAttendee in emptyAddressAttendees)
                        {
                            if (!eventData.EventAttendees.Any(x => x.Id == emptyAddressAttendee.Id))
                            {
                                eventData.EventAttendees.Add(emptyAddressAttendee);
                            }
                        }
                    }

                    // // insert in call table for recall.ai playbooks
                    // if (user.Id == Guid.Parse("61EEC2BE-2784-4D57-8A11-D567D338F540") || user.Id == Guid.Parse("fbe1b835-7846-4f15-b700-abc21717d520"))
                    // {
                    //     var checkForExistingCall = await db.Calls.AnyAsync(w => w.EventId.ToLower() == eventData.Id.ToString().ToLower() && w.MeetingId == eventData.EventId);
                    //     if (!checkForExistingCall)
                    //     {
                    //         Calls call = new Calls();
                    //         call.Id = Guid.NewGuid();
                    //         call.EventId = eventData.Id.ToString();
                    //         call.MeetingId = eventData.EventId;
                    //         call.PlaybookId = Guid.Parse("D182AD89-80FC-410F-A0CE-8FA50FC7F16B"); // for demo
                    //         db.Calls.Add(call);
                    //         await db.SaveChangesAsync();
                    //     }
                    // }
                }
                else
                {
                    await calendarService.ProcessEvents(userId, token, "", true, true);
                    if (!string.IsNullOrEmpty(meetinglink))
                    {
                        var eventDataExists = await weedb.EventModels.AsNoTracking().AnyAsync(w => w.OnlineMeetingLink.Contains(meetinglink));
                        if (eventDataExists)
                        {
                            eventData = await weedb.EventModels.AsNoTracking().Where(w => w.OnlineMeetingLink.Contains(meetinglink))
                                .FirstOrDefaultAsync();
                        }
                        else if (!string.IsNullOrEmpty(id))
                        {
                            Guid eid = Guid.Parse(id);
                            var idExists = await weedb.EventModels.AsNoTracking().AnyAsync(w => w.Id == eid);
                            if (idExists)
                            {
                                eventData = await weedb.EventModels.AsNoTracking().Where(w => w.Id == eid).FirstOrDefaultAsync();
                            }
                            else
                            {
                                var meetingIdExists = await weedb.EventModels.AsNoTracking().AnyAsync(w => w.HTMLBody.Contains(eventId));
                                if (meetingIdExists)
                                {
                                    eventData = await weedb.EventModels.AsNoTracking().Where(w => w.HTMLBody.Contains(eventId))
                                        .FirstOrDefaultAsync();
                                }
                            }
                        }
                    }
                    else
                    {
                        Guid eid = Guid.Parse(id);
                        var eventDataExists2 = await weedb.EventModels.AsNoTracking().AnyAsync(w => w.Id == eid);
                        ;
                        if (eventDataExists2)
                        {
                            eventData = await weedb.EventModels.AsNoTracking().Where(w => w.Id == eid).FirstOrDefaultAsync();
                        }
                        else
                        {
                            var meetingIdExists2 = await weedb.EventModels.AsNoTracking().AnyAsync(w => w.HTMLBody.Contains(eventId));
                            if (meetingIdExists2)
                            {
                                eventData = await weedb.EventModels.AsNoTracking().Where(w => w.HTMLBody.Contains(eventId)).FirstOrDefaultAsync();
                            }
                        }
                    }
                }

                var orgUser = await weodb.OrgUsers.AsNoTracking().Where(w =>
                        w.UserId == userId && w.Org.Name.ToLower() == slice[1].ToLower() && w.IsActive == true)
                    .Include(x => x.Org).ToListAsync();
                var orgId = orgUser
                    .Where(x => x.Org.AppId.ToLower() == user.AppId.ToString().ToLower() &&
                                x.Org.IsActive == true).Select(x => x.Org.Id).FirstOrDefault();
                if (eventData != null)
                {
                    if (eventData.EventAttendees.Count == 0)
                    {
                        var attendees = await weedb.EventAttendees.AsNoTracking().Where(w =>
                                w.EventId == eventData.EventId && w.UserId == userId && (w.IsActive == true || w.IsActive == null) &&
                                (w.IsUser == false || w.IsUser == null))
                            .ToListAsync();
                        var emptyAddressAttendees = attendees.Where(w => string.IsNullOrEmpty(w.Address)).ToList();
                        var uniqueAttendees = attendees.DistinctBy(x => x.Address).ToList();
                        eventData.EventAttendees.AddRange(uniqueAttendees);
                        eventData.EventAttendees.AddRange(emptyAddressAttendees);
                    }

                    foreach (var attItem in eventData.EventAttendees.ToList())
                    {
                        var stringReplace1 = attItem.Address.Replace("|", "");
                        var contactData = await weedb.Contacts.AsNoTracking().Where(w => w.Id == attItem.ContactId).FirstOrDefaultAsync();
                        if (string.IsNullOrEmpty(attItem.Name))
                        {
                            attItem.Name = contactData.FirstName + " " + contactData.LastName;
                        }
                        if (!isFreeDomain)
                        {
                            var contactPersonExists = await db.OrgPeople.AsNoTracking().AnyAsync(w => w.OrgId == orgId && w.EmailId.ToLower() == stringReplace1.ToLower());
                            if (contactPersonExists)
                            {

                                var cp = await db.OrgPeople.AsNoTracking().FirstOrDefaultAsync(w => w.OrgId == orgId && w.EmailId.ToLower() == stringReplace1.ToLower());
                                var status = _requestResearchService.GetResearchStatus(cp);
                                attItem.Status = status.ToString();
                                attItem.profileImage = CommonData.S3BucketUrlForPic + cp.PersonId;
                                attItem.company = contactData.Company;
                                attItem.role = contactData.Desig;
                            }

                        }
                        else
                        {
                            var cp = await db.ContactPeople.AsNoTracking().FirstOrDefaultAsync(w => w.ContactId == contactData.Id);
                            if (cp != null)
                            {
                                var status = _requestResearchService.GetResearchStatus(cp);
                                attItem.Status = status.ToString();
                                attItem.profileImage = CommonData.S3BucketUrlForPic + cp.PersonId;
                                attItem.company = contactData.Company;
                                attItem.role = contactData.Desig;
                            }
                            else
                            {
                                attItem.company = contactData.Company;
                                attItem.role = contactData.Desig;
                                attItem.Status = "0";
                            }
                        }
                    }
                }
                //await _redisCaching.SaveAsync("eventDetail_" + userId + "_" + id, eventData, TimeSpan.FromDays(5));

                var eventDetailCards = await weddb.Cards.AsNoTracking().Where(w => w.IsActive == true && w.AppId.Contains(CommonData.GlobalAppId.ToString()) && w.PageType == 3).ToListAsync();
                List<EventDetailWithCardIdsModel> eventDataList = new List<EventDetailWithCardIdsModel>();
                foreach (var item in eventDetailCards)
                {
                    EventDetailWithCardIdsModel eventItem = new EventDetailWithCardIdsModel();
                    eventItem.CardId = item.Id;
                    switch (item.Id.ToString().ToUpper())
                    {
                        case "A7273D52-8DEA-415B-A82D-23A1FEEC71C3": // event card
                            eventItem.CardContent = eventData;
                            break;
                        case "BBAB9DC7-E15D-481C-9788-7D671431F9E1": // Attendees
                            eventItem.CardContent = eventData.EventAttendees.ToList();
                            break;
                        case "51234EA9-2DE0-40E8-8FD4-364A90F011DA": // Event Description
                            eventItem.CardContent = eventData.Desc;
                            break;
                        case "3F4FFDC5-FF22-409F-A7C8-A623F81B1F5B": // Notes
                            eventItem.CardContent = eventData.Notes;
                            break;
                        case "0A1F8F73-B796-488B-B5EC-C5E2D6A5CF15": // Links
                            eventItem.CardContent = eventData.HTMLBody;
                            break;
                        case "E485F702-BBD0-4749-9870-B9DC4AB51845": // Organisations
                            eventItem.CardContent = eventData.EventAttendees.ToList();
                            break;
                        case "EF9D6123-2EF5-4195-BA04-B2A2C053B92B": // Join Meeting
                            eventItem.CardContent = eventData.HTMLBody;
                            break;
                        case "9306983E-42C8-45F0-A591-138B617B4F74"://Location
                            eventItem.CardContent = eventData.Location;
                            break;
                        default:
                            break;
                    }

                    eventDataList.Add(eventItem);
                }

                EventDataReturnModel returnModel = new EventDataReturnModel();
                returnModel.EventId = eventData.Id;
                returnModel.HtmlBody = eventData.HTMLBody;
                returnModel.Desc = eventData.Desc;
                returnModel.DataList = eventDataList.ToList();
                return Ok(returnModel);
            }
            catch (Exception ex)
            {
                telemetryTracker.TrackException(ex, userId);
                BadRequest("Error Occured" + ex.Message);
            }

            return BadRequest("Error Occured");
        }

        public class EventDetailReturnModel
        {
            public EventModel EventData { get; set; }
            public List<AttendeesModel> attendeesModels { get; set; } = new();
        }

        public class EventDataReturnModel
        {
            public Guid EventId { get; set; }
            public string Desc { get; set; }
            public List<EventDetailWithCardIdsModel> DataList { get; set; } = new List<EventDetailWithCardIdsModel>();
            public string HtmlBody { get; set; }
        }

        public class EventDetailWithCardIdsModel
        {
            public Guid CardId { get; set; }
            public object CardContent { get; set; }
        }

        [HttpPost]
        [Route("6EE0753A")]
        public async Task<IActionResult> GetEventAttachment(Guid eventId)
        {
            var userId = _authService.GetUser(Request);
            var tokenId = _authService.GetToken(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized();
            }

            var eventData = weedb.EventModels.Where(x => x.Id == eventId).FirstOrDefault();
            if (eventData != null)
            {
                var EventAttachments =
                    await calendarService.GetAttachments(userId, eventData, (Guid)eventData.ProviderId, tokenId);
                return Ok(EventAttachments);
            }
            else
            {
                return BadRequest("Event is not present");
            }
        }
        public class ZohoAttachmentDetails
        {


            public Guid id { get; set; }

            public string attachId { get; set; }

        }
        public class EventIdWithCallendarId
        {
            public string eventId { get; set; }

            public string calendarId { get; set; }
        }
        public class ZohoAttachmentReturnModel
        {
            public string url { get; set; }

            public string accessToken { get; set; }
        }
        [HttpPost]
        [Route("DCDB33F3")]
        public async Task<IActionResult> GetZohoAttachment([FromBody] ZohoAttachmentDetails payload)
        {
            var userId = _authService.GetUser(Request);
            var tokenId = _authService.GetToken(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized();

            }
            try
            {
                ZohoAttachmentReturnModel returnModel = new ZohoAttachmentReturnModel();
                var eventIdWithCalId = await weedb.EventModels.AsNoTracking().Where(x => x.Id == payload.id).Select(x => new EventIdWithCallendarId
                {
                    eventId = x.EventId,
                    calendarId = x.CalId
                }).FirstOrDefaultAsync();
                var Url = await weddb.ProviderUrls.AsNoTracking().Where(x => x.Url.ShowOrder == 12600 && x.ProviderId == CommonData.ZohoCalendarProviderId).Select(x => x.Url.Name).FirstOrDefaultAsync();
                returnModel.url = await ReplaceZohoAttach(eventIdWithCalId.eventId, eventIdWithCalId.calendarId, payload.attachId, Url);
                await providerService.GenerateAccessRefreshTokensForProviders(userId, tokenId
                    , providerId: CommonData.ZohoCalendarId);
                var accessToken = await wepdb.UserProviderIdentifiers.AsNoTracking().Where(x => x.UserProvider.ProviderId == CommonData.ZohoCalendarProviderId && x.UserProvider.UserId == userId && x.UserProvider.IsActive == true && x.IdentifierId == CommonData.ZohoAccessToken).Select(x => x.Value).FirstOrDefaultAsync();
                returnModel.accessToken = accessToken;
                return Ok(returnModel);


            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }

        }
        public async Task<string> ReplaceZohoAttach(string eventId, string calendarid, string fileId, string url)
        {

            return url.Replace("{eventid}", eventId).Replace("{calendarid}", calendarid).Replace("{fileid}", fileId);
        }


        public class ReadSummaryAi
        {
            public string Summary { get; set; }
        }
        public class ReadSummaryReturnModel
        {
            public string summary { get; set; }
            public List<ActionItem> action_items { get; set; }
            public List<KeyQuestion> key_questions { get; set; }
            public List<Topic> topics { get; set; }
            public List<ChapterSummary> chapter_summaries { get; set; }
        }
        [HttpPost]
        [Route("D4A43571")]

        public async Task<IActionResult> GetReadAIData(string id)
        {
            var userId = _authService.GetUser(Request);
            var tokenId = _authService.GetToken(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized();
            }
            try
            {
                List<EventAttachmentCardModel> returnModel = new List<EventAttachmentCardModel>();
                string Summary = "";
                if (!string.IsNullOrEmpty(id))
                {
                    var eventData = await weedb.EventModels.Where(x => x.Id == Guid.Parse(id)).FirstOrDefaultAsync();
                    if (eventData != null)
                    {
                        var ReadAiData = await weedb.ReadAISummarys.AsNoTracking().Where(x => x.EventId == eventData.EventId && x.UserId == userId).FirstOrDefaultAsync();
                        if (ReadAiData == null)
                        {

                            var timezoneSetting = wepdb.UserSettings.AsNoTracking()
                   .FirstAsync(x => x.SettingId == CommonData.TimeZoneSettingId && x.UserId == userId).Result.Value;
                            int hourToAdd = 0;
                            int minutesToAdd = 0;

                            if (!string.IsNullOrEmpty(timezoneSetting))
                            {
                                var split = timezoneSetting.Split(":");
                                hourToAdd = int.Parse(split[0]);
                                minutesToAdd = int.Parse(split[1]);
                            }
                            TimeSpan timespan = new TimeSpan(hourToAdd, minutesToAdd, 0);
                            eventData.Start = eventData.Start.Value.ToOffset(timespan);
                            eventData.End = eventData.End.Value.ToOffset(timespan);
                            var Lstart = eventData.Start.Value.AddHours(-2).ToUnixTimeSeconds();
                            var Lend = eventData.End.Value.AddHours(2).ToUnixTimeSeconds();
                            ReadAISummary ReadAiDataWithoutEvent = new ReadAISummary();
                            var ReadAiDataWithoutEventList = await weedb.ReadAISummarys.AsNoTracking().Where(x => x.UserId == userId && x.EventName.ToLower() == eventData.Subject.ToLower()).OrderByDescending(x => x.CreatedDate).ToListAsync();
                            foreach (var readAiDetails in ReadAiDataWithoutEventList)
                            {
                                DateTimeOffset StartRead = DateTimeOffset.Parse(readAiDetails.EventStart.ToString());
                                DateTimeOffset EndRead = DateTimeOffset.Parse(readAiDetails.EventEnd.ToString());
                                long LStartRead = StartRead.AddHours(hourToAdd).AddMinutes(minutesToAdd).ToUnixTimeSeconds();
                                long LEndRead = EndRead.AddHours(hourToAdd).AddMinutes(minutesToAdd).ToUnixTimeSeconds();
                                if (LStartRead >= Lstart && LEndRead <= Lend)
                                {
                                    ReadAiDataWithoutEvent = readAiDetails;
                                    readAiDetails.EventId = eventData.EventId;
                                    await weedb.SaveChangesAsync();
                                    ReadAiData = readAiDetails;

                                }
                            }


                            Summary = ReadAiDataWithoutEvent.Summary;
                            ReadSummaryReturnModel readSummaryReturnModel2 = new ReadSummaryReturnModel();
                            if (ReadAiData != null)
                            {
                                if (ReadAiData.Transcript != null)
                                {
                                    var transcriptRemainData2 = Newtonsoft.Json.JsonConvert.DeserializeObject<ReadAITranscript>(ReadAiData.Transcript);
                                    readSummaryReturnModel2.chapter_summaries = transcriptRemainData2.chapter_summaries;
                                    readSummaryReturnModel2.action_items = transcriptRemainData2.action_items;
                                    readSummaryReturnModel2.topics = transcriptRemainData2.topics;
                                    readSummaryReturnModel2.key_questions = transcriptRemainData2.key_questions;
                                }
                                readSummaryReturnModel2.summary = ReadAiData.Summary;
                            }
                            EventAttachmentCardModel cardModel2 = new EventAttachmentCardModel();
                            cardModel2.CardId = Guid.Parse("********-2660-4E8A-8A00-83BF09DD062E");
                            cardModel2.CardContent = readSummaryReturnModel2;
                            returnModel.Add(cardModel2);
                            return Ok(returnModel);


                        }
                        ReadSummaryReturnModel readSummaryReturnModel = new ReadSummaryReturnModel();
                        if (ReadAiData.Transcript != null)
                        {
                            var transcriptRemainData2 = Newtonsoft.Json.JsonConvert.DeserializeObject<ReadAITranscript>(ReadAiData.Transcript);
                            readSummaryReturnModel.chapter_summaries = transcriptRemainData2.chapter_summaries;
                            readSummaryReturnModel.action_items = transcriptRemainData2.action_items;
                            readSummaryReturnModel.topics = transcriptRemainData2.topics;
                            readSummaryReturnModel.key_questions = transcriptRemainData2.key_questions;

                        }
                        readSummaryReturnModel.summary = ReadAiData.Summary;
                        EventAttachmentCardModel cardModel = new EventAttachmentCardModel();
                        cardModel.CardId = Guid.Parse("********-2660-4E8A-8A00-83BF09DD062E");
                        cardModel.CardContent = readSummaryReturnModel;
                        returnModel.Add(cardModel);
                        return Ok(returnModel);

                        //Summary = ReadAiData.Summary;
                    }

                }
                else
                {
                    return BadRequest("Event Id is Empty");
                }
                return Ok(returnModel);

            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);

            }

        }
        [HttpPost]
        [Route("05DC2A16")]
        public async Task<IActionResult> GetEventAttachmentWithCardIds(Guid eventId)
        {
            var userId = _authService.GetUser(Request);
            var tokenId = _authService.GetToken(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized();
            }

            List<EventAttachmentCardModel> returnCardList = new List<EventAttachmentCardModel>();
            var eventData = weedb.EventModels.Where(x => x.Id == eventId).FirstOrDefault();
            if (eventData != null)
            {
                var EventAttachments =
                    await calendarService.GetAttachments(userId, eventData, (Guid)eventData.ProviderId, tokenId);
                EventAttachmentCardModel cardModel = new EventAttachmentCardModel();
                cardModel.CardId = Guid.Parse("A072C8BC-DEF7-417F-A69F-15E19ED65192");
                cardModel.CardContent = EventAttachments;
                if (eventData.ProviderId == CommonData.ZohoCalendarProviderId)
                {
                    cardModel.IsApiShouldBeCalled = true;
                }
                returnCardList.Add(cardModel);
                return Ok(returnCardList);
            }
            else
            {
                return BadRequest("Event is not present");
            }
        }

        public class EventAttachmentCardModel
        {
            public Guid CardId { get; set; }
            public object CardContent { get; set; }

            public bool IsApiShouldBeCalled { get; set; } = false;
        }

        [HttpPost]
        [Route("C3C67A09")]

        public async Task<IActionResult> GetEventFireFiles(string eventId)
        {
            var userId = _authService.GetUser(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized();
            }

            try
            {
                var eventData = weedb.EventModels.Where(x => x.Id == Guid.Parse(eventId)).FirstOrDefault();
                if (eventData != null)
                {
                    // var firefliesUserProviderExists = wepdb.UserProviders.Any(w => w.UserId == userId && w.ProviderId == CommonData.FirefliesProviderId && w.IsActive == true);
                    var x = await _userProviderCaching.GetUserProvidersFromRedisOrDbAsyncWithUserId(userId,
                        FirefliesProviderId);
                    var firefliesUserProviderExists = x != null;
                    if (firefliesUserProviderExists)
                    {
                        var firefliesData =
                            await transcriptService.GetTranscripts(eventData.EventId, userId, eventData);
                        if (firefliesData != null)
                        {
                            //var jsonData = JsonConvert.DeserializeObject<FireFliesReturnModel2>(existingData.TranscriptData);
                            return Ok(firefliesData);
                        }
                        else
                        {
                            return Ok("Data not found");
                        }
                    }
                }
                else
                {
                    return BadRequest("No Such Event Present");
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                return BadRequest(ex);
            }
        }

        [HttpPost]
        [Route("BCC6DC7C")]
        public async Task<IActionResult> GetEventFireFilesWithCardIds(string eventId)
        {
            var userId = _authService.GetUser(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized();
            }

            try
            {
                var eventData = weedb.EventModels.Where(x => x.Id == Guid.Parse(eventId)).FirstOrDefault();
                if (eventData != null)
                {
                    List<EventAttachmentCardModel> returnCardList = new List<EventAttachmentCardModel>();
                    var firefliesUserProviderExists = wepdb.UserProviders.Any(w =>
                        w.UserId == userId && w.ProviderId == CommonData.FirefliesProviderId && w.IsActive == true);
                    if (firefliesUserProviderExists)
                    {
                        var firefliesData =
                            await transcriptService.GetTranscripts(eventData.EventId, userId, eventData);

                        //var jsonData = JsonConvert.DeserializeObject<FireFliesReturnModel2>(existingData.TranscriptData);
                        EventAttachmentCardModel cardModel = new EventAttachmentCardModel();
                        cardModel.CardId = Guid.Parse("********-2660-4E8A-8A00-83BF09DD062F");
                        cardModel.CardContent = firefliesData;
                        returnCardList.Add(cardModel);
                        return Ok(returnCardList);
                    }
                }
                else
                {
                    return BadRequest("No Such Event Present");
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                return BadRequest(ex);
            }
        }

        [HttpPost]
        [Route("86C95480")]
        public async Task<IActionResult> GetMoreEventData(int mode)
        {
            var userId = _authService.GetUser(Request);
            var token = _authService.GetToken(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized("Wrong Token");
            }

            try
            {
                var timezoneSetting = wepdb.UserSettings.AsNoTracking()
                       .FirstAsync(x => x.SettingId == CommonData.TimeZoneSettingId && x.UserId == userId).Result.Value;
                int hourToAdd = 0;
                int minutesToAdd = 0;

                if (!string.IsNullOrEmpty(timezoneSetting))
                {
                    var split = timezoneSetting.Split(":");
                    hourToAdd = int.Parse(split[0]);
                    minutesToAdd = int.Parse(split[1]);
                }
                TimeSpan timespan = new TimeSpan(hourToAdd, minutesToAdd, 0);
                DateTimeOffset utcNow = DateTime.UtcNow.Date;
                DateTimeOffset today = utcNow.ToOffset(timespan);

                DateTimeOffset tomorrow = today.AddHours(24);
                DateTimeOffset yesterday = today.AddHours(-24);
                long yLong = yesterday.ToUnixTimeSeconds();
                DateTimeOffset minusOne = today.AddMinutes(-1);
                long mLong = minusOne.ToUnixTimeSeconds();
                long tomorrowSecs = tomorrow.AddHours(24).ToUnixTimeSeconds();
                var mLong2 = DateTimeOffset.UtcNow.ToOffset(timespan).AddMinutes(-1).ToUnixTimeSeconds();

                var yEvents = await weedb.EventModels.AsNoTracking().Where(w => w.UserId == userId && w.IsActive == true && w.BTwo == true && w.LStart > yLong && w.LEnd <= mLong2)
                    .ToListAsync();
                //var events = await cosmosService.GetItems<EventModel>(userId, w => w.UserId == userId && w.IsActive && w.BTwo == true);

                switch (mode)
                {
                    case 1:
                        break;
                    case 2: // reminders
                            // var todayEvents = events.Where(w =>
                            //   w.End > endLimit && w.Start < tomorrow && w.IsActive == true && w.BTwo == true).ToList();
                            // var yEvents = events.Where(w =>
                            //     w.IsActive == true && w.BTwo == true && w.LStart > yLong && w.LEnd <= mLong).ToList();
                        List<OnePage.Models.Models.EventModel> reminderList =
                            new List<OnePage.Models.Models.EventModel>(yEvents.ToList());
                        return Ok(reminderList.Where(w => w.Notes == "" || w.Notes == null).OrderBy(w => w.Start)
                            .ToList());
                    default:
                        break;
                }

                return Ok();
            }
            catch (Exception ex)
            {
                telemetryTracker.TrackException(ex, userId);
                BadRequest("Error Occured" + ex.Message);
            }

            return BadRequest("Error Occured");
        }

        [HttpPost]
        [Route("B1C267DC")]
        public async Task<IActionResult> PostEventNote([FromBody] OnePage.Models.Models.EventModel model)
        {
            var userId = _authService.GetUser(Request);
            var token = _authService.GetToken(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized("Wrong Token");
            }

            try
            {
                if (model != null)
                {
                    TrackMetadata trackMetadata = new TrackMetadata();
                    trackMetadata.user_id = userId.ToString();
                    trackMetadata.action = "CRMActivity";
                    trackMetadata.deviceId = wepdb.UserDevices.Where(x => x.UserId == userId && x.IsActive == true)
                        .Select(x => x.Id).FirstOrDefault().ToString();
                    trackMetadata.loginWebsite = "WebApp";
                    metricsService.TrackUsuage(trackMetadata, "events");


                    var eventModel = weedb.EventModels.FirstOrDefault(w => w.Id == model.Id);
                    eventModel.Notes = model.Notes;
                    eventModel.Modified = DateTime.UtcNow;
                    weedb.SaveChanges();
                    // await cosmosService.UpsertItem(userId, model);
                }

                return Ok();
            }
            catch (Exception ex)
            {
                telemetryTracker.TrackException(ex, userId);
                BadRequest("Error Occured" + ex.Message);
            }

            return BadRequest("Error Occured");
        }

        public class EventNoteCustomModel
        {
            public Guid Id { get; set; }
            public string Notes { get; set; }
        }

        [HttpPost]
        [Route("E326ED21")]
        public async Task<IActionResult> PostEventNote2([FromBody] EventNoteCustomModel model)
        {
            var userId = _authService.GetUser(Request);
            var token = _authService.GetToken(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized("Wrong Token");
            }

            try
            {
                if (model != null)
                {
                    TrackMetadata trackMetadata = new TrackMetadata();
                    trackMetadata.user_id = userId.ToString();
                    trackMetadata.action = "CRMActivity";
                    trackMetadata.deviceId = wepdb.UserDevices.Where(x => x.UserId == userId && x.IsActive == true)
                        .Select(x => x.Id).FirstOrDefault().ToString();
                    metricsService.TrackUsuage(trackMetadata, "events");


                    var eventModel = weedb.EventModels.FirstOrDefault(w => w.Id == model.Id);
                    eventModel.Notes = model.Notes;
                    eventModel.Modified = DateTime.UtcNow;
                    weedb.SaveChanges();
                    // await cosmosService.UpsertItem(userId, model);
                }

                return Ok();
            }
            catch (Exception ex)
            {
                telemetryTracker.TrackException(ex, userId);
                BadRequest("Error Occured" + ex.Message);
            }

            return BadRequest("Error Occured");
        }

        [HttpPost]
        [Route("3AAA4664")]
        public async Task<IActionResult> PostIgnoreReminder(Guid eventId)
        {
            var userId = _authService.GetUser(Request);
            var token = _authService.GetToken(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized("Wrong Token");
            }

            try
            {
                var eventDetailExists = weedb.EventModels.Any(w => w.Id == eventId);
                if (eventDetailExists)
                {
                    var eventDetail = weedb.EventModels.FirstOrDefault(w => w.Id == eventId);
                    eventDetail.Notes = " ";
                    eventDetail.Modified = DateTime.UtcNow;
                    weedb.SaveChanges();
                }

                return Ok();
            }
            catch (Exception ex)
            {
                telemetryTracker.TrackException(ex, userId);
                BadRequest("Error Occured" + ex.Message);
            }

            return BadRequest("Error Occured");
        }

        [HttpPost]
        [Route("C31E6C26")]
        public async Task<IActionResult> GeteamsUP()
        {
            var userId = _authService.GetUser(Request);
            var token = _authService.GetToken(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized("Wrong Token");
            }

            try
            {
                Guid TeamsProviderId = Guid.Parse("825E7926-04DB-409F-8BCB-F48ED904156C");
                var isUserProviderExists = wepdb.UserProviders.Any(w =>
                    w.UserId == userId && w.ProviderId == TeamsProviderId && w.IsActive == true);
                return Ok(isUserProviderExists);
            }
            catch (Exception ex)
            {
                telemetryTracker.TrackException(ex, userId);
                return BadRequest(ex.ToString());
            }
        }

        [HttpPost]
        [Route("5BDF7CA3")]
        public async Task<IActionResult> SendTelegramMessage(string message)
        {
            TelegramService.SendMessageToTestBot(message);
            return Ok();
        }

        [HttpPost]
        [Route("87E2E0E1")]
        public async Task<IActionResult> ProcessForEventAttendee([FromBody] List<AttendeesModel> AttendeeList)
        {
            var userId = _authService.GetUser(Request);
            var token = _authService.GetToken(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized("Wrong Token");
            }

            try
            {
                if (AttendeeList.Count > 0)
                {
                    var userDetails = wepdb.Users.Where(x => x.Id == userId).FirstOrDefault();
                    var slice = userDetails.Email.Split('@');

                    var orgUser = weodb.OrgUsers.Where(w =>
                            w.UserId == userId && w.Org.Name.ToLower() == slice[1].ToLower() && w.IsActive == true)
                        .Include(x => x.Org).ToList();
                    var orgId = orgUser
                        .Where(x => x.Org.AppId.ToLower() == userDetails.AppId.ToString().ToLower() &&
                                    x.Org.IsActive == true).Select(x => x.Org.Id).FirstOrDefault();
                    foreach (var attModel in AttendeeList)
                    {
                        var contactPersonExists = db.OrgPeople.Any(w =>
                            w.OrgId == orgId && w.EmailId.ToLower() == attModel.Address.ToLower());
                        if (contactPersonExists)
                        {
                            var cp = db.OrgPeople.First(w =>
                                w.OrgId == orgId && w.EmailId.ToLower() == attModel.Address.ToLower());
                            //if (cp.IsInsightsFound == false)
                            //{
                            //    attModel.Status = 4;
                            //}

                            //if (attModel.Status != 4)
                            //{
                            if (cp.IsRequested == false && cp.IsRedeemed == false && cp.IsSearchComplete == false)
                                attModel.Status = 0;
                            else if (cp.IsRequested == true && cp.IsRedeemed == false &&
                                     cp.IsSearchComplete == true && cp.IsInsightsFound == true)
                                attModel.Status = 2;
                            else if (cp.IsRequested == true && cp.IsRedeemed == true && cp.IsSearchComplete == true &&
                                     cp.IsInsightsFound == true)
                                attModel.Status = 3;
                            else if (cp.IsRequested == true && cp.IsRedeemed == true && cp.IsSearchComplete == true &&
                                     cp.IsInsightsFound == false)
                                attModel.Status = 4;
                            else if (cp.IsRequested == true && cp.IsRedeemed == false && cp.IsSearchComplete == true &&
                                     cp.IsInsightsFound == false)
                                attModel.Status = 4;
                            // }
                        }
                        else
                        {
                            if (!string.IsNullOrEmpty(attModel.Address))
                            {
                                //var isPersonEmail = db.PersonEmails.Any(w => w.Email == attModel.Address && w.IsActive == true);
                                //if (isPersonEmail)
                                //{
                                //    ContactPerson contactPerson = new ContactPerson();
                                //    Guid ctId = (attModel.ContactId);
                                //    contactPerson.ContactId = ctId;
                                //    contactPerson.CreatedDate = DateTime.UtcNow;
                                //    contactPerson.Email = attModel.Address;
                                //    contactPerson.Id = Guid.NewGuid();
                                //    contactPerson.IsMatchFound = true;
                                //    contactPerson.IsRedeemed = false;
                                //    contactPerson.IsRequested = false;
                                //    contactPerson.IsSearchComplete = true;
                                //    contactPerson.PersonId = null;
                                //    db.ContactPeople.Add(contactPerson);
                                //    db.SaveChanges();

                                //    attModel.PersonId = null;
                                //    attModel.Status = 0;
                                //}
                                //else
                                //{
                                attModel.Status = 0;
                                // }
                                // }
                            }
                        }
                    }
                }

                return Ok(AttendeeList);
            }
            catch (Exception ex)
            {
                telemetryTracker.TrackException(ex, userId);
                return BadRequest(ex.ToString());
            }
        }

        [HttpPost("fix-email-entries")]
        public async Task<IActionResult> FixEmailEntries()
        {
            const int batchSize = 100;
            int processedRecords = 0;

            try
            {
                int totalRecords;

                do
                {
                    var contacts = await weedb.Contacts
                        .Where(c => c.EmailDisplay.StartsWith("[") && c.EmailDisplay.EndsWith("]"))
                        .OrderBy(c => c.Id)
                        .Skip(processedRecords)
                        .Take(batchSize)
                        .ToListAsync();

                    foreach (var contact in contacts)
                    {
                        var fixedEmail = HelperFunction.HandleEmailBlob(contact.EmailDisplay);
                        contact.EmailDisplay = fixedEmail;
                    }

                    await weddb.SaveChangesAsync();
                    processedRecords += contacts.Count;

                    totalRecords = contacts.Count;
                }
                while (totalRecords == batchSize);

                return Ok("Email entries have been fixed.");
            }
            catch (Exception ex)
            {

                return StatusCode(500, $"An error occurred: {ex.Message}");
            }
        }

    }
}