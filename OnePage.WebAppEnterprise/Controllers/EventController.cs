using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using OnePage.Models.Models.AdminModel;
using OnePage.Models.Models.DataModel;
using OnePage.Models.Models.OrgModel;
using OnePage.Models.Models.PeopleModel;
using OnePage.Models.Models;
using OnePage.Services.Interfaces;
using OnePage.Services;
using OnePage.Models.Models.TokenModel;
using Amazon.S3;
using Amazon;
using OnePage.Models.Models.ENTEventModel;
using Google.Apis.Gmail.v1.Data;
using OnePage.Common;
using static OnePage.Common.CommonData;
using UserProvider = OnePage.Models.Models.PeopleModel.UserProvider;
using Newtonsoft.Json;
using Contact = OnePage.Models.Models.ENTEventModel.Contact;
using EventModel = OnePage.Models.Models.ENTEventModel.EventModel;
using Microsoft.ApplicationInsights.DataContracts;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using OnePage.Models.Models;
using Thread = System.Threading.Thread;
using System.Security.Cryptography;
using TokenContext = OnePage.Models.Models.TokenModel.TokenContext;
using Microsoft.EntityFrameworkCore;
using OnePage.Services.ENTServices;
using Org.BouncyCastle.Asn1.Ocsp;
using System.ComponentModel.DataAnnotations;
using OnePage.Models;
using System;
using Microsoft.Graph.DeviceManagement.UserExperienceAnalyticsWorkFromAnywhereMetrics.Item.MetricDevices.Item;
using Microsoft.Graph.Drives.Item.Items.Item.Workbook.Functions.True;
using Microsoft.Azure.Cosmos.Core.Collections;

namespace OnePage.WebAppEnterprise.Controllers
{
    public class EventController : Controller
    {
        private readonly PeopleContext wepdb;
        private readonly DataContext weddb;
        private readonly AdminContext weadb;
        private readonly Models.Models.TokenModel.TokenContext wetdb;
        private readonly OrgContext weodb;
        private readonly ContactContext db;
        private readonly ENTEventContext weedb;
        private readonly IConfiguration _configuration;
        private readonly EntRequestResearchService entRequestResearchService;
        RestClient restClient = new();

        private readonly EntResearchService researchService;
        private readonly MetricsService metricsService;

        private readonly EntCalendarService calendarService;
        public IUploadService fileService;
        private readonly IRedisCaching _redisCaching;
        private readonly AuthService _authService;
        public EventController(IRedisCaching redisCaching, EntResearchService entResearchService, IUploadService _fileService, PeopleContext wepdb, DataContext weddb, AdminContext weadb, TokenContext wetdb, OrgContext weodb, ContactContext db, ENTEventContext weedb, IConfiguration configuration, EntCalendarService calendarService, AuthService authService, EntRequestResearchService entRequestResearchService, MetricsService metricsService)
        {
            _redisCaching = redisCaching;
            fileService = _fileService;
            this.wepdb = wepdb;
            this.weddb = weddb;
            this.weadb = weadb;
            this.wetdb = wetdb;
            this.weodb = weodb;
            this.db = db;
            this.metricsService = metricsService;
            this.weedb = weedb;
            this.calendarService = calendarService;
            _configuration = configuration;
            _authService = authService;
            this.researchService = entResearchService;
            this.entRequestResearchService = entRequestResearchService;
            // calendarService = new(redisCaching, _fileService);
        }
        public class EventReturnModel
        {
            public List<EventModel> NowList { get; set; } = new();
            public List<EventModel> TodayList { get; set; } = new();
            public List<EventModel> ReminderList { get; set; } = new();
            public List<EventModel> TomorrowList { get; set; } = new();
            public List<AttendeesModel> AttendeesList { get; set; } = new();
        }
        [HttpPost]
        [Route("E5C28662")]
        public async Task<IActionResult> GetEventEnt(string deviceId)
        {
            Guid userId = Guid.Empty;
            try
            {
                userId = _authService.GetUser(Request);
                var token = _authService.GetToken(Request);
                var user = await wepdb.Users.Where(x => x.Id == userId).AsNoTracking().FirstOrDefaultAsync();
                var isFreeDomain = await wepdb.EmailWhiteLists.AsNoTracking().AnyAsync(x => x.Email.ToLower() == user.Email.ToLower() && x.IsActive == true);
                if (userId == Guid.Empty)
                {
                    return Unauthorized("Wrong Token");
                }
                //if (fetchOnline)
                //{
                //    var userInfo = _redisCaching.CheckForItem("userDetailInfo_" + userId, () => wepdb.Users.FirstOrDefault(w => w.Id == userId), CommonData.CustomJsonSerializer.Serialize, JsonConvert.DeserializeObject<User>, DateTimeOffset.Now.AddHours(1));
                //    var slice = userInfo.Email.Split('@');
                //    var orgUser = await weodb.OrgUsers.Where(w =>
                //           w.UserId == userId && w.Org.Name.ToLower() == slice[1].ToLower() && w.IsActive == true)
                //       .Include(x => x.Org).AsNoTracking().ToListAsync();
                //    var orgId = orgUser
                //        .Where(x => x.Org.AppId.ToLower() == userInfo.AppId.ToString().ToLower() &&
                //                    x.Org.IsActive == true).Select(x => x.Org.Id).FirstOrDefault();
                //    bool profileExists = weedb.Contacts.Any(w => w.UserId == userId && w.OtherUserId == userId);
                //    if (!profileExists)
                //    {
                //        await CreateProfileContact(userId);
                //    }
                //    var userEmailSplit = userInfo.Email.Split('@');
                //    var userDomain = userEmailSplit[1];

                //    var profile = await weedb.Contacts.Where(w => w.UserId == userId && w.OtherUserId == userId).AsNoTracking().FirstOrDefaultAsync();
                //    List<string> profileEmails = weedb.ContactEmails.Where(x => x.ContactId == profile.Id && x.IsActive == true).Select(w => w.Email).ToList();
                //    var orgs = weodb.OrgUsers.Where(w => w.UserId == userId && w.Org.Name.ToLower() == userDomain.ToLower() && w.Org.AppId.ToLower() == CommonData.GlobalAppId.ToString().ToLower() && w.IsActive == true).ToList();
                //    foreach (var org in orgs)
                //    {
                //        var orgProviders = await weodb.OrgProviders.Where(w => w.OrgId == org.OrgId && w.IsActive == true).AsNoTracking().ToListAsync();
                //        foreach (var orgProvider in orgProviders)
                //        {
                //            switch (orgProvider.ProviderId.ToString().ToLower())
                //            {
                //                case "deaa4c8d-b0bb-4d81-bb86-055944e207d7":

                //                    try
                //                    {
                //                        var jsonFileContent = await fileService.DownloadFile("Google/" + org.OrgId.ToString().ToUpper() + ".json");
                //                        await calendarService.GetGoogleEvents(jsonFileContent, userInfo.Email, userInfo.Id, profileEmails);
                //                    }
                //                    catch (Exception ex)
                //                    {

                //                    }
                //                    break;
                //                case "a2dbf10e-08c5-4aa5-b198-8bbd2a860a66":
                //                    try
                //                    {
                //                        var jsonFileContent2 = await fileService.DownloadFile("Microsoft/" + org.OrgId.ToString().ToUpper() + ".json");
                //                        await calendarService.GetMicrosoftEvents(jsonFileContent2, userInfo.Email, userInfo.Id, profileEmails);
                //                    }
                //                    catch (Exception ex)
                //                    {

                //                    }
                //                    break;
                //                default:
                //                    break;
                //            }
                //        }
                //    }
                //}
                var entEventRds = new DbContextOptionsBuilder<ENTEventContext>();
                entEventRds.UseSqlServer(_configuration["DBConnections:WEENTEventConnectionString"]);


                using (var entDb = new ENTEventContext(entEventRds.Options))
                {
                    //DateTimeOffset today = new DateTime(DateTime.Today.Year, DateTime.Today.Month, DateTime.Today.Day);
                    //DateTimeOffset tomorrow = today.UtcDateTime.AddHours(24);
                    //DateTimeOffset now = DateTimeOffset.UtcNow;
                    //DateTimeOffset yesterday = today.UtcDateTime.AddHours(-24);
                    //long yLong = yesterday.ToUnixTimeSeconds();
                    //DateTimeOffset minusOne = today.UtcDateTime.AddMinutes(-1);
                    //long mLong = minusOne.ToUnixTimeSeconds();
                    //DateTimeOffset startLimit = now.Subtract(TimeSpan.FromMinutes(-15));
                    //DateTimeOffset endLimit = DateTimeOffset.Now.Subtract(TimeSpan.FromMinutes(1));
                    //long tomorrowSecs = tomorrow.ToUnixTimeSeconds();
                    var userDetail = await wepdb.Users.Where(x => x.Id == userId).AsNoTracking().FirstOrDefaultAsync();
                    var timezoneSetting = wepdb.UserSettings.AsNoTracking()
                        .FirstAsync(x => x.SettingId == CommonData.TimeZoneSettingId && x.UserId == userId).Result.Value;
                    int hourToAdd = 0;
                    int minutesToAdd = 0;

                    if (!string.IsNullOrEmpty(timezoneSetting))
                    {
                        var split = timezoneSetting.Split(":");
                        hourToAdd = int.Parse(split[0]);
                        minutesToAdd = int.Parse(split[1]);
                    }

                    TimeSpan timespan = new TimeSpan(hourToAdd, minutesToAdd, 0);
                    DateTimeOffset utcNow = DateTime.UtcNow.Date;
                    DateTimeOffset today = utcNow.ToOffset(timespan);

                    DateTimeOffset tomorrow = today.AddHours(24);
                    DateTimeOffset yesterday = today.AddHours(-24);
                    long yLong = yesterday.ToUnixTimeSeconds();
                    DateTimeOffset minusOne = today.AddMinutes(-1);
                    long mLong = minusOne.ToUnixTimeSeconds();
                    long tomorrowSecs = tomorrow.AddHours(24).ToUnixTimeSeconds();
                    List<EventModel> events = new List<EventModel>();
                    events = await entDb.EventModels.AsNoTracking().Where(w =>
                     w.UserId == userId && w.IsActive == true && w.BTwo == true && w.LEnd > yLong &&
                     w.LStart < tomorrowSecs).ToListAsync();
                    // var events = entDb.EventModels.Where(w => w.UserId == userId && w.IsActive == true && w.BTwo == true).ToList();
                    TrackMetadata trackMetadata = new TrackMetadata();//Event
                    //Guid timezoneSettingid = Guid.Parse("1D9BC314-786C-40E4-9FE4-769860C58F02");
                    //var timezoneSetting = wepdb.UserSettings.First(x => x.SettingId == timezoneSettingid && x.UserId == userId).Value;
                    //int hourToAdd = 0;
                    //int minutesToAdd = 0;
                    //if (!string.IsNullOrEmpty(timezoneSetting))
                    //{
                    //    var split = timezoneSetting.Split(":");
                    //    hourToAdd = int.Parse(split[0]);
                    //    minutesToAdd = int.Parse(split[1]);
                    //}
                    try
                    {
                        trackMetadata.user_id = userId.ToString();
                        trackMetadata.action = "Event";
                        var userDevice = await wepdb.UserDevices.Where(x => x.UserId == userId && x.IsActive == true)
                            .FirstOrDefaultAsync();
                        trackMetadata.numberOfEvents = events.Count.ToString();
                        trackMetadata.deviceId = userDevice.Id.ToString();
                        trackMetadata.deviceTypeId = userDevice.DeviceTypeId.ToString();
                        trackMetadata.deviceName = weddb.DeviceTypes.Where(x => x.Id == userDevice.DeviceTypeId)
                            .Select(x => x.Name).FirstOrDefault();
                        trackMetadata.signInTime = DateTime.Now.ToString();
                        trackMetadata.loginWebsite = "Ent";
                        trackMetadata.isFreeDomain = isFreeDomain.ToString();

                        //var userDetail = await wepdb.Users.Where(x => x.Id == userId).AsNoTracking().FirstOrDefaultAsync();
                        if (userDetail.IsTestUser != null)
                        {
                            trackMetadata.isTestUser = userDetail.IsTestUser.ToString();
                        }
                        metricsService.TrackUsuage(trackMetadata, "events");
                    }
                    catch (Exception)
                    {

                        throw;
                    }
                    EventReturnModel returnModel = new();
                    var todayEvents = await GetEventsMethod(false, events, userId, false);
                    _redisCaching.SetData<List<EventModel>>("microsoftEvents-today" + userId, todayEvents, DateTimeOffset.Now.AddMinutes(20));
                    var nowEvents = await GetEventsMethod(true, events, userId, false);
                    var tomorrowEvents = await GetEventsMethod(false, events, userId, true);
                    //TimeSpan timespan = new TimeSpan(hourToAdd, minutesToAdd, 0);
                    var mLong2 = DateTimeOffset.UtcNow.ToOffset(timespan).AddMinutes(-1).ToUnixTimeSeconds();
                    var yEvents = events.Where(w => w.IsActive == true && w.BTwo == true && w.LStart > yLong && w.LEnd <= mLong2).ToList();
                    List<EventModel> yestEvent = new List<EventModel>();
                    foreach (var yItem in yEvents)
                    {
                        var isCalendarExists = entDb.CalendarModels.Any(x => x.IsSelected == true && x.RefId == yItem.CalId);
                        if (isCalendarExists)
                        {
                            yestEvent.Add(yItem);

                        }
                    }

                    List<EventModel> reminderList = new List<EventModel>(yestEvent.ToList());
                    returnModel.TodayList = todayEvents;
                    int nowCount = 0;
                    returnModel.NowList = nowEvents;
                    foreach (var titem in returnModel.TodayList)
                    {
                        nowCount++;
                        if (titem.Desc != null)
                        {
                            if (titem.Desc.Contains("\n"))
                            {
                                var splitValue = titem.Desc.Split('\n');
                                var str1 = " ";
                                var str2 = " ";

                                foreach (var item in splitValue)
                                {
                                    if (item.Contains("meet.google.com") || item.Contains("zoom.us") || item.Contains("teams.microsoft.com") || item.Contains(".....") || item.Contains("join.skype.com") || item.Contains("calendly.com"))
                                    {
                                        continue;
                                    }
                                    else
                                    {
                                        str1 = item + "\n";
                                        str2 = str2 + str1;
                                    }
                                }
                                titem.Desc = str2;
                            }
                        }
                    }
                    var reminderList2 = reminderList.Where(w => w.Notes == "" || w.Notes == null).ToList();
                    if (reminderList2.Count > 0)
                    {
                        foreach (var item in reminderList2)
                        {
                            var attendees = await entDb.EventAttendees.Where(x => x.UserId == userId && x.EventId == item.EventId && (x.BOne == true) && (x.IsUser == false) && x.UserId == userId).AsNoTracking().ToListAsync();
                            item.EventAttendees = attendees;



                        }
                    }
                    returnModel.ReminderList = reminderList2;
                    returnModel.TomorrowList = tomorrowEvents;
                    int todayEventCount = 0;
                    var userDetails = wepdb.Users.Where(x => x.Id == userId).FirstOrDefault();
                    var slice = userDetails.Email.Split('@');

                    var orgUser = await weodb.OrgUsers.Where(w =>
                    w.UserId == userId && w.Org.Name.ToLower() == slice[1].ToLower() && w.IsActive == true).Include(x => x.Org).AsNoTracking().ToListAsync();
                    var orgId = orgUser.Where(x => x.Org.AppId.ToLower() == userDetails.AppId.ToString().ToLower() && x.Org.IsActive == true).Select(x => x.Org.Id).FirstOrDefault();

                    if (todayEvents.Count > 0)
                    {
                        foreach (var eventItem in todayEvents)
                        {
                            todayEventCount++;
                            if (eventItem.EventAttendees.Count > 0)
                            {
                                foreach (var attItem in eventItem.EventAttendees)
                                {

                                    var researchExists = false;
                                    var contactDataExists = entDb.Contacts.Any(w => w.Id == attItem.ContactId);
                                    if (contactDataExists)
                                    {
                                        var contactData = entDb.Contacts.Where(w => w.Id == attItem.ContactId).FirstOrDefault();
                                        var contactSocials = await entDb.ContactSocials.Where(w => w.ContactId == attItem.ContactId).AsNoTracking().ToListAsync();
                                        AttendeesModel attModel = new AttendeesModel();

                                        if (contactSocials.Count > 0)
                                        {

                                            researchExists = contactSocials.Any(w => w.Url.ToLower().Contains("twitter") || w.Url.ToLower().Contains("linkedin"));
                                        }
                                        if (!isFreeDomain)
                                        {
                                            var stringReplace1 =
                                               contactData.EmailDisplay.Replace('|'.ToString(), "");

                                            attModel.Address = attItem.Address;
                                            attModel.ContactId = (Guid)attItem.ContactId;
                                            if (attItem.Created != null)
                                                attModel.Created = (DateTimeOffset)attItem.Created;
                                            attModel.EventId = attItem.EventId;
                                            attModel.id = attItem.Id;
                                            if (attItem.BOne != null)
                                                attModel.IsActive = (bool)attItem.BOne;
                                            else
                                                attModel.IsActive = true;

                                            if (attItem.IsUser != null)
                                                attModel.IsUser = (bool)attItem.IsUser;
                                            ;
                                            attModel.Start = (DateTimeOffset)eventItem.Start;
                                            attModel.LStart = (long)eventItem.LStart;
                                            attModel.End = (DateTimeOffset)eventItem.End;
                                            attModel.LEnd = (long)eventItem.LEnd;
                                            if (attItem.Modified != null)
                                                attModel.Modified = (DateTimeOffset)attItem.Modified;
                                            attModel.Name = contactData.FirstName + " " + contactData.LastName;
                                            Guid cId = (Guid)attItem.ContactId;
                                            var contactPersonExists = db.OrgPeople.Any(w => w.OrgId == orgId && w.EmailId.ToLower() == stringReplace1.ToLower());
                                            if (contactPersonExists)
                                            {
                                                var cp = await db.OrgPeople.Where(w => w.OrgId == orgId && w.EmailId.ToLower() == attItem.Address.ToLower()).AsNoTracking().FirstOrDefaultAsync();


                                                attModel.Status = entRequestResearchService.GetResearchStatus(cp);

                                                attModel.profileImage = CommonData.S3BucketUrlForPic + cp.PersonId;
                                                attModel.company = contactData.Company;
                                                attModel.role = contactData.Desig;
                                            }

                                            else
                                            {
                                                attModel.Status = 0;
                                                //    }
                                                //}
                                            }

                                        }
                                        else
                                        {
                                            attModel.Address = attItem.Address;
                                            attModel.ContactId = (Guid)attItem.ContactId;
                                            if (attItem.Created != null)
                                                attModel.Created = (DateTimeOffset)attItem.Created;
                                            attModel.EventId = attItem.EventId;
                                            attModel.id = attItem.Id;
                                            if (attItem.BOne != null)
                                                attModel.IsActive = (bool)attItem.BOne;
                                            else
                                                attModel.IsActive = true;

                                            if (attItem.IsUser != null)
                                                attModel.IsUser = (bool)attItem.IsUser;
                                            ;
                                            attModel.Start = (DateTimeOffset)eventItem.Start;
                                            attModel.LStart = (long)eventItem.LStart;
                                            attModel.End = (DateTimeOffset)eventItem.End;
                                            attModel.LEnd = (long)eventItem.LEnd;
                                            if (attItem.Modified != null)
                                                attModel.Modified = (DateTimeOffset)attItem.Modified;
                                            attModel.Name = contactData.FirstName + " " + contactData.LastName;
                                            Guid cId = (Guid)attItem.ContactId;
                                            var contactPersonExists = db.ContactPeople.Any(w => w.ContactId == attItem.ContactId);
                                            if (contactPersonExists)
                                            {
                                                var cp = db.ContactPeople.First(w => w.ContactId == attItem.ContactId);
                                                attModel.Status = entRequestResearchService.GetResearchStatus(cp);
                                                attModel.profileImage = CommonData.S3BucketUrlForPic + cp.PersonId;
                                                attModel.company = contactData.Company;
                                                attModel.role = contactData.Desig;
                                            }

                                            else
                                            {
                                                attModel.Status = 0;
                                                //    }
                                                //}
                                            }


                                        }

                                        // }





                                        attModel.UserId = (Guid)attItem.UserId;
                                        returnModel.AttendeesList.Add(attModel);

                                    }
                                }

                            }
                        }


                    }
                    returnModel.AttendeesList = returnModel.AttendeesList.DistinctBy(w => w.Address).ToList();
                    try
                    {
                        trackMetadata.numberOfAttendees = returnModel.AttendeesList.Count.ToString();
                        trackMetadata.loginWebsite = "WebApp";
                        metricsService.TrackUsuage(trackMetadata, "events");
                    }
                    catch (Exception ex)
                    {

                    }
                    returnModel.ReminderList = returnModel.ReminderList.OrderBy(w => w.Start).ToList();

                    return Ok(returnModel);


                }

                //trackMetadata.numberOfAttendees = returnModel.AttendeesList.Count.ToString();
                //metricsService.TrackUsuage(trackMetadata, "events");


            }
            catch (Exception ex)
            {
                Models.Models.LogModel logModel = new Models.Models.LogModel()
                {
                    ErrorMessage = ex.Message,
                    Id = userId.ToString(),
                    //InputUsed = JsonConvert.SerializeObject(payload),
                    Source = "Ent FetchOnline .net api",
                    StackTrace = ex.StackTrace
                };
                //await dreamcastMongoService.CreateLog(logModel);
                await SESService.SendErrorMailsEnt(logModel);
                return BadRequest(ex.ToString());
            }
        }

        [HttpPost]
        [Route("7156FE13")]
        public async Task<IActionResult> CRMContactandsignal()
        {
            Guid userId = Guid.Empty;
            try
            {
                userId = _authService.GetUser(Request);
                var token = _authService.GetToken(Request);
                if (userId == Guid.Empty)
                {
                    return Unauthorized("Wrong Token");
                }
                var userInfo = await wepdb.Users.Where(x => x.Id == userId).AsNoTracking().FirstOrDefaultAsync();
                var slice = userInfo.Email.Split('@');
                var orgUser = await weodb.OrgUsers.Where(w =>
                       w.UserId == userId && w.Org.Name.ToLower() == slice[1].ToLower() && w.IsActive == true)
                   .Include(x => x.Org).AsNoTracking().ToListAsync();
                var orgId = orgUser
                    .Where(x => x.Org.AppId.ToLower() == userInfo.AppId.ToString().ToLower() &&
                                x.Org.IsActive == true).Select(x => x.Org.Id).FirstOrDefault();


                var timezoneSetting = wepdb.UserSettings
                    .FirstAsync(x => x.SettingId == CommonData.TimeZoneSettingId && x.UserId == userId).Result.Value;
                int hourToAdd = 0;
                int minutesToAdd = 0;

                if (!string.IsNullOrEmpty(timezoneSetting))
                {
                    var split = timezoneSetting.Split(":");
                    hourToAdd = int.Parse(split[0]);
                    minutesToAdd = int.Parse(split[1]);
                }

                TimeSpan timespan = new TimeSpan(hourToAdd, minutesToAdd, 0);
                DateTimeOffset utcNow = DateTime.UtcNow.Date;
                DateTimeOffset today = utcNow.ToOffset(timespan);

                DateTimeOffset tomorrow = today.AddHours(24);
                DateTimeOffset yesterday = today.AddHours(-24);
                long yLong = today.ToUnixTimeSeconds();
                DateTimeOffset minusOne = today.AddMinutes(-1);
                long mLong = minusOne.ToUnixTimeSeconds();
                long tomorrowSecs = tomorrow.AddHours(24).ToUnixTimeSeconds();


                var eventList = await weedb.EventModels.Where(w =>
                    w.UserId == userId && w.IsActive == true && w.BTwo == true && w.LEnd > yLong &&
                    w.LStart < tomorrowSecs).ToListAsync();



                DateTime CurrentTime = DateTime.UtcNow;
                DateTime unixEpochStart = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
                long epochTime = (long)(CurrentTime - unixEpochStart).TotalSeconds;
                eventList = eventList.Where(w => w.LEnd > epochTime).ToList();


                await calendarService.ScheduleForCRMLog(orgId, userId, eventList);


                return Ok("success");
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
        [HttpPost]
        [Route("5BCD3113")]

        public async Task<IActionResult> GetEventsusingUPorOP()
        {
            var userId = Guid.Empty;
            try
            {
                userId = _authService.GetUser(Request);
                var token = _authService.GetToken(Request);
                var user = await wepdb.Users.Where(x => x.Id == userId).AsNoTracking().FirstOrDefaultAsync();
                var isFreeDomain = await wepdb.EmailWhiteLists.AsNoTracking().AnyAsync(x => x.Email.ToLower() == user.Email.ToLower() && x.IsActive == true);
                bool profileExists = weedb.Contacts.Any(w => w.UserId == userId && w.OtherUserId == userId);
                if (!profileExists)
                {
                    await CreateProfileContact(userId);
                }
                string domain = user.Email.Split('@')[1];
                var profile = await weedb.Contacts.Where(w => w.UserId == userId && w.OtherUserId == userId).AsNoTracking().FirstOrDefaultAsync();
                List<string> profileEmails = weedb.ContactEmails.Where(x => x.ContactId == profile.Id && x.IsActive == true).Select(w => w.Email).ToList();
                if (userId == Guid.Empty)
                {
                    return Unauthorized("Wrong Token");
                }

                await calendarService.GetEventUsingUP(user, token, profileEmails);
                await calendarService.GetEventsUsingOrg(user, domain, profileEmails);

                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }

        }

        [HttpPost]
        [Route("88571B9A")]

        public async Task<IActionResult> EventContactandsignal()
        {
            Guid userId = Guid.Empty;
            try
            {
                userId = _authService.GetUser(Request);
                var token = _authService.GetToken(Request);
                if (userId == Guid.Empty)
                {
                    return Unauthorized("Wrong Token");
                }
                List<EventModel> EventsList = new List<EventModel>();
                var GoogleEvents = _redisCaching.GetData<List<EventModel>>("googleEvents" + userId);

                if (GoogleEvents != null)
                {
                    foreach (var googleEvent in GoogleEvents)
                    {
                        EventsList.Add(googleEvent);
                    }

                }
                var MicrosoftEvents = _redisCaching.GetData<List<EventModel>>("microsoftEvents" + userId);
                if (MicrosoftEvents != null)
                {
                    foreach (var microsoftEvent in MicrosoftEvents)
                    {
                        EventsList.Add(microsoftEvent);
                    }

                }
                //    var EventsList = _redisCaching.GetData<List<EventModel>>("events" + userId);
                OnePage.Models.Models.ENTEventModel.Contact profile = _redisCaching.CheckForItem("profile_" + userId, () => weedb.Contacts.Where(w => w.UserId == userId && w.OtherUserId == userId).FirstOrDefault(), CustomJsonSerializer.Serialize, JsonConvert.DeserializeObject<OnePage.Models.Models.ENTEventModel.Contact>, DateTimeOffset.Now.AddDays(1));
                var profileEmails = _redisCaching.CheckForItem("profileEmails" + userId, () => weedb.ContactEmails.Where(x => x.ContactId == profile.Id && x.IsActive == true).Select(w => w.Email).ToList(), CustomJsonSerializer.Serialize, JsonConvert.DeserializeObject<List<string>>, DateTimeOffset.Now.AddDays(1));

                int i = 0;
                bool IsDaily = false;
                var userInfo = await wepdb.Users.AsNoTracking().Where(x => x.Id == userId).FirstOrDefaultAsync();
                var domain = userInfo.Email.Split('@')[0];
                await CreateCompanySignal(EventsList, domain, userId);
                /* if (!IsDaily)
                     await calendarService.CheckAllAttendees(userId);*/
                TelegramService.SendMessageToTestBot2("schedule for web fn");
                await CreatePrefecthMaiil(userId, EventsList);
                await calendarService.ScheduleForWeb(userId, EventsList);

                //   await ResearchForEvents(userId, EventsList);*/
                return Ok("success");

            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);

            }


        }

        public async Task CreatePrefecthMaiil(Guid uid, List<EventModel> events)
        {
            var timeZoneUserSetting = await wepdb.UserSettings.Where(w => w.UserId == uid && w.IsActive == true && w.SettingId == TimeZoneSettingId).Select(x => x.Value).FirstOrDefaultAsync();
            int hourToAdd = 0;
            int minutesToAdd = 0;
            if (!string.IsNullOrEmpty(timeZoneUserSetting))
            {
                var split = timeZoneUserSetting.Split(":");
                hourToAdd = int.Parse(split[0]);
                minutesToAdd = int.Parse(split[1]);
            }
            foreach (var eventModelData in events)
            {
                var prefetchUSExists = await wepdb.UserSettings.AnyAsync(w => w.UserId == uid && w.SettingId == CommonData.ShouldPreFetchSettingId && w.IsActive == true);
                if (prefetchUSExists)
                {
                    var prefetchUS = await wepdb.UserSettings.FirstOrDefaultAsync(w => w.UserId == uid && w.SettingId == CommonData.ShouldPreFetchSettingId && w.IsActive == true);
                    var tempTime = ((DateTimeOffset)eventModelData.Start).AddHours(hourToAdd);
                    var tempTime2 = tempTime.AddMinutes(minutesToAdd);
                    var endTempTime = ((DateTimeOffset)eventModelData.End).AddHours(hourToAdd);
                    var entTmp2 = endTempTime.AddMinutes(minutesToAdd);
                    if (prefetchUS.Value == "1")
                    {
                        DateTime triggerTime = new DateTime();
                        triggerTime = tempTime2.AddMinutes(-10).DateTime;
                        var allEventAttendees = await weedb.EventAttendees.Where(w => w.EventId == eventModelData.EventId && w.IsActive == true && w.IsOrganizer == false).ToListAsync();
                        var contactIdsToPrefetch = allEventAttendees.Select(w => w.ContactId).ToList();
                        // create signal to fetch 
                        Signal signal = new Signal();
                        signal.Id = Guid.NewGuid();
                        signal.CreatedDate = DateTime.UtcNow;
                        signal.IsActive = true;
                        signal.IsComplete = false;
                        signal.IsSeen = false;
                        signal.Iteration = 1;
                        signal.ModifiedDate = DateTime.UtcNow;
                        signal.SignalTypeId = CommonData.ShouldPrefetchForEntSignalTypeId;
                        signal.TriggerOn = triggerTime;
                        if (!string.IsNullOrEmpty(uid.ToString()))
                            signal.UserId = uid;
                        else
                            signal.UserId = uid;
                        signal.AdditionalData = JsonConvert.SerializeObject(contactIdsToPrefetch);
                        wepdb.Signals.Add(signal);
                        await wepdb.SaveChangesAsync();
                    }
                }

            }
        }
        public async Task CreateCompanySignal(List<EventModel> eventModel, string userDomain, Guid userId)
        {
            try
            {
                var timeZoneUserSetting = await wepdb.UserSettings.Where(w => w.UserId == userId && w.IsActive == true && w.SettingId == TimeZoneSettingId).Select(x => x.Value).FirstOrDefaultAsync();
                int hourToAdd = 0;
                int minutesToAdd = 0;
                if (!string.IsNullOrEmpty(timeZoneUserSetting))
                {
                    var split = timeZoneUserSetting.Split(":");
                    hourToAdd = int.Parse(split[0]);
                    minutesToAdd = int.Parse(split[1]);
                }
                foreach (var eve in eventModel)
                {
                    List<string> DomainList = new List<string>();
                    var eventAttendee = await weedb.EventAttendees.AsNoTracking().Where(x => x.EventId == eve.EventId).ToListAsync();
                    var tempTime = ((DateTimeOffset)eve.Start).AddHours(hourToAdd);
                    var tempTime2 = tempTime.AddMinutes(minutesToAdd);
                    DateTime triggerTime = new DateTime();
                    triggerTime = tempTime2.AddMinutes(-10).DateTime;
                    if (eventAttendee.Count > 0)
                    {
                        foreach (var item in eventAttendee)
                        {
                            string domain = item.Address.Split('@')[1];
                            if (userDomain != domain)
                            {
                                DomainList.Add(domain);
                            }


                        }
                        var IsSignalPresent = await wepdb.Signals.AsNoTracking().AnyAsync(x => x.AdditionalData == JsonConvert.SerializeObject(DomainList) && x.IsActive == true);
                        if (!IsSignalPresent)
                        {

                            Signal signal = new Signal();
                            signal.Id = Guid.NewGuid();
                            signal.CreatedDate = DateTime.UtcNow;
                            signal.IsActive = true;
                            signal.IsComplete = false;
                            signal.IsSeen = false;
                            signal.Iteration = 1;
                            signal.ModifiedDate = DateTime.UtcNow;
                            signal.SignalTypeId = Guid.Parse("687946D4-6B27-47E9-A7D7-9E7023044603");
                            signal.TriggerOn = triggerTime;
                            if (!string.IsNullOrEmpty(userId.ToString()))
                                signal.UserId = userId;

                            else
                                signal.UserId = userId;
                            signal.AdditionalData = JsonConvert.SerializeObject(DomainList);
                            wepdb.Signals.Add(signal);
                            await wepdb.SaveChangesAsync();
                        }
                    }



                }
            }
            catch (Exception ex)
            {

            }

        }

        public class AddContactModelEnt
        {
            [Required]
            public string FirstName { get; set; }
            [Required]
            public string LastName { get; set; }
            
            public string Email { get; set; }

            public string eventId { get; set; } = "";

            public string Role { get; set; } = "";



            public string LinkedinUrl { get; set; }
        }

        public class contactWithProfileEnt
        {
            public Contact contactList { get; set; }

            public string peopleImage { get; set; }

            public int RequestStatus { get; set; }
        }
        public class ErrorStatus
        {
            public string errorMessage { get; set; }
        }

        internal class RequestInsightsModel1
        {
            public string contact_id { get; set; }
            public string email { get; set; }
            public string first_name { get; set; }
            public string last_name { get; set; }
            public string linkedin_url { get; set; }
            public string ln { get; set; }
            public string user_id { get; set; }
            public string user_first_name { get; set; }
            public string user_last_name { get; set; }
            public string user_email { get; set; }
            public string user_linkedin_url { get; set; }
        }
        public class ContactWithProfile
        {
            public Contact contactList { get; set; }

            public string peopleImage { get; set; }

            public int RequestStatus { get; set; }
        }
        [Route("FB0BBBCA")]
        [HttpPost]

        public async Task<IActionResult> AddContactEnt([FromBody] AddContactModelEnt contactModel, bool isContactPage = false)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            ErrorStatus errorStatus = new ErrorStatus();
            if (string.IsNullOrEmpty(contactModel?.Email) && string.IsNullOrEmpty(contactModel?.LinkedinUrl))
            {
                errorStatus.errorMessage = " Either Email Or Linkedin URL must be provided.";
                return BadRequest(errorStatus);
            }

            var userId = _authService.GetUser(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized();
            }

            var userDetails = await wepdb.Users.AsNoTracking().Where(x => x.Id == userId).FirstOrDefaultAsync();
            var slice = userDetails.Email.Split('@');
            var isFreeDomain = await weddb.FreeDomains.AsNoTracking().AnyAsync(x => x.DomainName.ToLower() == slice[1].ToLower());
            var contactDetail = new Contact();
            var orgUser = weodb.OrgUsers.Where(w =>
                    w.UserId == userId && w.Org.Name.ToLower() == slice[1].ToLower() && w.IsActive == true)
                .Include(x => x.Org).ToList();
            var orgId = orgUser
                .Where(x => x.Org.AppId.ToLower() == userDetails.AppId.ToString().ToLower() &&
                            x.Org.IsActive == true).Select(x => x.Org.Id).FirstOrDefault();

            ContactWithProfile returnModel = new ContactWithProfile();
            if (isContactPage)
            {
                var isContact = false;
                if (!string.IsNullOrEmpty(contactModel.Email))
                {

                    isContact = await weedb.Contacts.AsNoTracking().AnyAsync(x => x.EmailDisplay.Replace("|", "").ToLower() == contactModel.Email.ToLower() && x.UserId == userId);
                }
                else
                {
                    isContact = await weedb.ContactSocials.AsNoTracking().AnyAsync(x => x.Url.ToLower() == contactModel.LinkedinUrl.ToLower() && x.UserId == userId);
                }
                if (isContact)
                {
                    if (!string.IsNullOrEmpty(contactModel.Email))
                    {

                        var contactList = await weedb.Contacts.AsNoTracking().Where(x => x.EmailDisplay.Replace("|", "").ToLower() == contactModel.Email.Replace("|", "").ToLower() && x.UserId == userId).FirstOrDefaultAsync();
                        if (!isFreeDomain)
                        {
                            var checkForOrgPerson = await db.OrgPeople.AsNoTracking().AnyAsync(w => w.EmailId.ToLower() == contactList.EmailDisplay.Replace("|", "").ToLower() && w.OrgId == orgId);

                            if (checkForOrgPerson)
                            {
                                var orgPerson = await db.OrgPeople.AsNoTracking().FirstOrDefaultAsync(w => w.EmailId.ToLower().Replace("|", "") == contactList.EmailDisplay.Replace("|", "").ToLower() && w.OrgId == orgId);
                                var cp = await db.OrgPeople.AsNoTracking().FirstOrDefaultAsync(w => w.OrgId == orgId && w.EmailId.ToLower() == contactList.EmailDisplay.Replace("|", "").ToLower());
                                returnModel.RequestStatus = entRequestResearchService.GetResearchStatus(cp);

                                if (orgPerson.PersonId != null)
                                {

                                    Guid personId = (Guid)(orgPerson.PersonId);
                                    var insightResponse = await GetInsightsByPersonId1(userId, personId);
                                    if (insightResponse.data != null)
                                    {
                                        returnModel.peopleImage = insightResponse.data.profile_pic;
                                    }
                                }

                            }
                            else
                            {
                                contactDetail = contactList;
                                OrgPerson orgPerson = new OrgPerson();
                                orgPerson.Id = Guid.NewGuid();
                                orgPerson.EmailId = contactDetail.EmailDisplay;
                                orgPerson.UserId = userId;
                                orgPerson.OrgId = orgId;
                                orgPerson.ContactId = contactList.Id;
                                orgPerson.SLinkedIn = contactDetail.Linked;
                                orgPerson.CreatedDate = DateTime.UtcNow;
                                orgPerson.ModifiedDate = DateTime.UtcNow;
                                orgPerson.IsInsightsFound = false;
                                orgPerson.IsRedeemed = false;
                                orgPerson.IsActive = true;
                                orgPerson.IsRequested = false;
                                orgPerson.IsSearchComplete = false;
                                db.OrgPeople.Add(orgPerson);
                                await db.SaveChangesAsync();

                            }
                            returnModel.contactList = contactList;

                            return Ok(returnModel);
                        }
                        else
                        {

                            var checkForOrgPerson = await db.ContactPeople.AsNoTracking().AnyAsync(w => w.ContactId == contactList.Id);

                            if (checkForOrgPerson)
                            {
                                var orgPerson = await db.ContactPeople.AsNoTracking().FirstOrDefaultAsync(w => w.ContactId == contactList.Id && w.Email.ToLower() == contactModel.Email.Replace("|", ""));

                                var cp = await db.ContactPeople.AsNoTracking().FirstOrDefaultAsync(w => w.ContactId == contactList.Id && w.Email.ToLower() == contactModel.Email.Replace("|", ""));
                                returnModel.RequestStatus = entRequestResearchService.GetResearchStatus(cp);

                                if (orgPerson.PersonId != null)
                                {

                                    Guid personId = (Guid)(orgPerson.PersonId);
                                    var insightResponse = await GetInsightsByPersonId1(userId, personId);
                                    if (insightResponse.data != null)
                                    {
                                        returnModel.peopleImage = insightResponse.data.profile_pic;
                                    }
                                }

                            }
                            else
                            {
                                contactDetail = contactList;
                                ContactPerson orgPerson = new ContactPerson();
                                orgPerson.Id = Guid.NewGuid();
                                orgPerson.Email = contactDetail.EmailDisplay;
                                // orgPerson.UserId = userId;
                                //orgPerson.OrgId = orgId;
                                orgPerson.ContactId = contactList.Id;
                                // orgPerson.SLinkedIn = contactDetail.Linked;
                                orgPerson.CreatedDate = DateTime.UtcNow;
                                //orgPerson.ModifiedDate = DateTime.UtcNow;
                                orgPerson.InsightsFound = false;
                                orgPerson.IsRedeemed = false;
                                //orgPerso = true;
                                orgPerson.IsRequested = false;
                                orgPerson.IsSearchComplete = false;
                                db.ContactPeople.Add(orgPerson);
                                await db.SaveChangesAsync();

                            }
                            returnModel.contactList = contactList;

                            return Ok(returnModel);

                        }
                    }
                    else
                    {
                        var contactDetail1 = new Contact();
                        bool checkForOrgPerson = false;
                        List<Contact> ContactList = new List<Contact>();
                        Guid contactId = Guid.Empty;
                        var isContactSocialExists = await weedb.ContactSocials.AsNoTracking().AnyAsync(x => x.Url.ToLower() == contactModel.LinkedinUrl.ToLower() && x.IsActive == true);
                        if (isContactSocialExists)
                        {
                            var contactSocial = await weedb.ContactSocials.FirstOrDefaultAsync(x => x.Url.ToLower() == contactModel.LinkedinUrl.ToLower() && x.IsActive == true);
                            if (contactSocial != null)
                            {
                                contactId = (Guid)contactSocial.ContactId;
                            }
                            // var query = from contact in weedb.Contacts
                            //             where (from cs in contactSOCIAL
                            //                    select cs.ContactId).Contains(contact.Id)
                            //             && contact.UserId == userId
                            //             select contact;
                            // if (query.Count() > 0)
                            // {
                            //     ContactList = query.ToList();
                            // }
                        }
                        if (ContactList.Count > 0)
                        {
                            contactId = ContactList.Select(x => x.Id).FirstOrDefault();
                            checkForOrgPerson = await db.OrgPeople.AsNoTracking().AnyAsync(w => w.ContactId == contactId);
                            contactDetail = await weedb.Contacts.AsNoTracking().Where(x => x.Id == contactId).FirstOrDefaultAsync();
                        }
                        else
                        {
                            OnePage.Models.Models.ENTEventModel.Contact contact = new OnePage.Models.Models.ENTEventModel.Contact();
                            contact.Id = Guid.NewGuid();
                            contact.FirstName = contactModel.FirstName;
                            contact.LastName = contactModel?.LastName ?? string.Empty;
                            contact.EmailDisplay = contactModel?.Email ?? string.Empty;
                            contact.ProviderId = CommonData.ContactAddedByUser;
                            contact.UserId = userId;
                            contact.Created = DateTime.UtcNow;
                            contact.Modified = DateTime.UtcNow;
                            contact.Display = contactModel.FirstName + " " + contactModel?.LastName;
                            contact.Desig = contactModel?.Role;
                            contact.IsActive = true;
                            weedb.Contacts.Add(contact);
                            await weedb.SaveChangesAsync();
                            contactId = contact.Id;

                            if (!String.IsNullOrEmpty(contactModel?.LinkedinUrl))
                            {
                                OnePage.Models.Models.ENTEventModel.ContactSocial contactSocial = new OnePage.Models.Models.ENTEventModel.ContactSocial();
                                contactSocial.Id = Guid.NewGuid();
                                contactSocial.ContactId = contactId;
                                contactSocial.Created = DateTime.Now;
                                contactSocial.IsActive = true;
                                contactSocial.Url = contactModel?.LinkedinUrl;
                                contactSocial.UserId = userId;
                                contactSocial.Name = "LinkedIn";
                                contactSocial.SocialId = Guid.Parse("0EA37808-1506-43A3-9ABD-6FF561941FF5");
                                weedb.ContactSocials.Add(contactSocial);
                                await weedb.SaveChangesAsync();
                            }
                            contactDetail = contact;
                        }


                        if (!isFreeDomain)
                        {

                            if (checkForOrgPerson)
                            {

                                var cp = db.OrgPeople.First(w => w.ContactId == contactId);
                                returnModel.RequestStatus = entRequestResearchService.GetResearchStatus(cp);


                                if (cp.PersonId != null)
                                {

                                    Guid personId = (Guid)(cp.PersonId);
                                    var insightResponse = await GetInsightsByPersonId1(userId, personId);
                                    if (insightResponse.data != null)
                                    {
                                        returnModel.peopleImage = insightResponse.data.profile_pic;
                                    }
                                }

                            }
                            else
                            {
                                OrgPerson orgPerson = new OrgPerson();
                                orgPerson.Id = Guid.NewGuid();
                                orgPerson.EmailId = contactDetail.EmailDisplay;
                                orgPerson.OrgId = orgId;
                                orgPerson.UserId = userId;
                                orgPerson.ContactId = contactDetail.Id;
                                orgPerson.SLinkedIn = contactDetail.Linked;
                                orgPerson.CreatedDate = DateTime.UtcNow;
                                orgPerson.ModifiedDate = DateTime.UtcNow;
                                orgPerson.IsInsightsFound = false;
                                orgPerson.IsRedeemed = false;
                                orgPerson.IsActive = true;
                                orgPerson.IsRequested = false;
                                orgPerson.IsSearchComplete = false;
                                db.OrgPeople.Add(orgPerson);
                                db.SaveChanges();

                            }
                            returnModel.contactList = contactDetail;
                            return Ok(returnModel);
                        }
                        else
                        {
                            checkForOrgPerson = await db.ContactPeople.AsNoTracking().AnyAsync(w => w.ContactId == contactDetail.Id);

                            if (checkForOrgPerson)
                            {
                                var orgPerson = await db.ContactPeople.AsNoTracking().FirstOrDefaultAsync(w => w.ContactId == contactDetail.Id && w.Email.ToLower() == contactModel.Email.Replace("|", ""));

                                var cp = await db.ContactPeople.AsNoTracking().FirstOrDefaultAsync(w => w.ContactId == contactDetail.Id && w.Email.ToLower() == contactModel.Email.Replace("|", ""));
                                returnModel.RequestStatus = entRequestResearchService.GetResearchStatus(cp);

                                if (orgPerson.PersonId != null)
                                {

                                    Guid personId = (Guid)(orgPerson.PersonId);
                                    var insightResponse = await GetInsightsByPersonId1(userId, personId);
                                    if (insightResponse.data != null)
                                    {
                                        returnModel.peopleImage = insightResponse.data.profile_pic;
                                    }
                                }

                            }
                            else
                            {
                                contactDetail = contactDetail;
                                ContactPerson orgPerson = new ContactPerson();
                                orgPerson.Id = Guid.NewGuid();
                                orgPerson.Email = contactDetail.EmailDisplay;
                                // orgPerson.UserId = userId;
                                //orgPerson.OrgId = orgId;
                                orgPerson.ContactId = contactDetail.Id;
                                // orgPerson.SLinkedIn = contactDetail.Linked;
                                orgPerson.CreatedDate = DateTime.UtcNow;
                                //orgPerson.ModifiedDate = DateTime.UtcNow;
                                orgPerson.InsightsFound = false;
                                orgPerson.IsRedeemed = false;
                                //orgPerso = true;
                                orgPerson.IsRequested = false;
                                orgPerson.IsSearchComplete = false;
                                db.ContactPeople.Add(orgPerson);
                                await db.SaveChangesAsync();

                            }
                            returnModel.contactList = contactDetail;

                            return Ok(returnModel);

                        }
                    }

                }
                else
                {
                    var op = new OrgPerson();
                    OnePage.Models.Models.ENTEventModel.Contact contact = new OnePage.Models.Models.ENTEventModel.Contact();
                    contact.Id = Guid.NewGuid();
                    contact.FirstName = contactModel.FirstName;
                    contact.LastName = contactModel?.LastName ?? string.Empty;
                    contact.EmailDisplay = contactModel?.Email ?? string.Empty;
                    contact.ProviderId = CommonData.ContactAddedByUser;
                    contact.UserId = userId;
                    contact.Created = DateTime.UtcNow;
                    contact.Modified = DateTime.UtcNow;
                    contact.Display = contactModel.FirstName + " " + contactModel?.LastName;
                    contact.Desig = contactModel?.Role;
                    contact.IsActive = true;
                    weedb.Contacts.Add(contact);
                    await weedb.SaveChangesAsync();
                    returnModel.contactList = contact;
                    if (!string.IsNullOrEmpty(contactModel?.LinkedinUrl))
                    {
                        var socialTypeId = weddb.SocialTypes.FirstOrDefault(w => w.Name == "LinkedIn");
                        OnePage.Models.Models.ENTEventModel.ContactSocial social = new()
                        {
                            ContactId = contact.Id,
                            Url = contactModel.LinkedinUrl,
                            UserId = userId,
                            IsActive = true,
                            AskMe = false,
                            CanRequest = false,
                            Name = socialTypeId.Name,
                            Request = false,
                            Show = false,
                            SocialId = socialTypeId.Id,
                            Id = Guid.NewGuid()
                        };
                        weedb.ContactSocials.Add(social);
                        await weedb.SaveChangesAsync();
                        var orgPerson = new OrgPerson()
                        {
                            ContactId = contact.Id,
                            CreatedDate = DateTime.UtcNow,
                            EmailId = contact.EmailDisplay,
                            Id = Guid.NewGuid(),
                            IsActive = true,
                            IsInsightsFound = false,
                            IsRedeemed = false,
                            IsRequested = true,
                            IsSearchComplete = false,
                            ModifiedDate = DateTime.UtcNow,
                            OrgId = orgId,
                            SLinkedIn = contactModel.LinkedinUrl,
                            UserId = userId
                        };

                        db.OrgPeople.Add(orgPerson);
                        await db.SaveChangesAsync();
                        op = orgPerson;
                    }
                    int status = 0;
                    returnModel.RequestStatus = status;
                }
                return Ok(returnModel);

            }
            var isContactPresent = false;
            if (!string.IsNullOrEmpty(contactModel.Email))
            {
                isContactPresent = await weedb.Contacts.AsNoTracking().AnyAsync(x => x.EmailDisplay.ToLower() == contactModel.Email.ToLower() && x.UserId == userId);

            }
            else
            {

                List<Contact> ContactList = new List<Contact>();
                Guid contactId = Guid.Empty;
                var isContactSocialExists = await weedb.ContactSocials.AsNoTracking().AnyAsync(x => x.Url.ToLower() == contactModel.LinkedinUrl.ToLower() && x.IsActive == true);
                if (isContactSocialExists)
                {
                    var contactSocial = await weedb.ContactSocials.FirstOrDefaultAsync(x => x.Url.ToLower() == contactModel.LinkedinUrl.ToLower() && x.IsActive == true);
                    if (contactSocial != null)
                    {
                        contactId = (Guid)contactSocial.ContactId;
                    }
                    // var query = from contact in weedb.Contacts
                    //             where (from cs in contactSOCIAL
                    //                    select cs.ContactId).Contains(contact.Id)
                    //             && contact.UserId == userId
                    //             select contact;
                    // if (query.Count() > 0)
                    // {
                    //     ContactList = query.ToList();
                    // }
                }
                // Guid contactId = Guid.Empty;
                // if (ContactList.Count > 0)
                // {
                //     contactId = ContactList.Select(x => x.Id).FirstOrDefault();
                // }
                if (contactId == Guid.Empty)
                {
                    isContactPresent = false;
                }
                else
                {
                    isContactPresent = true;
                }
            }

            if (!isContactPresent)
            {
                var op = new OrgPerson();
                OnePage.Models.Models.ENTEventModel.Contact contact = new OnePage.Models.Models.ENTEventModel.Contact();
                contact.Id = Guid.NewGuid();
                contact.FirstName = contactModel.FirstName;
                contact.LastName = contactModel.LastName;
                contact.EmailDisplay = contactModel.Email;
                contact.ProviderId = CommonData.ContactAddedByUser;
                contact.UserId = userId;
                contact.Created = DateTime.UtcNow;
                contact.Modified = DateTime.UtcNow;
                contact.Desig = contactModel.Role;
                contact.Display = contactModel.FirstName + " " + contactModel.LastName;
                contact.IsActive = true;
                weedb.Contacts.Add(contact);
                await weedb.SaveChangesAsync();
                returnModel.contactList = contact;
                if (!string.IsNullOrEmpty(contactModel.LinkedinUrl))
                {
                    var socialTypeId = await weddb.SocialTypes.AsNoTracking().FirstOrDefaultAsync(w => w.Name == "LinkedIn");
                    var isUrlExists = await weedb.ContactSocials.AsNoTracking().AnyAsync(w => w.UserId == userId && w.Url == contactModel.LinkedinUrl && w.IsActive == true);
                    if (!isUrlExists)
                    {
                        OnePage.Models.Models.ENTEventModel.ContactSocial social = new()
                        {
                            ContactId = contact.Id,
                            Url = contactModel.LinkedinUrl,
                            UserId = userId,
                            IsActive = true,
                            AskMe = false,
                            CanRequest = false,
                            Name = socialTypeId.Name,
                            Request = false,
                            Show = false,
                            SocialId = socialTypeId.Id,
                            Id = Guid.NewGuid()
                        };
                        weedb.ContactSocials.Add(social);
                        await weedb.SaveChangesAsync();
                    }
                }
                var orgPerson = new OrgPerson()
                {
                    ContactId = contact.Id,
                    CreatedDate = DateTime.UtcNow,
                    EmailId = contact.EmailDisplay,
                    Id = Guid.NewGuid(),
                    IsActive = true,
                    IsInsightsFound = false,
                    IsRedeemed = false,
                    IsRequested = true,
                    IsSearchComplete = false,
                    ModifiedDate = DateTime.UtcNow,
                    OrgId = orgId,
                    SLinkedIn = contactModel.LinkedinUrl,
                    UserId = userId
                };

                db.OrgPeople.Add(orgPerson);
                await db.SaveChangesAsync();
                op = orgPerson;
                int status = 0;
                returnModel.RequestStatus = status;
                var isEventPresent = await weedb.EventModels.AsNoTracking().Where(x => x.EventId == contactModel.eventId && x.UserId == userId && x.IsActive == true).FirstOrDefaultAsync();
                if (isEventPresent != null)
                {
                    var isEventAttendee = await weedb.EventAttendees.AsNoTracking().Where(x => x.EventId == isEventPresent.EventId && x.ContactId == contact.Id).FirstOrDefaultAsync();
                    if (isEventAttendee != null)
                    {
                        return Ok(returnModel);
                    }
                    OnePage.Models.Models.ENTEventModel.EventAttendee eventAttendee = new OnePage.Models.Models.ENTEventModel.EventAttendee();
                    eventAttendee.Id = Guid.NewGuid();
                    eventAttendee.Address = contactModel.Email;
                    eventAttendee.EventId = contactModel.eventId;
                    eventAttendee.UserId = userId;
                    eventAttendee.ContactId = contact.Id;
                    eventAttendee.IsActive = true;
                    eventAttendee.Name = contactModel.FirstName + " " + contactModel.LastName;
                    eventAttendee.Created = DateTime.UtcNow;
                    eventAttendee.Modified = DateTime.UtcNow;
                    eventAttendee.IsAdhocUser = true;
                    weedb.EventAttendees.Add(eventAttendee);
                    await weedb.SaveChangesAsync();
                }
                else
                {

                    errorStatus.errorMessage = "Event does not exists.";
                    return BadRequest(errorStatus);


                }
                return Ok(returnModel);

            }
            else
            {
                if (!string.IsNullOrEmpty(contactModel.Email))
                {
                    var op = new OrgPerson();
                    var contactList = await weedb.Contacts.AsNoTracking().Where(x => x.EmailDisplay.ToLower() == contactModel.Email.ToLower() && x.UserId == userId).FirstOrDefaultAsync();
                    var isEventPresent = await weedb.EventModels.AsNoTracking().Where(x => x.EventId == contactModel.eventId && x.UserId == userId && x.IsActive == true).FirstOrDefaultAsync();
                    if (isEventPresent != null)
                    {
                        var isEventAttendee = await weedb.EventAttendees.AsNoTracking().Where(x => x.EventId == isEventPresent.EventId && x.ContactId == contactList.Id).FirstOrDefaultAsync();
                        if (isEventAttendee != null)
                        {
                            errorStatus.errorMessage = "Event Attendee Already Present";
                            return BadRequest(errorStatus);


                        }
                        OnePage.Models.Models.ENTEventModel.EventAttendee eventAttendee = new OnePage.Models.Models.ENTEventModel.EventAttendee();
                        eventAttendee.Id = Guid.NewGuid();
                        eventAttendee.Address = contactModel.Email;
                        eventAttendee.EventId = contactModel.eventId;
                        eventAttendee.UserId = userId;
                        eventAttendee.ContactId = contactList.Id;
                        eventAttendee.IsActive = true;
                        eventAttendee.Name = contactModel.FirstName + " " + contactModel.LastName;
                        eventAttendee.Created = DateTime.UtcNow;
                        eventAttendee.Modified = DateTime.UtcNow;
                        eventAttendee.IsAdhocUser = true;
                        weedb.EventAttendees.Add(eventAttendee);
                        await weedb.SaveChangesAsync();
                    }
                    else
                    {
                        errorStatus.errorMessage = "Event does not exists";
                        return BadRequest(errorStatus);

                    }
                    returnModel.contactList = contactList;
                    int status = 0;
                    returnModel.RequestStatus = status;
                    return Ok(returnModel);

                }
                else
                {
                    var contactDetail1 = new Contact();
                    bool checkForOrgPerson = false;
                    List<Contact> ContactList = new List<Contact>();
                    Guid contactId = Guid.Empty;
                    var isContactSocialExists = await weedb.ContactSocials.AsNoTracking().AnyAsync(x => x.Url.ToLower() == contactModel.LinkedinUrl.ToLower() && x.IsActive == true);
                    if (isContactSocialExists)
                    {
                        var contactSocial = await weedb.ContactSocials.FirstOrDefaultAsync(x => x.Url.ToLower() == contactModel.LinkedinUrl.ToLower() && x.IsActive == true);
                        if (contactSocial != null)
                        {
                            contactId = (Guid)contactSocial.ContactId;
                        }
                        // var query = from contact in weedb.Contacts
                        //             where (from cs in contactSOCIAL
                        //                    select cs.ContactId).Contains(contact.Id)
                        //             && contact.UserId == userId
                        //             select contact;
                        // if (query.Count() > 0)
                        // {
                        //     ContactList = query.ToList();
                        // }
                    }
                    if (contactId != Guid.Empty)
                    {
                        checkForOrgPerson = await db.OrgPeople.AsNoTracking().AnyAsync(w => w.ContactId == contactId);
                        contactDetail1 = await weedb.Contacts.AsNoTracking().Where(x => x.Id == contactId).FirstOrDefaultAsync();
                    }


                    var op = new OrgPerson();
                    var contactList = await weedb.Contacts.AsNoTracking().Where(x => x.Id == contactId).FirstOrDefaultAsync();
                    var isEventPresent = await weedb.EventModels.AsNoTracking().Where(x => x.EventId == contactModel.eventId && x.UserId == userId && x.IsActive == true).FirstOrDefaultAsync();
                    if (isEventPresent != null)
                    {
                        var isEventAttendee = await weedb.EventAttendees.AsNoTracking().Where(x => x.EventId == isEventPresent.EventId && x.ContactId == contactList.Id).FirstOrDefaultAsync();
                        if (isEventAttendee != null)
                        {
                            errorStatus.errorMessage = "Event Attendee Already Present";
                            return BadRequest(errorStatus);
                        }
                        OnePage.Models.Models.ENTEventModel.EventAttendee eventAttendee = new OnePage.Models.Models.ENTEventModel.EventAttendee();
                        eventAttendee.Id = Guid.NewGuid();
                        eventAttendee.Address = contactModel.Email;
                        eventAttendee.EventId = contactModel.eventId;
                        eventAttendee.UserId = userId;
                        eventAttendee.ContactId = contactList.Id;
                        eventAttendee.IsActive = true;
                        eventAttendee.Name = contactModel.FirstName + " " + contactModel.LastName;
                        eventAttendee.Created = DateTime.UtcNow;
                        eventAttendee.Modified = DateTime.UtcNow;
                        eventAttendee.IsAdhocUser = true;
                        weedb.EventAttendees.Add(eventAttendee);
                        await weedb.SaveChangesAsync();
                    }
                    else
                    {
                        errorStatus.errorMessage = "Event does not exists";
                        return BadRequest(errorStatus);
                    }
                    returnModel.contactList = contactList;
                    int status = 0;
                    returnModel.RequestStatus = status;
                    return Ok(returnModel);
                }
            }
        }
        [HttpPost]
        [Route("3810F747")]
        public async Task<IActionResult> GetEventAttachmentWithCardIdsForUp(Guid eventId)
        {
            var userId = _authService.GetUser(Request);
            var tokenId = _authService.GetToken(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized();
            }

            List<EventAttachmentCardModel> returnCardList = new List<EventAttachmentCardModel>();
            var eventData = weedb.EventModels.Where(x => x.Id == eventId).FirstOrDefault();
            if (eventData != null)
            {
                var EventAttachments =
                    await calendarService.GetAttachmentsUP(userId, eventData, (Guid)eventData.ProviderId, tokenId);
                EventAttachmentCardModel cardModel = new EventAttachmentCardModel();
                cardModel.CardId = Guid.Parse("A072C8BC-DEF7-417F-A69F-15E19ED65192");
                cardModel.CardContent = EventAttachments;
                if (eventData.ProviderId == CommonData.ZohoCalendarProviderId)
                {
                    cardModel.IsApiShouldBeCalled = true;
                }
                returnCardList.Add(cardModel);
                return Ok(returnCardList);
            }
            else
            {
                return BadRequest("Event is not present");
            }
        }

        public async Task<ContactInsightsReturnModel> GetInsightsByPersonId1(Guid userId, Guid personId)
        {
            try
            {
                var aiUrl = _configuration["ServerSettings:OnePageAIUrl"];
                var response = await restClient.GetAsync<ContactInsightsReturnModel>(aiUrl + "person/" + personId + "/insights", useAuthToken: false, isPDLCall: false);
                return response;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public class ExsistsEventAttedndee
        {
            public Guid ContactId { get; set; }
            public string EventId { get; set; }
        }
        [HttpPost]
        [Route("6C2702C4")]

        public async Task<IActionResult> AddExistContactToEvent([FromBody] ExsistsEventAttedndee eventAttedndee)
        {
            var userId = _authService.GetUser(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized();
            }
            var contactDetail = weedb.Contacts.Where(x => x.Id == eventAttedndee.ContactId).FirstOrDefault();
            if (contactDetail != null)
            {
                var isEventPresent = weedb.EventModels.Where(x => x.EventId == eventAttedndee.EventId && x.UserId == userId && x.IsActive == true).FirstOrDefault();
                if (isEventPresent != null)
                {
                    if (!await weedb.EventAttendees.AsNoTracking().AnyAsync(x => x.ContactId == eventAttedndee.ContactId && x.EventId == eventAttedndee.EventId))
                    {
                        OnePage.Models.Models.ENTEventModel.EventAttendee eventAttendee = new OnePage.Models.Models.ENTEventModel.EventAttendee();
                        eventAttendee.Id = Guid.NewGuid();
                        eventAttendee.Address = contactDetail.EmailDisplay;
                        eventAttendee.EventId = eventAttedndee.EventId;
                        eventAttendee.UserId = userId;
                        eventAttendee.ContactId = eventAttedndee.ContactId;
                        eventAttendee.IsActive = true;
                        eventAttendee.Name = contactDetail.FirstName;
                        eventAttendee.Created = DateTime.UtcNow;
                        eventAttendee.Modified = DateTime.UtcNow;
                        eventAttendee.IsAdhocUser = true;
                        weedb.EventAttendees.Add(eventAttendee);
                        weedb.SaveChanges();
                        return Ok("Contact added to this event");
                    }
                    else
                    {
                        return BadRequest("This Contact is already present in the eventID");
                    }
                }

            }
            return Ok();



        }
        [Route("5FD4BE08")]
        [HttpPost]

        public async Task<IActionResult> UpdateTheContact([FromBody] AddContactModelEnt contactModel)
        {
            var userId = _authService.GetUser(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized();
            }
            var isContact = weedb.Contacts.Where(x => x.EmailDisplay == contactModel.Email && x.UserId == userId && x.IsActive == true).FirstOrDefault();
            if (isContact != null)
            {
                isContact.FirstName = contactModel.FirstName;
                isContact.LastName = contactModel.LastName;
                isContact.Modified = DateTime.UtcNow;
                isContact.IsActive = true;
                weedb.SaveChanges();
                OnePage.Models.Models.ENTEventModel.EventAttendee eventAttendee = new OnePage.Models.Models.ENTEventModel.EventAttendee();
                eventAttendee.Id = Guid.NewGuid();
                eventAttendee.Address = contactModel.Email;
                eventAttendee.EventId = contactModel.eventId;
                eventAttendee.UserId = userId;
                eventAttendee.ContactId = isContact.Id;
                eventAttendee.IsActive = true;
                eventAttendee.Name = contactModel.FirstName;
                eventAttendee.Created = DateTime.UtcNow;
                eventAttendee.Modified = DateTime.UtcNow;
                eventAttendee.IsAdhocUser = true;
                weedb.EventAttendees.Add(eventAttendee);
                weedb.SaveChanges();

            }
            return Ok("Updated SuccessFully");
        }
        public class EventsPaginationReturnModel
        {
            public string cardId { get; set; }

            public object value { get; set; }

            public bool? isNext { get; set; }
        }
        [HttpPost]
        [Route("BBC6FFBD")]
        public async Task<IActionResult> EventsPagination([FromQuery] Guid contactId, int pageNumber = 1, int size = 10, bool isPast = false)
        {
            try
            {
                var userId = _authService.GetUser(Request);
                if (userId == Guid.Empty)
                {
                    return BadRequest(CommonData.ErrorCodes.E0008);
                }
                EventsPaginationReturnModel returnModel = new EventsPaginationReturnModel();
                var eventsForContact = await weedb.EventAttendees.AsNoTracking().Where(w => w.ContactId == contactId).Select(w => w.EventId).ToListAsync();
                var events = await weedb.EventModels.AsNoTracking().Where(w => eventsForContact.Contains(w.EventId)).ToListAsync();
                if (events.Count > 0)
                {
                    if (!isPast)
                    {
                        var futureEvents = events
                       .Where(w => w.End > DateTimeOffset.Now.Subtract(TimeSpan.FromMinutes(5)))
                       .ToList();
                        if (futureEvents.Count > 0)
                        {
                            QuickSort(futureEvents, 0, futureEvents.Count - 1);
                            var allEvents = futureEvents;
                            futureEvents = futureEvents.Skip((pageNumber - 1) * size).Take(size).ToList();
                            returnModel.cardId = "6E043DD5-20A3-41B0-BE9B-B51D6D1DF469";
                            returnModel.value = futureEvents;
                            var nextContactList = allEvents.Skip(((pageNumber + 1) - 1) * size).Take(size).ToList();
                            if (nextContactList.Count() > 0)
                            {
                                returnModel.isNext = true;
                            }
                            else
                            {
                                returnModel.isNext = false;
                            }
                            return Ok(returnModel);
                        }
                        return BadRequest(CommonData.ErrorCodes.E00010);

                    }
                    else
                    {

                        var pastEvents = events.Where(w => w.End < DateTimeOffset.Now)
                        .ToList();
                        if (pastEvents.Count > 0)
                        {
                            QuickSort(pastEvents, 0, pastEvents.Count - 1, true);
                            var allEvents = pastEvents;
                            pastEvents = pastEvents.Skip((pageNumber - 1) * size).Take(size).ToList();
                            returnModel.cardId = "19C14CA5-41BE-4ED7-BAB6-CEBA398746A5";
                            returnModel.value = pastEvents;
                            var nextContactList = allEvents.Skip(((pageNumber + 1) - 1) * size).Take(size).ToList();
                            if (nextContactList.Count() > 0)
                            {
                                returnModel.isNext = true;
                            }
                            else
                            {
                                returnModel.isNext = false;
                            }
                            return Ok(returnModel);
                        }
                        else
                        {
                            return BadRequest(CommonData.ErrorCodes.E00011);
                        }

                    }
                }
                return BadRequest(CommonData.ErrorCodes.E0009);

            }
            catch (Exception ex)
            {

                return BadRequest(ex);
            }

        }
        public static void QuickSort(List<OnePage.Models.Models.ENTEventModel.EventModel> eventModels, int left, int right, bool isDescending = false)
        {
            if (left < right)
            {
                int pivotIndex = Partition(eventModels, left, right, isDescending);
                QuickSort(eventModels, left, pivotIndex - 1, isDescending);
                QuickSort(eventModels, pivotIndex + 1, right, isDescending);
            }

        }

        public static int Partition(List<OnePage.Models.Models.ENTEventModel.EventModel> eventModels, int left, int right, bool isDescending = false)
        {
            OnePage.Models.Models.ENTEventModel.EventModel pivotValue = eventModels[right];
            int pivotIndex = left;

            for (int i = left; i < right; i++)
            {
                if (isDescending)
                {
                    if (eventModels[i].LStart > pivotValue.LStart)

                    {
                        Swap(eventModels, i, pivotIndex);
                        pivotIndex++;
                    }
                }
                else
                {
                    if (eventModels[i].LStart > pivotValue.LStart)

                    {
                        Swap(eventModels, i, pivotIndex);
                        pivotIndex++;
                    }

                }
            }

            Swap(eventModels, pivotIndex, right);
            return pivotIndex;

        }
        public static void Swap(List<OnePage.Models.Models.ENTEventModel.EventModel> eventModels, int i, int j)
        {
            OnePage.Models.Models.ENTEventModel.EventModel temp = eventModels[i];
            eventModels[i] = eventModels[j];
            eventModels[j] = temp;
        }
        [HttpPost]
        [Route("88522BC8")]
        public async Task<IActionResult> ProcessWebhook(Guid userId)
        {
            try
            {
                var userInfo = _redisCaching.CheckForItem("userDetailInfo_" + userId, () => wepdb.Users.FirstOrDefault(w => w.Id == userId), CommonData.CustomJsonSerializer.Serialize, JsonConvert.DeserializeObject<User>, DateTimeOffset.Now.AddHours(1));
                bool profileExists = await weedb.Contacts.AsNoTracking().AnyAsync(w => w.UserId == userId && w.OtherUserId == userId);
                if (!profileExists)
                {
                    await CreateProfileContact(userId);
                }
                var profile = weedb.Contacts.Where(w => w.UserId == userId && w.OtherUserId == userId).FirstOrDefault();
                List<string> profileEmails = weedb.ContactEmails.Where(x => x.ContactId == profile.Id && x.IsActive == true).Select(w => w.Email).ToList();
                var orgs = weodb.OrgUsers.Where(w => w.UserId == userId && w.IsActive == true);
                foreach (var org in orgs)
                {
                    var orgProviders = weodb.OrgProviders.Where(w => w.OrgId == org.OrgId && w.IsActive == true).ToList();
                    foreach (var orgProvider in orgProviders)
                    {
                        switch (orgProvider.ProviderId.ToString().ToLower())
                        {
                            case "deaa4c8d-b0bb-4d81-bb86-055944e207d7":
                                try
                                {
                                    var jsonFileContent = await fileService.DownloadFile("Google/" + org.OrgId.ToString().ToUpper() + ".json");
                                    await calendarService.GetGoogleEvents(jsonFileContent, userInfo.Email, userInfo.Id, profileEmails);
                                }
                                catch (Exception ex)
                                {

                                }
                                break;
                            case "a2dbf10e-08c5-4aa5-b198-8bbd2a860a66":
                                try
                                {
                                    var jsonFileContent2 = await fileService.DownloadFile("Microsoft/" + org.OrgId.ToString().ToUpper() + ".json");
                                    await calendarService.GetMicrosoftEvents(jsonFileContent2, userInfo.Email, userInfo.Id, profileEmails);
                                }
                                catch (Exception ex)
                                {

                                }
                                break;
                            default:
                                break;
                        }
                    }
                }
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.ToString());
            }
        }

        public class EntEventDetailReturnModel
        {
            public OnePage.Models.Models.ENTEventModel.EventModel EventData { get; set; }
            public List<AttendeesModel> attendeesModels { get; set; } = new();
        }


        [Route("EE55D9C5")]
        [HttpPost]
        public async Task<IActionResult> UpdateSelectedCaLEndars([FromBody] List<OnePage.Models.Models.ENTEventModel.CalendarModel> callList)
        {
            var userId = _authService.GetUser(Request);
            var token = _authService.GetToken(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized();
            }
            try
            {
                using (var db = weedb)
                {
                    foreach (var dt in callList)
                    {


                        var calendarModelData = db.CalendarModels.Where(w => w.UserId == userId && w.RefId == dt.RefId).ToList();
                        if (calendarModelData != null)
                        {
                            foreach (var data in calendarModelData)
                            {
                                data.IsSelected = dt.IsSelected;
                                db.SaveChanges();
                            }
                        }

                    }

                }

                return Ok();
            }
            catch (Exception ex)
            {
                // telemetryTracker.TrackException(ex, userId);
                return BadRequest(ex.StackTrace.ToString());
            }
        }

        public class EntEventDetailWithCardIdsModel
        {
            public Guid CardId { get; set; }
            public object CardContent { get; set; }
        }

        public class EntEventDataReturnModel
        {
            public Guid EventId { get; set; }
            public string Desc { get; set; }
            public List<EntEventDetailWithCardIdsModel> DataList { get; set; } = new List<EntEventDetailWithCardIdsModel>();
            public string HtmlBody { get; set; }
        }
        public class EventDetailReturnModel
        {
            public EventModel EventData { get; set; }
            public List<AttendeesModel> attendeesModels { get; set; } = new();
        }
        [HttpPost]
        [Route("B32B822F")]
        public async Task<IActionResult> GetEventDetailWithCardIds(string eventId, string id, string meetinglink = "")
        {
            var userId = _authService.GetUser(Request);
            var token = _authService.GetToken(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized("Wrong Token");
            }

            try
            {
                EventDetailReturnModel detailReturnModel = new EventDetailReturnModel();
                OnePage.Models.Models.ENTEventModel.EventModel eventData = new OnePage.Models.Models.ENTEventModel.EventModel();
                //var eventDetailCache = await _redisCaching.GetAsync<EventModel>("eventDetail_" + userId + "_" + id);


                var user = await wepdb.Users.AsNoTracking().Where(x => x.Id == userId).FirstOrDefaultAsync();
                var slice = user?.Email.Split('@');
                var isFreeDomain = await weddb.FreeDomains.AsNoTracking().AnyAsync(x => x.DomainName.ToLower() == slice[1].ToLower());




                if (!string.IsNullOrEmpty(meetinglink))
                {
                    var eventDataExists = await weedb.EventModels.AsNoTracking().AnyAsync(w => w.OnlineMeetingLink.Contains(meetinglink));
                    if (eventDataExists)
                    {
                        eventData = await weedb.EventModels.AsNoTracking().Where(w => w.OnlineMeetingLink.Contains(meetinglink))
                            .FirstOrDefaultAsync();
                    }
                    else if (!string.IsNullOrEmpty(id))
                    {
                        Guid eid = Guid.Parse(id);
                        var idExists = await weedb.EventModels.AsNoTracking().AnyAsync(w => w.Id == eid && w.UserId == userId);
                        if (idExists)
                        {
                            eventData = await weedb.EventModels.AsNoTracking().Where(w => w.Id == eid && w.UserId == userId).FirstOrDefaultAsync();
                        }
                        else
                        {
                            var meetingIdExists = await weedb.EventModels.AsNoTracking().AnyAsync(w => w.HTMLBody.Contains(eventId) && w.UserId == userId);
                            if (meetingIdExists)
                            {
                                eventData = await weedb.EventModels.AsNoTracking().Where(w => w.HTMLBody.Contains(eventId) && w.UserId == userId).FirstOrDefaultAsync();
                            }
                        }
                    }
                }
                else if (!string.IsNullOrEmpty(id))
                {
                    Guid eid = Guid.Parse(id);
                    var eventDataExists = await weedb.EventModels.AsNoTracking().AnyAsync(w => w.Id == eid && w.UserId == userId);
                    if (eventDataExists)
                    {
                        eventData = await weedb.EventModels.AsNoTracking().Where(w => w.Id == eid && w.UserId == userId).FirstOrDefaultAsync();
                    }
                    else
                    {
                        var meetingIdExists = await weedb.EventModels.AsNoTracking().AnyAsync(w => w.EventId.Contains(eventId) && w.UserId == userId);
                        if (meetingIdExists)
                        {
                            eventData = await weedb.EventModels.AsNoTracking().Where(w => w.EventId.Contains(eventId) && w.UserId == userId).FirstOrDefaultAsync();
                        }
                    }
                }
                else
                {
                    var meetingIdExists =
                        await weedb.EventModels.AsNoTracking().AnyAsync(w => w.HTMLBody.Contains(eventId) && w.UserId == userId);
                    if (meetingIdExists)
                    {
                        eventData = await weedb.EventModels.AsNoTracking().Where(w => w.HTMLBody.Contains(eventId) && w.UserId == userId)
                            .FirstOrDefaultAsync();
                    }
                }

                List<string> contactId = new List<string>();
                List<OnePage.Models.Models.ENTEventModel.EventAttendee> eventAttendees = new List<OnePage.Models.Models.ENTEventModel.EventAttendee>();
                if (!string.IsNullOrEmpty(eventData.EventId))
                {
                    if (eventData.EventAttendees.Count == 0)
                    {
                        var attendees = await weedb.EventAttendees.AsNoTracking().Where(w =>
                            w.EventId == eventData.EventId && w.UserId == userId && (w.IsActive == true || w.IsActive == null) &&
                            (w.IsUser == false || w.IsUser == null)).ToListAsync();
                        var emptyAddressAttendees = attendees.Where(w => string.IsNullOrEmpty(w.Address)).ToList();
                        var uniqueAttendees = attendees.DistinctBy(x => x.Address).ToList();
                        eventData.EventAttendees.AddRange(uniqueAttendees);
                        foreach (var emptyAddressAttendee in emptyAddressAttendees)
                        {
                            if (!eventData.EventAttendees.Any(x => x.Id == emptyAddressAttendee.Id))
                            {
                                eventData.EventAttendees.Add(emptyAddressAttendee);
                            }
                        }
                    }
                }
                else
                {
                    //await calendarService.ProcessEvents(userId, token, "", true, true);
                    if (!string.IsNullOrEmpty(meetinglink))
                    {
                        var eventDataExists = await weedb.EventModels.AsNoTracking().AnyAsync(w => w.OnlineMeetingLink.Contains(meetinglink));
                        if (eventDataExists)
                        {
                            eventData = await weedb.EventModels.AsNoTracking().Where(w => w.OnlineMeetingLink.Contains(meetinglink))
                                .FirstOrDefaultAsync();
                        }
                        else if (!string.IsNullOrEmpty(id))
                        {
                            Guid eid = Guid.Parse(id);
                            var idExists = await weedb.EventModels.AsNoTracking().AnyAsync(w => w.Id == eid);
                            if (idExists)
                            {
                                eventData = await weedb.EventModels.AsNoTracking().Where(w => w.Id == eid).FirstOrDefaultAsync();
                            }
                            else
                            {
                                var meetingIdExists = await weedb.EventModels.AsNoTracking().AnyAsync(w => w.HTMLBody.Contains(eventId));
                                if (meetingIdExists)
                                {
                                    eventData = await weedb.EventModels.AsNoTracking().Where(w => w.HTMLBody.Contains(eventId))
                                        .FirstOrDefaultAsync();
                                }
                            }
                        }
                    }
                    else
                    {
                        Guid eid = Guid.Parse(id);
                        var eventDataExists2 = await weedb.EventModels.AsNoTracking().AnyAsync(w => w.Id == eid);
                        ;
                        if (eventDataExists2)
                        {
                            eventData = await weedb.EventModels.AsNoTracking().Where(w => w.Id == eid).FirstOrDefaultAsync();
                        }
                        else
                        {
                            var meetingIdExists2 = await weedb.EventModels.AsNoTracking().AnyAsync(w => w.HTMLBody.Contains(eventId));
                            if (meetingIdExists2)
                            {
                                eventData = await weedb.EventModels.AsNoTracking().Where(w => w.HTMLBody.Contains(eventId)).FirstOrDefaultAsync();
                            }
                        }
                    }
                }

                var orgUser = await weodb.OrgUsers.AsNoTracking().Where(w =>
                        w.UserId == userId && w.Org.Name.ToLower() == slice[1].ToLower() && w.IsActive == true)
                    .Include(x => x.Org).ToListAsync();
                var orgId = orgUser
                    .Where(x => x.Org.AppId.ToLower() == user.AppId.ToString().ToLower() &&
                                x.Org.IsActive == true).Select(x => x.Org.Id).FirstOrDefault();
                if (eventData != null)
                {
                    if (eventData.EventAttendees.Count == 0)
                    {
                        var attendees = await weedb.EventAttendees.AsNoTracking().Where(w =>
                                w.EventId == eventData.EventId && w.UserId == userId && (w.IsActive == true || w.IsActive == null) &&
                                (w.IsUser == false || w.IsUser == null))
                            .ToListAsync();
                        var emptyAddressAttendees = attendees.Where(w => string.IsNullOrEmpty(w.Address)).ToList();
                        var uniqueAttendees = attendees.DistinctBy(x => x.Address).ToList();
                        eventData.EventAttendees.AddRange(uniqueAttendees);
                        eventData.EventAttendees.AddRange(emptyAddressAttendees);
                    }

                    foreach (var attItem in eventData.EventAttendees.ToList())
                    {
                        var stringReplace1 = attItem.Address.Replace("|", "");
                        var contactData = await weedb.Contacts.AsNoTracking().Where(w => w.Id == attItem.ContactId).FirstOrDefaultAsync();
                        if (contactData != null)
                        {
                            if (string.IsNullOrEmpty(attItem.Name))
                            {
                                attItem.Name = contactData.FirstName + " " + contactData.LastName;
                            }
                            if (!isFreeDomain)
                            {
                                var contactPersonExists = await db.OrgPeople.AsNoTracking().AnyAsync(w => w.OrgId == orgId && w.EmailId.ToLower() == stringReplace1.ToLower());
                                if (contactPersonExists)
                                {

                                    var cp = await db.OrgPeople.AsNoTracking().FirstOrDefaultAsync(w => w.OrgId == orgId && w.EmailId.ToLower() == stringReplace1.ToLower());
                                    var status = entRequestResearchService.GetResearchStatus(cp);
                                    attItem.Status = status.ToString();
                                    attItem.profileImage = CommonData.S3BucketUrlForPic + cp.PersonId;
                                    attItem.company = contactData.Company;
                                    attItem.role = contactData.Desig;
                                }

                            }
                            else
                            {
                                var cp = await db.ContactPeople.AsNoTracking().FirstOrDefaultAsync(w => w.ContactId == contactData.Id);
                                if (cp != null)
                                {
                                    var status = entRequestResearchService.GetResearchStatus(cp);
                                    attItem.Status = status.ToString();
                                    attItem.profileImage = CommonData.S3BucketUrlForPic + cp.PersonId;
                                    attItem.company = contactData.Company;
                                    attItem.role = contactData.Desig;
                                }
                                else
                                {
                                    attItem.company = contactData.Company;
                                    attItem.role = contactData.Desig;
                                    attItem.Status = "0";
                                }
                            }
                        }
                    }
                }
                //await _redisCaching.SaveAsync("eventDetail_" + userId + "_" + id, eventData, TimeSpan.FromDays(5));

                var eventDetailCards = await weddb.Cards.AsNoTracking().Where(w => w.IsActive == true && w.AppId.Contains(CommonData.GlobalAppId.ToString()) && w.PageType == 3).ToListAsync();
                List<EventDetailWithCardIdsModel> eventDataList = new List<EventDetailWithCardIdsModel>();
                foreach (var item in eventDetailCards)
                {
                    EventDetailWithCardIdsModel eventItem = new EventDetailWithCardIdsModel();
                    eventItem.CardId = item.Id;
                    switch (item.Id.ToString().ToUpper())
                    {
                        case "A7273D52-8DEA-415B-A82D-23A1FEEC71C3": // event card
                            eventItem.CardContent = eventData;
                            break;
                        case "BBAB9DC7-E15D-481C-9788-7D671431F9E1": // Attendees
                            eventItem.CardContent = eventData.EventAttendees.ToList();
                            break;
                        case "51234EA9-2DE0-40E8-8FD4-364A90F011DA": // Event Description
                            eventItem.CardContent = eventData.Desc;
                            break;
                        case "3F4FFDC5-FF22-409F-A7C8-A623F81B1F5B": // Notes
                            eventItem.CardContent = eventData.Notes;
                            break;
                        case "0A1F8F73-B796-488B-B5EC-C5E2D6A5CF15": // Links
                            eventItem.CardContent = eventData.HTMLBody;
                            break;
                        case "E485F702-BBD0-4749-9870-B9DC4AB51845": // Organisations
                            eventItem.CardContent = eventData.EventAttendees.ToList();
                            break;
                        case "EF9D6123-2EF5-4195-BA04-B2A2C053B92B": // Join Meeting
                            eventItem.CardContent = eventData.HTMLBody;
                            break;
                        case "9306983E-42C8-45F0-A591-138B617B4F74"://Location
                            eventItem.CardContent = eventData.Location;
                            break;
                        default:
                            break;
                    }

                    eventDataList.Add(eventItem);
                }

                EventDataReturnModel returnModel = new EventDataReturnModel();
                returnModel.EventId = eventData.Id;
                returnModel.HtmlBody = eventData.HTMLBody;
                returnModel.Desc = eventData.Desc;
                returnModel.DataList = eventDataList.ToList();
                return Ok(returnModel);
            }
            catch (Exception ex)
            {

                BadRequest("Error Occured" + ex.Message);
            }

            return BadRequest("Error Occured");
        }
        public class EventAttachmentCardModel
        {
            public Guid CardId { get; set; }
            public object CardContent { get; set; }

            public bool IsApiShouldBeCalled { get; set; } = false;
        }
        [HttpPost]
        [Route("F57FAE19")]
        public async Task<IActionResult> GetEventAttachmentWithCardIds(Guid eventId)
        {
            var userId = _authService.GetUser(Request);
            var tokenId = _authService.GetToken(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized();
            }

            List<EventAttachmentCardModel> returnCardList = new List<EventAttachmentCardModel>();
            var eventData = await weedb.EventModels.AsNoTracking().Where(x => x.Id == eventId).FirstOrDefaultAsync();
            if (eventData != null)
            {
                var EventAttachments = await calendarService.GetAttachments(userId, eventData.EventId, eventData.ProviderId.ToString()); ;
                EventAttachmentCardModel cardModel = new EventAttachmentCardModel();
                cardModel.CardId = Guid.Parse("A072C8BC-DEF7-417F-A69F-15E19ED65192");
                cardModel.CardContent = EventAttachments;
                if (eventData.ProviderId == CommonData.ZohoCalendarProviderId)
                {
                    cardModel.IsApiShouldBeCalled = true;
                }
                returnCardList.Add(cardModel);
                return Ok(returnCardList);
            }
            else
            {
                return BadRequest("Event is not present");
            }
        }
        public class EventDataReturnModel
        {
            public Guid EventId { get; set; }
            public string Desc { get; set; }
            public List<EventDetailWithCardIdsModel> DataList { get; set; } = new List<EventDetailWithCardIdsModel>();
            public string HtmlBody { get; set; }
        }

        public class EventDetailWithCardIdsModel
        {
            public Guid CardId { get; set; }
            public object CardContent { get; set; }
        }


        //[HttpPost]
        //[Route("")]
        //public async Task<IActionResult> GetEventDetail(string eventId, string id, string meetinglink = "")
        //{

        //    var userId = _authService.GetUser(Request);
        //    if (userId == null)
        //    {
        //        return BadRequest("user not present");

        //    }
        //    try
        //    {

        //        OnePage.Models.Models.ENTEventModel.EventModel eventData = new OnePage.Models.Models.ENTEventModel.EventModel();
        //        var user = await wepdb.Users.Where(x => x.Id == userId).AsNoTracking().FirstOrDefaultAsync();
        //        var isFreeDomain = await wepdb.EmailWhiteLists.AsNoTracking().AnyAsync(x => x.Email.ToLower() == user.Email.ToLower() && x.IsActive == true);
        //        var slice = user.Email.Split('@');

        //        var orgUser = await weodb.OrgUsers.Where(w =>
        //        w.UserId == userId && w.Org.Name.ToLower() == slice[1].ToLower() && w.IsActive == true).Include(x => x.Org).AsNoTracking().ToListAsync();
        //        var orgId = orgUser.Where(x => x.Org.AppId.ToLower() == user.AppId.ToString().ToLower() && x.Org.IsActive == true).Select(x => x.Org.Id).FirstOrDefault();
        //        if (!string.IsNullOrEmpty(meetinglink))
        //        {
        //            var eventDataExists =await weedb.EventModels.AsNoTracking().AnyAsync(w => w.OnlineMeetingLink.Contains(meetinglink));
        //            if (eventDataExists)
        //            {
        //                eventData =await weedb.EventModels.Where(w => w.OnlineMeetingLink.Contains(meetinglink)).AsNoTracking().FirstOrDefaultAsync();
        //            }
        //            else if (!string.IsNullOrEmpty(id))
        //            {
        //                Guid eid = Guid.Parse(id);
        //                var idExists = await weedb.EventModels.AsNoTracking().AnyAsync(w => w.Id == eid);
        //                if (idExists)
        //                {
        //                    eventData = await weedb.EventModels.Where(w => w.Id == eid).AsNoTracking().FirstOrDefaultAsync();
        //                }
        //                else
        //                {

        //                    var meetingIdExists =await  weedb.EventModels.AsNoTracking().AnyAsync(w => w.HTMLBody.Contains(eventId));
        //                    if (meetingIdExists)
        //                    {
        //                        eventData =await weedb.EventModels.Where(w => w.HTMLBody.Contains(eventId)).AsNoTracking().FirstOrDefaultAsync();
        //                    }
        //                }
        //            }
        //        }
        //        else if (!string.IsNullOrEmpty(id))
        //        {
        //            Guid eid = Guid.Parse(id);
        //            var eventDataExists = await weedb.EventModels.AsNoTracking().AnyAsync(w => w.Id == eid);
        //            if (eventDataExists)
        //            {
        //                eventData =await weedb.EventModels.Where(w => w.Id == eid).AsNoTracking().FirstOrDefaultAsync();
        //            }
        //            else
        //            {
        //                var meetingIdExists = await weedb.EventModels.AsNoTracking().AnyAsync(w => w.EventId.Contains(eventId));
        //                if (meetingIdExists)
        //                {
        //                    eventData =await weedb.EventModels.Where(w => w.EventId.Contains(eventId)).AsNoTracking().FirstOrDefaultAsync();
        //                }
        //            }
        //        }
        //        else
        //        {
        //            var meetingIdExists =  await weedb.EventModels.AsNoTracking().AnyAsync(w => w.HTMLBody.Contains(eventId));
        //            if (meetingIdExists)
        //            {
        //                eventData =await  weedb.EventModels.Where(w => w.HTMLBody.Contains(eventId)).AsNoTracking().FirstOrDefaultAsync();
        //            }
        //        }
        //        List<string> contactId = new List<string>();
        //        List<OnePage.Models.Models.EventAttendee> eventAttendees = new List<OnePage.Models.Models.EventAttendee>();
        //        if (!string.IsNullOrEmpty(eventData.EventId))
        //        {
        //            var EventAttendees =await weedb.EventAttendees.Where(x => x.EventId == eventData.EventId && x.UserId == userId && (x.IsUser == false || x.IsUser == null)).AsNoTracking().ToListAsync();
        //            if (EventAttendees.Count == 0)
        //            {
        //                var attendees = await weedb.EventAttendees.Where(w => w.EventId == eventData.EventId && w.UserId == userId && (w.IsUser == false || w.IsUser == null)).AsNoTracking().ToListAsync();
        //                EventAttendees = attendees.DistinctBy(x => x.Address).ToList();
        //                eventData.EventAttendees = EventAttendees;
        //            }
        //        }

        //        if (eventData != null)
        //        {

        //            var attendees =await  weedb.EventAttendees.Where(w => w.EventId == eventData.EventId && w.UserId == userId && (w.IsUser == false || w.IsUser == null)).AsNoTracking().ToListAsync();
        //            var EventAttendees = attendees.DistinctBy(x => x.Address).ToList();
        //            eventData.EventAttendees = EventAttendees;
        //            foreach (var attItem in EventAttendees.ToList())
        //            {
        //                var contactData = weedb.Contacts.Where(w => w.Id == attItem.ContactId).FirstOrDefault();
        //                if (attItem.ContactId != null)
        //                {


        //                    if (!isFreeDomain)
        //                    {

        //                        var stringReplace1 =
        //                           contactData?.EmailDisplay.Replace('|'.ToString(), "");
        //                        var cp = await db.OrgPeople.AsNoTracking().FirstOrDefaultAsync(w => w.OrgId == orgId && w.EmailId.ToLower() == stringReplace1.ToLower());
        //                        //if (cp.IsInsightsFound == false)
        //                        //{
        //                        //    attItem.Status = "4";
        //                        //}

        //                        //if (attItem.Status != "4")
        //                        //{
        //                        if (cp != null)
        //                        {

        //                            attItem.Status = Convert.ToString(entRequestResearchService.GetResearchStatus(cp));
        //                        }
        //                        else
        //                        {
        //                            attItem.Status = "0";
        //                        }
        //                        //  }


        //                    }
        //                    else
        //                    {
        //                        var cp = await db.ContactPeople.AsNoTracking().FirstAsync(w => w.ContactId == contactData.Id);
        //                        if (cp.InsightsFound != null)
        //                        {
        //                            if (cp.InsightsFound == false)
        //                            {
        //                                attItem.Status = "4";
        //                            }
        //                        }
        //                        if (attItem.Status != "4")
        //                        {

        //                            attItem.Status = Convert.ToString(entRequestResearchService.GetResearchStatus(cp));



        //                        }

        //                        else
        //                        {
        //                            if (!string.IsNullOrEmpty(attItem.Address))
        //                            {
        //                                var isPersonEmail = db.PersonEmails.Any(w => w.Email == attItem.Address && w.IsActive == true);
        //                                if (isPersonEmail)
        //                                {
        //                                    ContactPerson contactPerson = new ContactPerson();
        //                                    Guid ctId = (Guid)(attItem.ContactId);
        //                                    contactPerson.ContactId = ctId;
        //                                    contactPerson.CreatedDate = DateTime.UtcNow;
        //                                    contactPerson.Email = attItem.Address;
        //                                    contactPerson.Id = Guid.NewGuid();
        //                                    contactPerson.IsMatchFound = true;
        //                                    contactPerson.IsRedeemed = false;
        //                                    contactPerson.IsRequested = false;
        //                                    contactPerson.IsSearchComplete = true;
        //                                    contactPerson.PersonId = null;
        //                                    db.ContactPeople.Add(contactPerson);
        //                                    db.SaveChanges();


        //                                    attItem.Status = "0";
        //                                }
        //                                else
        //                                {
        //                                    attItem.Status = "0";

        //                                }
        //                            }
        //                        }
        //                    }




        //                    eventData.EventAttachments = await calendarService.GetAttachments(userId, eventId, eventData.ProviderId.ToString());

        //                }
        //            }
        //            try//EventDetail
        //            {
        //                TrackMetadata trackMetadata = new TrackMetadata();

        //                trackMetadata.user_id = userId.ToString();
        //                trackMetadata.action = "EventDetail";
        //                trackMetadata.deviceId = wepdb.UserDevices.Where(x => x.UserId == userId && x.IsActive == true)
        //                    .Select(x => x.Id).FirstOrDefault().ToString();
        //                trackMetadata.loginWebsite = "Ent";
        //                trackMetadata.isFreeDomain = isFreeDomain.ToString();

        //                if (user.IsTestUser != null)
        //                {
        //                    trackMetadata.isTestUser = user.IsTestUser.ToString();
        //                }
        //                metricsService.TrackUsuage(trackMetadata, "events");
        //            }
        //            catch (Exception ex)
        //            {
        //                Models.Models.LogModel logModel = new Models.Models.LogModel()
        //                {
        //                    ErrorMessage = ex.Message,
        //                    Id = userId.ToString(),
        //                    //InputUsed = jsonKey,
        //                    Source = "Event Detail Error Service 1",
        //                    StackTrace = ex.StackTrace
        //                };

        //                await SESService.SendErrorMailsEnt(logModel);
        //            }



        //        }
        //        return Ok(eventData);

        //    }
        //    catch (Exception ex)
        //    {
        //        Models.Models.LogModel logModel = new Models.Models.LogModel()
        //        {
        //            ErrorMessage = ex.Message,
        //            Id = userId.ToString(),
        //            //InputUsed = jsonKey,
        //            Source = "Event Detail Error Service 2",
        //            StackTrace = ex.StackTrace
        //        };

        //        await SESService.SendErrorMailsEnt(logModel);
        //        return BadRequest(ex);

        //    }
        //}

        [HttpPost]
        [Route("09A5E2C7")]
        public async Task<IActionResult> GeteamsUP()
        {
            var userId = _authService.GetUser(Request);
            var token = _authService.GetToken(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized("Wrong Token");
            }

            try
            {
                Guid TeamsProviderId = Guid.Parse("825E7926-04DB-409F-8BCB-F48ED904156C");
                var isUserProviderExists = wepdb.UserProviders.Any(w =>
                    w.UserId == userId && w.ProviderId == TeamsProviderId && w.IsActive == true);
                return Ok(isUserProviderExists);
            }
            catch (Exception ex)
            {

                return BadRequest(ex.ToString());
            }
        }
        async Task<List<EventModel>> GetEventsMethod(bool isNow, List<EventModel> eventList, Guid userId, bool isTomorrow = false)
        {
            List<EventModel> output = new List<EventModel>();
            try
            {
                var entEventRds = new DbContextOptionsBuilder<ENTEventContext>();
                entEventRds.UseSqlServer(_configuration["DBConnections:WEENTEventConnectionString"]);
                using (var entDb = new ENTEventContext(entEventRds.Options))
                {
                    Guid timezoneSettingid = Guid.Parse("1D9BC314-786C-40E4-9FE4-769860C58F02");
                    var timezoneSetting = wepdb.UserSettings
                        .First(x => x.SettingId == timezoneSettingid && x.UserId == userId).Value;
                    int hourToAdd = 0;
                    int minutesToAdd = 0;

                    if (!string.IsNullOrEmpty(timezoneSetting))
                    {
                        var split = timezoneSetting.Split(":");
                        hourToAdd = int.Parse(split[0]);
                        minutesToAdd = int.Parse(split[1]);
                    }

                    TimeSpan timespan = new TimeSpan(hourToAdd, minutesToAdd, 0);
                    DateTimeOffset utcNow = DateTime.UtcNow;
                    DateTimeOffset today = utcNow.ToOffset(timespan);

                    DateTimeOffset utcnow2 = DateTime.UtcNow;


                    DateTimeOffset endLimit = utcnow2.ToOffset(timespan).Subtract(TimeSpan.FromMinutes(1));

                    DateTimeOffset tomorrow = today.AddHours(24);
                    List<OnePage.Models.Models.ENTEventModel.EventModel> tempEvents = new List<OnePage.Models.Models.ENTEventModel.EventModel>();
                    if (!isNow)
                    {
                        if (isTomorrow)
                        {
                            today = today.AddDays(1);
                            tomorrow = tomorrow.AddDays(1);
                        }
                        DateTimeOffset yesterday = today.AddHours(-24);
                        long yLong = yesterday.ToUnixTimeSeconds();
                        var todayDate = DateTimeOffset.UtcNow.Date;
                        DateTimeOffset tommorrowfortodaylist = todayDate.AddHours(24).AddMinutes(-1);
                        if (isTomorrow)
                        {

                            tommorrowfortodaylist = todayDate.AddHours(48).AddMinutes(-1);
                        }
                        long endLimitLong = today.ToUnixTimeSeconds();
                        long tomorrowLong = tommorrowfortodaylist.ToUnixTimeSeconds();
                        bool todayEvents = eventList.Any(w => w.LEnd > endLimitLong && w.LStart < tomorrowLong && w.IsActive == true && w.BTwo == true);
                        if (todayEvents)
                        {
                            tempEvents = (eventList.Where(w => w.LEnd > endLimitLong && w.LStart < tomorrowLong && w.IsActive == true && w.BTwo == true)).OrderBy(w => w.Start).ToList();
                        }

                    }
                    else
                    {
                        DateTimeOffset now = DateTimeOffset.Now;
                        DateTimeOffset startLimit = now.Subtract(TimeSpan.FromMinutes(-1));
                        bool todayEvents = eventList.Any(w => w.End > now && w.Start < startLimit && w.IsActive == true && w.BTwo == true);
                        if (todayEvents)
                        {
                            tempEvents = (eventList.Where(w => w.End > now && w.Start < startLimit && w.IsActive == true && w.BTwo == true)).OrderBy(w => w.Start).ToList();
                        }
                    }
                    DateTimeOffset minusOne = today.AddMinutes(-1);
                    DateTimeOffset plusOne = tomorrow.AddMinutes(1);
                    var allDayEvents = eventList.Where(w => w.IsAllDay == true && w.Start > minusOne && w.End < plusOne && w.IsActive == true && w.BTwo == true).ToList();
                    if (allDayEvents.Count > 0)
                    {
                        foreach (var item in allDayEvents.ToList())
                        {
                            if (tempEvents.Any(w => w.Id == item.Id))
                                continue;
                            else
                                tempEvents.Add(item);
                        }
                        tempEvents = tempEvents.OrderBy(w => w.Start).ToList();
                    }
                    foreach (var tempEvent in tempEvents.ToList())
                    {
                        var iscalendarSelect = entDb.CalendarModels.Any(x => x.IsSelected == true && x.RefId == tempEvent.CalId && x.UserId == userId);
                        if (iscalendarSelect)
                        {
                            if (!string.IsNullOrEmpty(tempEvent.Subject))
                                tempEvent.Subject = tempEvent.Subject.Trim();

                            if (tempEvent.Desc != null)
                            {
                                tempEvent.Desc = tempEvent.Desc.Trim();
                            }
                            //var entEventRds1 = new DbContextOptionsBuilder<ENTEventContext>();
                            //entEventRds.UseSqlServer(_configuration["DBConnections:WEENTEventConnectionString"]);
                            //using (var entDb1 = new ENTEventContext(entEventRds1.Options))
                            //{ 

                            var checkForBTwoExists = await weedb.CalendarModels.AsNoTracking().AnyAsync(w => w.RefId == tempEvent.CalId);
                            if (checkForBTwoExists)
                            {
                                var checkForBTwo = weedb.CalendarModels.FirstOrDefault(w => w.RefId == tempEvent.CalId);
                                tempEvent.BTwo = checkForBTwo.IsSelected;
                            }
                            if (tempEvent.EventAttendees.Count == 0)
                            {
                                var attendees = weedb.EventAttendees.Where(w => w.EventId == tempEvent.EventId && (w.BOne == true || w.BOne == null) && (w.IsUser == false || w.IsUser == null) && w.UserId == tempEvent.UserId).ToList();
                                tempEvent.EventAttendees = attendees;
                            }
                            if (tempEvent.EventAttachments.Count == 0)
                            {
                                var attachment = entDb.EventAttachments.Where(x => x.EventId == tempEvent.EventId).ToList();
                                tempEvent.EventAttachments = attachment;
                            }
                        }
                        if (output.Any(w => w.EventId == tempEvent.EventId))
                            continue;
                        output.Add(tempEvent);
                    }

                }
            }
            catch (Exception ex)
            {
                return null;
            }
            return output;
        }

        public async Task CreateProfileContact(Guid userGuid)
        {
            try
            {
                var user = wepdb.Users.First(w => w.Id == userGuid);
                UserProvider userProvider = _redisCaching.CheckForItem("up_" + userGuid + "_" + CommonData.ContactAddedByAdmin, () => wepdb.UserProviders.FirstOrDefault(w => w.UserId == userGuid && w.ProviderId == CommonData.ContactAddedByAdmin), CustomJsonSerializer.Serialize, JsonConvert.DeserializeObject<UserProvider>, DateTimeOffset.Now.AddMinutes(10));

                if (userProvider != null)
                {
                    Contact profile = new()
                    {
                        FirstName = user.FirstName,
                        LastName = user.LastName,
                        Display = $"{user.FirstName} {user.LastName}",
                        UserId = userGuid,
                        OtherUserId = userGuid,
                        IsActive = true,
                        ProviderId = CommonData.ContactAddedByAdmin,
                        UserProviderId = userProvider.Id,
                        Id = Guid.NewGuid()
                    };
                    weedb.Contacts.Add(profile);
                    weedb.SaveChanges();
                }
                else
                {
                    var provision = new Models.Models.OrgModel.Provision();
                    provision.Id = Guid.NewGuid();
                    provision.OrgId = CommonData.WhatElseCustomerOrgId;
                    provision.UserCustomerId = user.ChargebeeCustomerId;
                    provision.AppId = user.AppId;
                    provision.ProviderTypeId = CommonData.DeviceContactsProviderTyepId;
                    provision.ProviderId = CommonData.ContactAddedByAdmin;
                    provision.UserId = user.Id;
                    provision.UserProviderId = Guid.Empty;
                    provision.CreatedDate = DateTime.UtcNow;
                    provision.ModifiedDate = DateTime.UtcNow;
                    provision.IsActive = true;
                    provision.IsConverted = false;
                    provision.IsEnterpriseConverted = false;
                    provision.IsRedeemed = false;
                    provision.PhoneNumber = "";
                    provision.FirstName = user.FirstName ?? "";
                    provision.LastName = user.LastName ?? "";
                    provision.MiddleName = user.MiddleName ?? "";
                    provision.UserId = user.Id;
                    provision.IsFree = false;
                    provision.IsProvisioned = false;
                    provision.IsPayed = false;
                    provision.IsRequested = false;
                    provision.IsAccepted = false;
                    provision.IsPurchasedByUser = false;
                    provision.IsPurchasedOnAndroid = false;
                    provision.IsPurchasedOnIos = false;
                    provision.EmailAddress = "ContactAddedByAdmin";
                    provision.IsClaimed = true;
                    provision.IsTrial = false;
                    weodb.Provisions.Add(provision);
                    weodb.SaveChanges();

                    var newUserProvider = new Models.Models.PeopleModel.UserProvider()
                    {
                        Id = Guid.NewGuid(),
                        UserId = user.Id,
                        AppId = user.AppId,
                        OrgId = CommonData.WhatElseCustomerOrgId,
                        Status = (int)Models.Models.PropertyModels.DataModels.UserProviderStatus.NewProvider,
                        CreatedDate = DateTime.UtcNow,
                        ModifiedDate = DateTime.UtcNow,
                        ProviderId = CommonData.ContactAddedByAdmin,
                        IsActive = true,
                        ActiveFrom = DateTime.UtcNow,
                        ActiveTill = DateTime.UtcNow.AddYears(10),
                        ProvisionId = provision.Id,
                        EmailAddress = "ContactAddedByAdmin"
                    };
                    TelegramService.SendMessageToTestBot("UserProvider Created");
                    wepdb.UserProviders.Add(newUserProvider);
                    wepdb.SaveChanges();

                    Contact profile = new()
                    {
                        FirstName = user.FirstName,
                        LastName = user.LastName,
                        Display = $"{user.FirstName} {user.LastName}",
                        UserId = userGuid,
                        OtherUserId = userGuid,
                        IsActive = true,
                        ProviderId = CommonData.ContactAddedByAdmin,
                        UserProviderId = newUserProvider.Id,
                        Id = Guid.NewGuid()
                    };
                    weedb.Contacts.Add(profile);
                    weedb.SaveChanges();
                }
            }
            catch (Exception ex)
            {
                TelegramService.SendMessageToTestBot("StackTrace: " + ex.StackTrace.ToString() + " Exception: " + ex.Message.ToString());
            }
        }
        [HttpPost]
        [Route("05D95671")]
        public async Task<IActionResult> PostEventNote([FromBody] OnePage.Models.Models.ENTEventModel.EventModel model)
        {
            var userId = _authService.GetUser(Request);
            var token = _authService.GetToken(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized("Wrong Token");
            }
            try
            {
                if (model != null)
                {
                    var eventModel = weedb.EventModels.FirstOrDefault(w => w.Id == model.Id);
                    eventModel.Notes = model.Notes;
                    eventModel.ModifiedDate = DateTime.UtcNow;
                    weedb.SaveChanges();
                    // await cosmosService.UpsertItem(userId, model);
                }
                return Ok();
            }
            catch (Exception ex)
            {
                //telemetryTracker.TrackException(ex, userId);
                BadRequest("Error Occured" + ex.Message);
            }
            return BadRequest("Error Occured");
        }

        [HttpPost]
        [Route("8B55C3FC")]
        public async Task<IActionResult> GetTodayAgenda()
        {
            var userId = _authService.GetUser(Request);
            var token = _authService.GetToken(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized();
            }
            try
            {
                var userInfo = _redisCaching.CheckForItem("userDetailInfo_" + userId, () => wepdb.Users.FirstOrDefault(w => w.Id == userId), CommonData.CustomJsonSerializer.Serialize, JsonConvert.DeserializeObject<User>, DateTimeOffset.Now.AddHours(1));
                bool profileExists = await weedb.Contacts.AsNoTracking().AnyAsync(w => w.UserId == userId && w.OtherUserId == userId);
                if (!profileExists)
                {
                    await CreateProfileContact(userId);
                }
                var profile = weedb.Contacts.Where(w => w.UserId == userId && w.OtherUserId == userId).FirstOrDefault();
                List<string> profileEmails = weedb.ContactEmails.Where(x => x.ContactId == profile.Id && x.IsActive == true).Select(w => w.Email).ToList();
                await calendarService.ProcessEvents(userId, userInfo, profileEmails);
                DateTimeOffset today = new DateTime(DateTime.Today.Year, DateTime.Today.Month, DateTime.Today.Day);
                DateTimeOffset tomorrow = today.AddHours(24);
                DateTimeOffset now = DateTimeOffset.Now;
                DateTimeOffset yesterday = today.AddHours(-24);
                long yLong = yesterday.ToUnixTimeSeconds();
                DateTimeOffset minusOne = today.AddMinutes(-1);
                long mLong = minusOne.ToUnixTimeSeconds();
                DateTimeOffset startLimit = now.Subtract(TimeSpan.FromMinutes(-15));
                DateTimeOffset endLimit = DateTimeOffset.Now.Subtract(TimeSpan.FromMinutes(1));
                var events = weedb.EventModels.Where(w => w.LEnd > yLong && w.UserId == userId
                                                                      && w.LStart < tomorrow.ToUnixTimeSeconds() && w.IsActive == true && w.BTwo == true).ToList();
                List<WebAgendaModel> returnList = new List<WebAgendaModel>();

                List<EventModel> tempEvents = new List<EventModel>();
                DateTimeOffset endLimit2 = DateTimeOffset.Now.Subtract(TimeSpan.FromMinutes(1));
                bool todayEvents = events.Any(w => w.End > endLimit2 && w.Start < tomorrow && w.IsActive == true && w.BTwo == true);
                if (todayEvents)
                {
                    tempEvents = (events.Where(w => w.End > endLimit2 && w.Start < tomorrow && w.IsActive == true && w.BTwo == true)).OrderBy(w => w.Start).ToList();
                }
                if (tempEvents.Count > 0)
                {
                    foreach (var eventItem in tempEvents)
                    {
                        if (eventItem.EventAttendees.Count == 0)
                        {
                            var attendees = weedb.EventAttendees.Where(w => w.EventId == eventItem.EventId && w.UserId == userId && (w.IsUser == false || w.IsUser == null)).ToList();
                            //var attendees = await cosmosService.GetItems<EventAttendee>(userId, w => w.EventId == eventItem.EventId && w.IsUser == false);
                            eventItem.EventAttendees = attendees;
                        }

                        WebAgendaModel agendaModel = new();
                        agendaModel.Created = (DateTimeOffset)eventItem.CreatedDate;
                        agendaModel.Desc = eventItem.Desc;
                        agendaModel.End = (DateTimeOffset)eventItem.End;
                        agendaModel.EventId = eventItem.EventId;
                        agendaModel.iCalUId = eventItem.iCalUId;
                        agendaModel.id = eventItem.Id;
                        agendaModel.IsActive = (bool)eventItem.IsActive;
                        agendaModel.LEnd = (long)eventItem.LEnd;
                        agendaModel.Link = eventItem.Link;
                        agendaModel.Location = eventItem.Location;
                        agendaModel.LStart = (long)eventItem.LStart;
                        agendaModel.Modified = (DateTimeOffset)eventItem.ModifiedDate;
                        agendaModel.Notes = eventItem.Notes;
                        agendaModel.ProviderId = (Guid)eventItem.ProviderId;
                        agendaModel.Start = (DateTimeOffset)eventItem.Start;
                        agendaModel.Subject = eventItem.Subject;
                        agendaModel.URL = eventItem.URL;
                        agendaModel.UserId = (Guid)eventItem.UserId;

                        if (eventItem.EventAttendees.Count > 0)
                        {
                            foreach (var attItem in eventItem.EventAttendees)
                            {
                                AttendeesModel attModel = new();
                                attModel.Address = attItem.Address;
                                attModel.ContactId = (Guid)(attItem.ContactId);
                                attModel.Created = (DateTimeOffset)attItem.Created;
                                attModel.EventId = attItem.EventId;
                                attModel.id = attItem.Id;
                                attModel.IsActive = (bool)attItem.IsActive;
                                attModel.IsUser = (bool)attItem.IsUser;
                                attModel.Modified = (DateTimeOffset)attItem.Modified;
                                attModel.Name = attItem.Name;
                                Guid cId = (Guid)attItem.ContactId;
                                var contactPersonExists = db.ContactPeople.Any(w => w.ContactId == cId);
                                if (contactPersonExists)
                                {
                                    var cp = db.ContactPeople.First(w => w.ContactId == cId);
                                    if (cp.IsRequested == false && cp.IsRedeemed == false && cp.IsSearchComplete == false)
                                        attModel.Status = 0;
                                    else if (cp.IsRequested == true && cp.IsRedeemed == false && cp.IsSearchComplete == false)
                                        attModel.Status = 1;
                                    else if (cp.IsRequested == true && cp.IsRedeemed == false && cp.IsSearchComplete == true)
                                        attModel.Status = 2;
                                    else if (cp.IsRequested == true && cp.IsRedeemed == true && cp.IsSearchComplete == true)
                                        attModel.Status = 3;
                                }
                                else
                                {
                                    attModel.Status = 0;
                                }
                                attModel.UserId = (Guid)attItem.UserId;
                                agendaModel.AttendeesList.Add(attModel);
                            }
                        }
                        returnList.Add(agendaModel);
                    }
                }
                return Ok(returnList);
            }
            catch (Exception ex)
            {

                BadRequest("Error Occured" + ex.Message);
            }
            return BadRequest("Error Occured");
        }

        [HttpPost]
        [Route("9A562278")]
        public async Task<IActionResult> Post_Event_Note(OnePage.Models.Models.ENTEventModel.EventModel model)
        {
            var userId = _authService.GetUser(Request);
            var Token = _authService.GetToken(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized();
            }
            try
            {
                if (model != null)
                {
                    var eventModel = weedb.EventModels.FirstOrDefault(w => w.Id == model.Id);
                    eventModel.Notes = model.Notes;
                    eventModel.ModifiedDate = DateTime.UtcNow;
                    weedb.SaveChanges();
                    // await cosmosService.UpsertItem(userId, model);
                }
                return Ok();
            }
            catch (Exception ex)
            {

                return BadRequest("Error Occured" + ex.Message);
            }

        }
        [HttpPost]
        [Route("53C3C85A")]

        public async Task<IActionResult> PostSocialDetail([FromBody] WebAppSocialModel model)
        {
            var userId = _authService.GetUser(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized("Wrong Token");
            }
            try
            {

                if (model != null)
                {
                    Guid cid = Guid.Parse(model.ContactId);
                    if (!string.IsNullOrEmpty(model.LinkedIn))
                    {
                        var checkForExisting = weedb.ContactSocials.Any(w => w.ContactId == cid && w.Url == model.LinkedIn);
                        if (!checkForExisting)
                        {
                            OnePage.Models.Models.ENTEventModel.ContactSocial cs1 = new OnePage.Models.Models.ENTEventModel.ContactSocial();
                            cs1.ContactId = Guid.Parse(model.ContactId);
                            cs1.Created = DateTime.UtcNow;
                            cs1.Id = Guid.NewGuid();
                            cs1.IsActive = true;
                            cs1.Modified = DateTime.UtcNow;
                            cs1.Name = "fab fa-linkedin-in";
                            cs1.SocialId = Guid.Parse("0ea37808-1506-43a3-9abd-6ff561931ff5");
                            cs1.Url = model.LinkedIn;
                            cs1.UserId = Guid.Parse(model.UserId);
                            cs1.ShowOrder = 0;
                            weedb.ContactSocials.Add(cs1);
                            weedb.SaveChanges();
                        }
                    }
                    if (!string.IsNullOrEmpty(model.Website))
                    {
                        var checkForExisting = weedb.ContactSocials.Any(w => w.ContactId == cid && w.Url == model.Website);
                        if (!checkForExisting)
                        {
                            OnePage.Models.Models.ENTEventModel.ContactSocial cs2 = new OnePage.Models.Models.ENTEventModel.ContactSocial();
                            cs2.Name = "fal-globe";
                            cs2.ContactId = Guid.Parse(model.ContactId);
                            cs2.Created = DateTime.UtcNow;
                            cs2.Id = Guid.NewGuid();
                            cs2.IsActive = true;
                            cs2.Modified = DateTime.UtcNow;
                            cs2.SocialId = Guid.Parse("ac45263f-dfd8-4482-9987-478537598d26");
                            cs2.Url = model.Website;
                            cs2.UserId = Guid.Parse(model.UserId);
                            cs2.ShowOrder = 0;
                            weedb.ContactSocials.Add(cs2);
                            weedb.SaveChanges();
                        }
                    }
                    if (!string.IsNullOrEmpty(model.Twitter))
                    {
                        var checkForExisting = weedb.ContactSocials.Any(w => w.ContactId == cid && w.Url == model.Twitter);
                        if (!checkForExisting)
                        {
                            OnePage.Models.Models.ENTEventModel.ContactSocial cs3 = new OnePage.Models.Models.ENTEventModel.ContactSocial();
                            cs3.Name = "fa-brands fa-x-twitter";
                            cs3.ContactId = Guid.Parse(model.ContactId);
                            cs3.Created = DateTime.UtcNow;
                            cs3.Id = Guid.NewGuid();
                            cs3.IsActive = true;
                            cs3.Modified = DateTime.UtcNow;
                            cs3.SocialId = Guid.Parse("4fb5f8a7-4639-489f-9de1-5888ee69d4bc");
                            cs3.Url = model.Twitter;
                            cs3.UserId = Guid.Parse(model.UserId);
                            cs3.ShowOrder = 0;
                            weedb.ContactSocials.Add(cs3);
                            weedb.SaveChanges();
                        }
                    }
                    if (!string.IsNullOrEmpty(model.Facebook))
                    {
                        var checkForExisting = weedb.ContactSocials.Any(w => w.ContactId == cid && w.Url == model.Facebook);
                        if (!checkForExisting)
                        {
                            OnePage.Models.Models.ENTEventModel.ContactSocial cs4 = new OnePage.Models.Models.ENTEventModel.ContactSocial();
                            cs4.Name = "fab-facebook-square";
                            cs4.ContactId = Guid.Parse(model.ContactId);
                            cs4.Created = DateTime.UtcNow;
                            cs4.Id = Guid.NewGuid();
                            cs4.IsActive = true;
                            cs4.Modified = DateTime.UtcNow;
                            cs4.SocialId = Guid.Parse("0f9b27c2-d649-4fa2-a698-90f7a08482d2");
                            cs4.Url = model.Facebook;
                            cs4.UserId = Guid.Parse(model.UserId);
                            cs4.ShowOrder = 0;
                            weedb.ContactSocials.Add(cs4);
                            weedb.SaveChanges();
                        }
                    }


                    Guid cId = Guid.Parse(model.ContactId);
                    var cpExists = db.ContactPeople.Any(w => w.ContactId == cId);
                    if (cpExists)
                    {
                        var cp = db.ContactPeople.First(w => w.ContactId == cId);
                        cp.IsRequested = false;
                        cp.IsRedeemed = false;
                        cp.IsSearchComplete = false;
                        db.SaveChanges();
                    }


                }


                return Ok();
            }
            catch (Exception ex)
            {

                return BadRequest(ex);
            }

        }

        [HttpPost]
        [Route("40500DEC")]

        public async Task<IActionResult> GetPeopleList(bool fetchOnline = false)
        {
            var userId = _authService.GetUser(Request);
            var token = _authService.GetToken(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized("Wrong Token");
            }
            try
            {
                if (fetchOnline)
                {

                    var userInfo = _redisCaching.CheckForItem("userDetailInfo_" + userId, () => wepdb.Users.FirstOrDefault(w => w.Id == userId), CommonData.CustomJsonSerializer.Serialize, JsonConvert.DeserializeObject<User>, DateTimeOffset.Now.AddHours(1));
                    bool profileExists = weedb.Contacts.Any(w => w.UserId == userId && w.OtherUserId == userId);
                    if (!profileExists)
                    {
                        await CreateProfileContact(userId);
                    }
                    var profile = weedb.Contacts.Where(w => w.UserId == userId && w.OtherUserId == userId).FirstOrDefault();
                    List<string> profileEmails = weedb.ContactEmails.Where(x => x.ContactId == profile.Id && x.IsActive == true).Select(w => w.Email).ToList();
                    await calendarService.ProcessEvents(userId, userInfo, profileEmails);
                }
                DateTimeOffset today = new DateTime(DateTime.Today.Year, DateTime.Today.Month, DateTime.Today.Day);
                DateTimeOffset tomorrow = today.AddHours(24);
                DateTimeOffset now = DateTimeOffset.Now;
                DateTimeOffset yesterday = today.AddHours(-24);
                long yLong = yesterday.ToUnixTimeSeconds();
                DateTimeOffset minusOne = today.AddMinutes(-1);
                long mLong = minusOne.ToUnixTimeSeconds();
                DateTimeOffset startLimit = now.Subtract(TimeSpan.FromMinutes(-15));
                DateTimeOffset endLimit = DateTimeOffset.Now.Subtract(TimeSpan.FromMinutes(1));
                var events = weedb.EventModels.Where(w => w.LEnd > yLong && w.UserId == userId
                                                                      && w.LStart < tomorrow.ToUnixTimeSeconds() && w.IsActive == true && w.BTwo == true).ToList();

                List<AttendeesModel> returnList = new List<AttendeesModel>();

                List<EventModel> tempEvents = new List<EventModel>();
                DateTimeOffset endLimit2 = DateTimeOffset.Now.Subtract(TimeSpan.FromMinutes(1));
                bool todayEvents = events.Any(w => w.End > endLimit2 && w.Start < tomorrow && w.IsActive == true && w.BTwo == true);
                if (todayEvents)
                {
                    tempEvents = (events.Where(w => w.End > endLimit2 && w.Start < tomorrow && w.IsActive == true && w.BTwo == true)).OrderBy(w => w.Start).ToList();
                }
                if (tempEvents.Count > 0)
                {
                    foreach (var eventItem in tempEvents)
                    {
                        if (eventItem.EventAttendees.Count == 0)
                        {
                            var attendees = weedb.EventAttendees.Where(w => w.EventId == eventItem.EventId && w.UserId == userId && (w.IsUser == false || w.IsUser == null)).ToList();
                            //var attendees = await cosmosService.GetItems<EventAttendee>(userId, w => w.EventId == eventItem.EventId && w.IsUser == false);
                            eventItem.EventAttendees = attendees;
                        }

                        if (eventItem.EventAttendees.Count > 0)
                        {
                            foreach (var attItem in eventItem.EventAttendees)

                            {
                                var contactData = weedb.Contacts.Where(w => w.Id == attItem.ContactId).FirstOrDefault();
                                //var contactData = cosmosService.GetItem<Contact>(userId, w => w.ContactId == attItem.ContactId.ToString());
                                AttendeesModel attModel = new();

                                attModel.Address = attItem.Address;
                                attModel.ContactId = (Guid)attItem.ContactId;
                                attModel.Created = (DateTimeOffset)attItem.Created;
                                attModel.EventId = attItem.EventId;
                                attModel.id = attItem.Id;
                                attModel.IsActive = (bool)attItem.IsActive;
                                attModel.IsUser = (bool)attItem.IsUser;
                                attModel.Modified = (DateTimeOffset)attItem.Modified;
                                attModel.Name = contactData.FirstName + " " + contactData.LastName;
                                Guid cId = (Guid)attItem.ContactId;
                                var contactPersonExists = db.ContactPeople.Any(w => w.ContactId == cId);
                                if (contactPersonExists)
                                {
                                    var cp = db.ContactPeople.First(w => w.ContactId == cId);
                                    attModel.PersonId = cp.PersonId.ToString();
                                    if (cp.IsRequested == false && cp.IsRedeemed == false && cp.IsSearchComplete == false)
                                        attModel.Status = 0;
                                    else if (cp.IsRequested == true && cp.IsRedeemed == false && cp.IsSearchComplete == false)
                                        attModel.Status = 1;
                                    else if (cp.IsRequested == true && cp.IsRedeemed == false && cp.IsSearchComplete == true)
                                        attModel.Status = 2;
                                    else if (cp.IsRequested == true && cp.IsRedeemed == true && cp.IsSearchComplete == true)
                                        attModel.Status = 3;
                                    else if (cp.IsRequested == true && cp.IsRedeemed == true && cp.IsSearchComplete == true && (string.IsNullOrEmpty(attModel.PersonId) || cp.PersonId == Guid.Empty))
                                        attModel.Status = 4;
                                }
                                else
                                {
                                    attModel.Status = 0;
                                }
                                attModel.UserId = (Guid)attItem.UserId;
                                returnList.Add(attModel);
                            }
                        }
                    }
                }
                return Ok(returnList.Distinct().ToList());
            }
            catch (Exception ex)
            {

                return BadRequest(ex);
            }


        }



        [HttpPost]
        [Route("C0C74FA0")]
        public async Task<IActionResult> GetMoreEventData(int mode)
        {
            var userId = _authService.GetUser(Request);
            var token = _authService.GetToken(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized("Wrong Token");
            }
            try
            {

                var timezoneSetting = wepdb.UserSettings.AsNoTracking()
                      .FirstAsync(x => x.SettingId == CommonData.TimeZoneSettingId && x.UserId == userId).Result.Value;
                int hourToAdd = 0;
                int minutesToAdd = 0;

                if (!string.IsNullOrEmpty(timezoneSetting))
                {
                    var split = timezoneSetting.Split(":");
                    hourToAdd = int.Parse(split[0]);
                    minutesToAdd = int.Parse(split[1]);
                }
                TimeSpan timespan = new TimeSpan(hourToAdd, minutesToAdd, 0);
                DateTimeOffset utcNow = DateTime.UtcNow.Date;
                DateTimeOffset today = utcNow.ToOffset(timespan);

                DateTimeOffset tomorrow = today.AddHours(24);
                DateTimeOffset yesterday = today.AddHours(-24);
                long yLong = yesterday.ToUnixTimeSeconds();
                DateTimeOffset minusOne = today.AddMinutes(-1);
                long mLong = minusOne.ToUnixTimeSeconds();
                long tomorrowSecs = tomorrow.AddHours(24).ToUnixTimeSeconds();
                var mLong2 = DateTimeOffset.UtcNow.ToOffset(timespan).AddMinutes(-1).ToUnixTimeSeconds();

                var yEvents = await weedb.EventModels.AsNoTracking().Where(w => w.UserId == userId && w.IsActive == true && w.BTwo == true && w.LStart > yLong && w.LEnd <= mLong2)
                    .ToListAsync();
                //var events = await cosmosService.GetItems<EventModel>(userId, w => w.UserId == userId && w.IsActive && w.BTwo == true);

                switch (mode)
                {
                    case 1:
                        break;
                    case 2: // reminders
                        List<OnePage.Models.Models.ENTEventModel.EventModel> reminderList =
                           new List<OnePage.Models.Models.ENTEventModel.EventModel>(yEvents.ToList());
                        return Ok(reminderList.Where(w => w.Notes == "" || w.Notes == null).OrderBy(w => w.Start)
                            .ToList());
                    default:
                        break;
                }
                return Ok();
            }
            catch (Exception ex)
            {
                BadRequest("Error Occured" + ex.Message);
            }
            return BadRequest("Error Occured");
        }






        [HttpPost]
        [Route("7CCC4BEC")]
        public async Task<IActionResult> SendTelegramMessage(string message)
        {
            TelegramService.SendMessageToTestBot(message);
            return Ok();
        }

        [HttpPost]
        [Route("AEF9653D")]
        public async Task<IActionResult> PostIgnoreReminder(Guid eventId)
        {
            var userId = _authService.GetUser(Request);
            var token = _authService.GetToken(Request);
            if (userId == Guid.Empty)
            {
                return Unauthorized("Wrong Token");
            }
            try
            {
                var eventDetailExists = weedb.EventModels.Any(w => w.Id == eventId);
                if (eventDetailExists)
                {
                    var eventDetail = weedb.EventModels.FirstOrDefault(w => w.Id == eventId);
                    eventDetail.Notes = " ";
                    eventDetail.ModifiedDate = DateTime.UtcNow;
                    weedb.SaveChanges();
                }
                return Ok();
            }
            catch (Exception ex)
            {
                BadRequest("Error Occured" + ex.Message);
            }
            return BadRequest("Error Occured");
        }


    }
}
