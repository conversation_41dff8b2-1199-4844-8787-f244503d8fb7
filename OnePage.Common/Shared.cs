using Google.Apis.Gmail.v1.Data;
using Microsoft.Graph;
using Newtonsoft.Json;
using OnePage.Models.Cosmos;
using OnePage.Models.Models;
using OnePage.Models.Models.DataModel;
using Org.BouncyCastle.Utilities.Encoders;
using PhoneNumbers;
using RestSharp;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Net;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using static OnePage.Models.Models.PropertyModels.DataModels;

namespace OnePage.Common
{
    public static class CommonData
    {
        //Static DeviceTypeId used in project 
        public static Guid IOSDeviceTypeId = Guid.Parse("85BF95B2-2315-4C59-9EF9-47F33CFA65AC");
        public static Guid AndroidDeviceTypeId = Guid.Parse("1E49B16C-6CFE-427C-9A71-C91A0FD1FC69");
        public static Guid WebAppDeviceTypeId = Guid.Parse("8DFE90E8-6F2C-4A60-BE04-830261556F6B");
        public static Guid TeamsDeviceTypeId = Guid.Parse("467AAE32-8361-43A3-A8C7-BE21E32C4F3B");
        //WE APP Id
        public static Guid WEAppId = Guid.Parse("b4f30f71-8e97-498a-aaa1-d8837ef55479");
        public static Guid OPAppId = Guid.Parse("bee2b05c-0b6e-4d8c-b9ae-960a78a3afc7");
        public static Guid OPV2AppId = Guid.Parse("6de53356-eb21-4830-a7de-7e2256395525");
        public static Guid OPMaukaAppId = Guid.Parse("39A84176-A61D-480E-9445-89FFB079B39B");
        public static Guid OPEnterpriseAppId = Guid.Parse("65E639CE-3E89-461D-A346-618323FC28D3");
        public static Guid MiofertaAppId = Guid.Parse("2d80f243-7b0b-4a1a-a24b-41e293dcd953");
        public static Guid AmperAppId = Guid.Parse("C1EF9960-2E93-47F7-8B40-BD323C17BF68");
        public static Guid WEAWSTestApp = Guid.Parse("da040d7a-a47d-481d-81cb-2f67a3aecec3");
        public static Guid NotificarAWSTestApp = Guid.Parse("1d5815d3-340e-45de-8bee-15b71b447753");
        public static Guid AmperAWSTestApp = Guid.Parse("a29cd3cb-3515-4c98-814d-75e3ea84e159");
        public static Guid ManualEntrySourceId = Guid.Parse("3DC4FE6C-7892-4EB1-A407-1DB0983F9D09");
        public static Guid GlobalAppId = Guid.Parse("92C4D496-63E2-4660-B38A-B1EBF1101A65");

        public static string adAuthToken = "b5f9688d-9e42-4886-83c7-62f57b38eba6";
        public static string aiauthToken = "79889518-b26e-4774-a473-d027521ea2e9";
        //FireFilesApiKey
        public static Guid FireFilesApiKeyIdentifier = Guid.Parse("CACCAC0E-AD07-4598-B879-36C6037F47A3");

        //DreamCast
        public static string GetOppName = "https://moukha.backend.get1page.net/api/v3/opportunities/";
        public static string NewOppMetric = "https://ap-south-1.aws.data.mongodb-api.com/app/data-mjnxo/endpoint/data/v1/action/insertOne";
        public static string NOMetricAppiKey = "61nqacn6skr0ncYZMGlaRBEjkYKMU9KpYfD7eEPgnNm4qq666uX5T1x14xxlcNug";

        //BulkSignalInterval
        public static Guid BulkSignalInteral = Guid.Parse("E1A0724C-B802-44A9-8E32-3073B7268103");
        //Static OrgIds
        public static Guid WhatElseCustomerOrgId = Guid.Parse("97859466-D85E-47E7-ABB5-56CF76335318");
        public static Guid WhatElseOrgId = Guid.Parse("98ee7a4b-de3c-49e3-aaf3-dc6b7e2cc905");
        public static Guid MrAwesomeAppsOrgId = Guid.Parse("66A6F8E3-999F-49D6-BCB7-2B03BC0F6E87");
        //Static CustomerId
        public static string WhatElseOrgCustomerId = "Hr5514ZQk5Q5C1NjO";
        //Static RoleIds
        //public static Guid AdminRoleId = Guid.Parse("a0702506-fbe2-4fd5-b0d5-c0f7238b6ee4");
        //public static Guid EnterpriseCustomerRoleId = Guid.Parse("a68de40f-c7e4-4a05-ab2e-4cafd31dbda7");

        public static Guid UserInviteOfferId = Guid.Parse("4CF56518-DC48-4F3A-B8C7-F6A2B0390572");
        public static Guid InviteForSameDomainOfferId = Guid.Parse("809C39DD-6ECB-45F6-AA42-4EEAD8939E34");
        public static Guid InviteForOtherDomainOfferId = Guid.Parse("6096A205-4E25-4760-9A1A-52DE1BBB26B3");
        public static Guid InviteForOtherNewDomainOfferId = Guid.Parse("31864922-BC10-4FFC-B01A-C2B77F160BA1");
        public static Guid InvitedByOfferId = Guid.Parse("F7E0A25E-D134-4F40-8B95-1F42DD93BDEF");

        //Static ProviderTypeId
        public static Guid CalendarProviderType = Guid.Parse("b75f4ec7-0b9a-4fae-8a71-acda3e1194f1");
        public static Guid EmailProviderType = Guid.Parse("7FED8DC9-937C-42AA-98FF-670F3399F3C9");
        public static Guid StorageProviderType = Guid.Parse("BB443B73-996B-4516-AF71-00598BA7671D");
        public static Guid SupportProviderType = Guid.Parse("6AFD623A-576F-42C3-A8AE-59EE5931E5CA");
        public static Guid ContactsProviderType = Guid.Parse("5938D26F-6791-4328-A152-FAA85F0AB346");
        public static Guid SuiteProviderType = Guid.Parse("C51E3D89-39A6-4610-812F-68A0F6D4026C");
        public static Guid CRMProviderType = Guid.Parse("A044C04D-D433-4C56-9FB7-CE3C4F66B7AC");
        public static Guid PersonInsightsProviderType = Guid.Parse("D3343061-FAF1-41F1-8313-70CB6BA68722");
        public static Guid CompanyIntelligenceProviderType = Guid.Parse("F1356586-0E09-4B9E-A8AE-672EC5CE6E9A");
        public static Guid BeanBagProviderType = Guid.Parse("4B2CFAAE-C1F0-4CE3-BC08-A9158FD4ABDB");
        public static Guid GoogleSuiteProviderId = Guid.Parse("DEAA4C8D-B0BB-4D81-BB86-055944E207D7");
        public static Guid MicrosoftSuiteProviderId = Guid.Parse("A2DBF10E-08C5-4AA5-B198-8BBD2A860A66");

        public static Guid DynamicsCRMProviderId = Guid.Parse("10A5E65C-0967-4CF7-B959-CE4CB06C4DD7");
        public static Guid FirefliesProviderId = Guid.Parse("03029F41-55F5-40D8-B697-61ABD493350B");
        public static Guid socialNetworkProviderTypeId = Guid.Parse("D9AE7BF6-41F0-4CC0-95CC-32F4FF72F332");
        public static Guid CRMProviderTyepId = Guid.Parse("A044C04D-D433-4C56-9FB7-CE3C4F66B7AC");
        public static Guid DeviceContactsProviderTyepId = Guid.Parse("6c59ec93-0573-4809-929c-35e11cdd6045");
        public static string WebsiteSocialId = "ac45263f-dfd8-4482-9987-*********d26";
        public static Guid PersonalEmailPTId = Guid.Parse("1DEBFF2D-57A7-4D65-9CB6-0C5B1B42087A");
        public static Guid PersonalStoragePTId = Guid.Parse("0E64E6C6-9943-4EC6-871B-AD4013174F6A");
        public static Guid Government = Guid.Parse("99DBFF0D-8175-4420-A6D9-669429710EDF");
        public static Guid AmperApps = Guid.Parse("85484934-58A2-4495-ACD1-4F0DD6CD7558");
        public static Guid EnterpriseStorageProivderTyId = Guid.Parse("BB443B73-996B-4516-AF71-00598BA7671D");
        public static Guid EnterpriseEmailProivderTyId = Guid.Parse("7FED8DC9-937C-42AA-98FF-670F3399F3C9");
        public static Guid VirtualNumbers = Guid.Parse("5938D26F-6791-4328-A152-FC285F0AB346");
        public static Guid CompanyInfoProviderTypeId = Guid.Parse("F1356586-0E09-4B9E-A8AE-672EC5CE6E9A");
        public static Guid SocialNetworkProviderTypeId = Guid.Parse("D9AE7BF6-41F0-4CC0-95CC-32F4FF72F332");

        public const string KickboxApi = "https://api.kickbox.com/v2/verify?email={emailaddress}&apikey={kickboxkey}";
        public const string KickboxApiKey = "*********************************************************************";
        public const string apolloKey = "oTV6yD84l02o1wu7plq-pw";
        public static readonly List<string> StorageProviderTypes = new List<string> { "bb443b73-996b-4516-af71-00598ba7671d", "0e64e6c6-9943-4ec6-871b-ad4013174f6a" };
        public static readonly List<string> EmailProviderTypes = new List<string> { "1debff2d-57a7-4d65-9cb6-0c5b1b42087a", "7fed8dc9-937c-42aa-98ff-670f3399f3c9" };
        public static readonly List<string> CRMProviderTypes = new List<string> { "a044c04d-d433-4c56-9fb7-ce3c4f66b7ac" };
        public static readonly List<string> SupportProviderTypes = new List<string> { "6afd623a-576f-42c3-a8ae-59ee5931e5ca" };
        public static readonly List<Guid> CalendarProviderList = new List<Guid>
        {
            Guid.Parse("327915F0-677A-444C-99A8-1B4998A4623C"),
            Guid.Parse("35488519-DB6B-45C6-9A4C-381B1189CF71"),
            Guid.Parse("0E2CECB5-0CBB-405D-8787-81566138E1D9"),
            Guid.Parse("A45B6B98-9D24-4C33-B464-AC585F768752"),
            Guid.Parse("10468AD5-5FD5-4FC4-BED0-E8F48A114DF9"),
            Guid.Parse("A2DBF10E-08C5-4AA5-B198-8BBD2A860A66"), // microsoft suite
            Guid.Parse("DEAA4C8D-B0BB-4D81-BB86-055944E207D7") // google suite
        };
        public static string DynamicCRmAppId = "8889647c-5fd8-ee11-904b-6045bda5854c";
        //Static ProviderId
        public static Guid IMAPProviderId = Guid.Parse("64254B84-0E2E-498A-BA7B-32FD21AB3A64");
        public static Guid BoxProviderId = Guid.Parse("EBB3714B-3F7C-4022-B304-936607A82437");
        public static Guid DropBoxProviderId = Guid.Parse("BB779183-E903-4F55-A099-3EF4373A6C25");
        public static Guid CalendlyProviderId = Guid.Parse("0E2CECB5-0CBB-405D-8787-81566138E1D9");
        public static Guid EvernoteProviderId = Guid.Parse("EE51A555-0930-4795-87CF-88E0541E2CE5");
        public static Guid IDPProviderId = Guid.Parse("3be06471-29d4-4aed-9bb8-66eda6ebd1bd");
        public static Guid office365ProviderId = Guid.Parse("1debff2d-57a7-4d65-9cb6-0c5b1b42087a");
        public static Guid MagicLinkProviderId = Guid.Parse("6F7398B7-A552-4768-AD95-F47B9E1E6FAF");
        public static Guid ApolloProviderId = Guid.Parse("D4B6BE82-0C8B-4735-8386-1C86BAEA85B2");
        //public static Guid outlookProviderId = Guid.Parse("*************-4649-a65a-2616db545cd8");
        public static Guid ZohoCalendarProviderId = Guid.Parse("10468AD5-5FD5-4FC4-BED0-E8F48A114DF9");
        public static Guid CalendarHeroProviderId = Guid.Parse("35488519-DB6B-45C6-9A4C-381B1189CF71");
        public static Guid MicrosoftProviderId = Guid.Parse("327915f0-677a-444c-99a8-1b4998a4623c");
        public static Guid MicrosoftContactProvideId = Guid.Parse("ECD27441-F9F9-4EDA-9432-CD4E86C9D027");
        public static Guid ManualProviderId = Guid.Parse("EE26B1CE-E1E8-477C-847D-79CBB663F310");
        public static Guid HubspotProviderId = Guid.Parse("************************************");
        public static Guid GDriveProviderId = Guid.Parse("1DDE92F9-1561-47DD-8993-59F55C5797AD");
        public static Guid GDriveForBusinessProviderId = Guid.Parse("03300FF7-4768-4BBC-8816-3577DC7B9F7A");
        public static Guid ZendeskProviderId = Guid.Parse("0D535674-70A3-4603-9D89-38223F8FE356");
        public static Guid MiOfertaProviderId = Guid.Parse("2209bb99-4dab-43f4-8c8d-44c7d83fb8fa");
        public static Guid DynamicCRMProviderId = Guid.Parse("10A5E65C-0967-4CF7-B959-CE4CB06C4DD7");
        public static Guid PipedriveProviderId = Guid.Parse("03A067AD-4BD3-4CF9-9D2E-73238A2F5A86");
        public static Guid PeopleDataProviderId = Guid.Parse("B8951A20-8CF4-47EE-9F18-1DDC4E7705AC");
        public static Guid TymlineDataProviderId = Guid.Parse("3E33AE65-8991-4F9B-95F1-7E438F559FCD");
        public static Guid PeopleDataLabSourceId = Guid.Parse("904D36E5-C08D-4DBE-A6CB-56200B92124D");
        public static Guid TymlineSourceId = Guid.Parse("138FC06D-9E73-4632-8FFE-463191425EE8");
        public static Guid TwitterProviderId = Guid.Parse("B76E4F1E-8FB0-46AA-8DD8-866EDDD84201");
        public static Guid GmailPersonalProviderId = Guid.Parse("b75ce275-**************-f6476cfbc67a");
        public static Guid GmailBusinessProviderId = Guid.Parse("b91cbaee-d9d0-490e-8458-1b5a8e4e2aac");
        public static Guid GoogleCalendarProviderId = Guid.Parse("a45b6b98-9d24-4c33-b464-ac585f768752");
        public static Guid GoogleContactProvideId = Guid.Parse("35b45aa5-10f5-428c-a264-9dd5854bac26");
        public static Guid ZohoCRMProviderId = Guid.Parse("F9695DA4-AE0E-41A3-A0E4-FBEC795D2DE5");
        public static Guid SalesforceProviderId = Guid.Parse("CF2F8BCC-F9A7-4AEF-900C-32A615DE4AF1");
        public static Guid FreshdeskProviderId = Guid.Parse("2E2B4E02-BA58-4E9E-A70E-18942F099A06");
        public static Guid FreshsalesProviderId = Guid.Parse("fd4e5e65-3f71-4c48-a6f2-4b27f584a795");
        public static Guid ZohoRecruitProviderId = Guid.Parse("8fafc8c0-a202-4613-a809-102f29332583");
        public static Guid AmperProviderId = Guid.Parse("ec7db679-2c41-4d59-8dae-0ce4f3c026f1");
        public static Guid AgileCRMProviderId = Guid.Parse("1ADEBDC2-54CD-4234-BB96-FA0D4A631B93");
        public static Guid IntercomProviderId = Guid.Parse("3B6B56D9-E19A-4A5A-8E13-EC20C65BB502");
        public static Guid ExotelProviderId = Guid.Parse("a1511b36-06ef-4cb1-9825-86f781c26235");
        public static Guid OzoneTelIndiaProviderId = Guid.Parse("257EA371-ECD0-400B-A27C-202166C316A5");
        public static Guid OzoneTelUSProviderId = Guid.Parse("B8DF0E95-D0FC-4A00-AAAC-C296BBFF9265");
        public static Guid SymblProviderId = Guid.Parse("0f80746b-7d33-4348-97bf-8ecbc0b42337");
        public static Guid TwilioProviderId = Guid.Parse("934fa923-1511-4fb6-9927-7c9247301b5d");
        public static Guid HarmonicProviderId = Guid.Parse("DA01F3FD-4509-463E-ACBD-0E2E3C17F8EA");
        public static Guid ZohoMailProviderId = Guid.Parse("21900222-fe90-435f-ac93-9624ee38605b");
        public static Guid OutlookMailProviderId = Guid.Parse("*************-4649-a65a-2616db545cd8");
        public static Guid BeanBagProviderId = Guid.Parse("0F808104-9605-48DE-BF8A-603FF3F6836C");

        // Offers
        public static Guid FreemiumOfferId = Guid.Parse("1D1274F1-F6FB-4F1D-8F69-00297DB8AB63");
        public static Guid ProfessionalOfferId = Guid.Parse("1149E7BF-8B3A-4440-82C4-18CB0F06D421");
        public static Guid SalespersonOfferId = Guid.Parse("28577922-06EE-45AE-AF80-4A655B331EF0");
        public static Guid TeamOfferId = Guid.Parse("E6040DBB-CF68-42B8-8E26-C617497C9E84");
        public static string WorkOsOTPApi = "https://api.workos.com/user_management/magic_auth";
        public static string WorkOsClientSecret = "sk_a2V5XzAxSjdCNTdCRk1aWEtHQjlYR1ZIRDFDSzY1LEdlSzAxdzR6eXpIbkY5ZDc3Rmo1VVJGZkI";

        public static Guid FreemiumPlanId = Guid.Parse("06E1D67E-941A-438B-B402-98B1BA368B3C");
        public static Guid SalespersonPland = Guid.Parse("5911109C-BD51-4FC4-92AB-3FF06BFB86C6");
        //const Setting Id for DemoCards
        
        public static Guid UserIsTrialCompleteSettingId = Guid.Parse("CA508D5D-700E-4315-A0CD-F19BD46BBF10");
        public static Guid UserTrialStartDateSettingId = Guid.Parse("CA508D5D-700E-4315-A0CD-F19BD46BBF13");
        public static Guid UserTrialDaysSettingId = Guid.Parse("CA508D5D-700E-4315-A0CD-F19BD46BBF16");
        public static Guid ShouldPreFetchSettingId = Guid.Parse("E36FAFCD-28B3-45E1-A106-D782A9BDD561");
        public static Guid ShouldPrefetchSignalTypeId = Guid.Parse("687946D4-6B27-47E9-A7D7-9E7023044601");
        public static Guid ShouldPrefetchForEntSignalTypeId = Guid.Parse("687946D4-6B27-47E9-A7D7-9E7023044602");
        public static Guid OwlerSetting = Guid.Parse("43298B14-5027-4A92-8478-703FC66833B4");
        public static Guid InsideviewSetting = Guid.Parse("2FC2F564-7491-4C51-9B3E-5049535D8948");
        public static Guid crunchbaseSetting = Guid.Parse("BF7D0B64-2262-4910-B3D0-C952EC708645");
        public static Guid snpSetting = Guid.Parse("DBAC9056-A246-45DF-A382-48C823C6CB7D");
        public static Guid harmonicSetting = Guid.Parse("C79D1C6E-CE4F-4A0D-81BC-5792B2F1906D");
        public static Guid pitchbookSetting = Guid.Parse("D739EAAF-41F4-48E3-8C07-0C88FC9E500C");
        public static Guid ISEmailValidated = Guid.Parse("E5648034-B3D4-4EB8-8350-92623E11A5B4");

        //Moukha Credits Part
        public static Guid AdminNeworgCredits = Guid.Parse("9AC8E61E-1B32-450F-A458-E546C11B69AA");
        public static Guid AdminNewpersoninorgCredits = Guid.Parse("999711C8-96F4-40FC-9500-54693095C620");
        public static Guid AdminDesignPartnerCredits = Guid.Parse("39C38D17-7197-4490-8E79-8C77A1478899");

        // Pipedrive
        public static Guid PipedriveClientIId = Guid.Parse("8D61A423-EC3D-4722-AC16-E355F20296D6");
        public static Guid PipedriveClientSecretIId = Guid.Parse("6FE0EC5C-C773-4797-8D6E-948A8D7738EB");
        public static Guid PipedriveRedirectUrlIId = Guid.Parse("A47EDC98-ABAE-4E72-9EE0-1C80E5DB8A50");
        public static Guid PipedriveBase64IId = Guid.Parse("7315C7B4-30D6-4DE5-A493-C1B5349C874B");
        public static string PipedriveRedirectUrl = "https://connect.get1page.com/Authenticated/03a067ad4bd34cf99d2e73238a2f5a86";

        //SalesforceIIds
        public static Guid SalesforceClientSecretId = Guid.Parse("2CABDB5A-5036-4E8E-BE43-6DF1F0CD75B9");
        public static Guid SalesforceScopeId = Guid.Parse("9EAE6D4C-0D2F-4F1E-B47A-60920A6EBDDF");
        public static Guid SalesforceRedirectURI = Guid.Parse("DE4F9C3D-22D6-43BF-92F8-9C6DE2B11828");
        public static Guid SalesforceClientId = Guid.Parse("FD7E01C0-7A0C-41D6-B8F3-DFC4D4A501D3");

        //HubspotIds
        public static Guid HubspotClientSecretId = Guid.Parse("************************************");
        public static Guid HubspotScopeId = Guid.Parse("************************************");
        public static Guid HubspotRedirectURI = Guid.Parse("************************************");
        public static Guid HubspotClientId = Guid.Parse("************************************");

        //Stripe Api
        public static string StripeAPI = "https://api.stripe.com/v1/checkout/sessions/{sessionId}";

        //DynamicCRmIDs
        public static Guid DynamicProviderId = Guid.Parse("10A5E65C-0967-4CF7-B959-CE4CB06C4DD7");
        public static Guid DytenantId = Guid.Parse("9248B6A6-CC1E-4FB4-91BE-2FF106D1B546");
        public static Guid DyClientId = Guid.Parse("DDA6FC69-A83E-43CD-B7E6-97620E8610BB");
        public static Guid DyClientSecret = Guid.Parse("C4C747ED-3A53-447F-BC72-BEA46175C2F0");
        public static Guid DyResource = Guid.Parse("0A37DD21-9884-4560-94B6-CD6BBE7031F5");
        public static Guid DyRedirectUrl = Guid.Parse("75b9165d-9bd7-42e7-902a-686b9545fc5c");
        public static Guid DyAccessToken = Guid.Parse("73EC88D7-DFA8-4D34-AEE6-BCD253EC9A15");
        public static Guid DyRefreshToken = Guid.Parse("242D9C00-EBDD-45C3-9F28-306F82D0A07F");
        public static Guid DyScope = Guid.Parse("9FD643E9-E2D5-4137-91A9-3CF2C34FF768");
        public static Guid DyRedirectUrlId = Guid.Parse("5CB4237C-0861-422E-A1C5-D88D4117C8E7");


        //CardId
        public static Guid HubspotDealCardId = Guid.Parse("************************************");
        //SettingsId

        public static Guid IsWelcomeMailSettingId = Guid.Parse("3CBB054E-6F25-4E6F-9176-9F44BE7A41EF");

        //const providerIds
        public const string ConstDynamicCRMProviderId = "10a5e65c-0967-4cf7-b959-ce4cb06c4dd7";
        public const string ConstFirefliesProviderId = "03029f41-55f5-40d8-b697-61abd493350b";
        public const string ConstPipedriveProviderId = "03a067ad-4bd3-4cf9-9d2e-73238a2f5a86";
        public const string ConstZendeskProviderId = "0d535674-70a3-4603-9d89-38223f8fe356";
        public const string ConstHubspotProviderId = "************************************";
        public const string ConstZohoCRMProviderId = "f9695da4-ae0e-41a3-a0e4-fbec795d2de5";
        public const string ConstSalesforceProviderId = "cf2f8bcc-f9a7-4aef-900c-32a615de4af1";
        public const string ConstSalesforceReviewProviderId = "d3bfe6cc-a874-4253-a059-89734ce01d75";
        public const string ConstFreshdeskProviderId = "2e2b4e02-ba58-4e9e-a70e-18942f099a06";
        public const string ConstFreshsalesProviderId = "fd4e5e65-3f71-4c48-a6f2-4b27f584a795";
        public const string ConstZohoRecruitProviderId = "8fafc8c0-a202-4613-a809-102f29332583";
        public const string ConstAgileCRMProviderId = "1adebdc2-54cd-4234-bb96-fa0d4a631b93";
        public const string ConstIntercomProviderId = "3b6b56d9-e19a-4a5a-8e13-ec20c65bb502";
        public const string ConstZohoContactsProviderId = "cd459cdc-4172-41fd-9dd9-8881067b70b2";
        // public const string ConstGmailProviderId = "b75ce275-**************-f6476cfbc67a";
        public const string ConstGsuiteProviderId = "b91cbaee-d9d0-490e-8458-1b5a8e4e2aac";
        public const string ConstGoogleCalendarProviderId = "a45b6b98-9d24-4c33-b464-ac585f768752";
        public const string ConstGoogleContactProviderId = "35b45aa5-10f5-428c-a264-9dd5854bac26";
        public const string ConstOutlookProviderId = "*************-4649-a65a-2616db545cd8";
        public const string ConstO365ProviderId = "1debff2d-57a7-4d65-9cb6-0c5b1b42087a";
        public const string ConstMSProviderId = "327915f0-677a-444c-99a8-1b4998a4623c";
        public const string ConstMSContactProviderId = "ecd27441-f9f9-4eda-9432-cd4e86c9d027";
        public const string ZohoCalendarId = "10468ad5-5fd5-4fc4-bed0-e8f48a114df9";
        public const string ZohoMailId = "21900222-fe90-435f-ac93-9624ee38605b";
        public const string UserContacts = "f9b2ae45-7c64-4dcf-a31f-be52d55746ba";
        public static Guid LinkedinSocialTypeId = Guid.Parse("0EA37808-1506-43A3-9ABD-6FF561941FF5");
        public static Guid WhatsAppSocialTypeId = Guid.Parse("4D312735-**************-EF89F2D6D7D2");
        //PhoneContacts ProviderId
        public static Guid ContactAddedByAdmin = Guid.Parse("1f7b38f4-bd9b-4deb-b928-2b122e9f37d2");
        public static Guid IOSContacts = Guid.Parse("963BDBA0-C791-4387-BE00-E25515E87041");
        public static Guid ContactAddedByUser = Guid.Parse("F9B2AE45-7C64-4DCF-A31F-BE52D55746BA");
        public static Guid WebContacts = Guid.Parse("B6960CEE-DD57-46FD-B88C-A2237DEC1D2F");
        public static Guid AndroidContacts = Guid.Parse("ADFD27E9-3ACD-47D8-BAA9-455B570EF2C4");
        public static Guid PeopleDataLabes = Guid.Parse("B8951A20-8CF4-47EE-9F18-1DDC4E7705AC");
        public static Guid Twitter = Guid.Parse("B76E4F1E-8FB0-46AA-8DD8-866EDDD84201");
        public static Guid MCTeams = Guid.Parse("5CC37A0C-96DD-4E30-93C7-A0A991156640");
        public static Guid Bot = Guid.Parse("825E7926-04DB-409F-8BCB-F48ED904156C");

        // identifierId
        public static Guid FireflieskeyIdentifierId = Guid.Parse("CACCAC0E-AD07-4598-B879-36C6037F47A3");
        public static Guid HarmonicApiKeyIdentifierId = Guid.Parse("F0A9A735-D747-4057-A6E0-678211AC3AAD");
        public static Guid BeanBagIdentifier = Guid.Parse("8B3E18DC-03C4-4BC6-A5E4-9DE49CE2457B");
        public static Guid ApolloIdentifier = Guid.Parse("C05DDCCC-F979-418C-A137-DF851C8A1B49");
        public static Guid SalesForcessurlIdentifier = Guid.Parse("CBE624DC-E82D-47BE-87DB-C45B8232BF24");
        public static Guid ZohoAccessToken = Guid.Parse("F5606D6C-7BD6-4CF0-809B-ED8C2DD6D0E3");
        public static Guid GoogleMailAppPasswordIdentifierId = Guid.Parse("A229814A-D0FC-4414-A31E-5BFD2933FF45");
        public static Guid GoogleMailEmailIdentifierId = Guid.Parse("7908DEE2-CEDB-4CDE-9FEA-18DFE494A523");    

        // Setting Ids
        public static Guid MorningTriggerMailSignalTypeId = Guid.Parse("EC1615D6-14C3-405A-8012-F319700DA701");
        public static Guid EveningTriggerMailSignalTypeId = Guid.Parse("EC1615D6-14C3-405A-8012-F319700DA702");
        public static Guid TrialStatusUpdateSignalTypeId = Guid.Parse("02C1ECAC-4BB8-4F5A-BDA6-741F68B413F5");
        public static Guid Drop3SignalTypeId = Guid.Parse("92C80779-A1EA-479E-9F7C-9760DE449B34");
        public static Guid TrialDateSettingId = Guid.Parse("CA508D5D-700E-4315-A0CD-F19BD46BBF13");
        public static Guid PurchasedPlanSettingId = Guid.Parse("CA508D5D-700E-4315-A0CD-F19BD46BBF19");
        public static Guid TotalCreditsSettings = Guid.Parse("FB993427-3680-4FEB-832A-968E9A9BD805");
        public static Guid TimeZoneSettingId = Guid.Parse("1D9BC314-786C-40E4-9FE4-769860C58F02");
        public static Guid NonVanishingCreditSettingId = Guid.Parse("FB993427-3680-4FEB-832A-968E9A9BD803");
        public static Guid VanishingCreditSettingId = Guid.Parse("FB993427-3680-4FEB-832A-968E9A9BD802");
        public static Guid SelectedLanguageSettingId = Guid.Parse("1D9BC314-786C-40E4-9FE4-769860C58F05");
        public static Guid CompanyApiCreditSettingId = Guid.Parse("42CB109F-A0C0-4689-9F40-33DFA05A3090");
        public static Guid MatchMakingApiCreditSettingId = Guid.Parse("48F2FC0C-BB35-4E0C-AC4C-1E8F33AE64A5");
        public static Guid CurrentStatusApiCreditSettingId = Guid.Parse("48F2FC0C-BB35-4E0C-AC4C-1E8F33AE64A6");
        public static Guid ShowAdsSettingId = Guid.Parse("455731BF-90CC-40BB-9547-32A8F9EE6504");
        public static Guid TeamSignalTypeId = Guid.Parse("A56AD9EF-98AE-449D-8329-EE30787D8E06");
        public static string UnsubscribeOneSignalApi = "https://onesignal.com/api/v1/notifications/{notifcationId}?app_id={appId}";
        //Keys
        public static string PDLKey = "347b5461540cd5510492fb11d179059b6358f8b0198fd2dd8aa44a44bb1f0a63"; //live key
        //public static string PDLKey = "aedf5607a26f3b24316de1829706df49cdc57ae32764f9e76f00b1715584e898";
        public static string HumanticKey = "chrexec_04ea87fb1c75a5163455fa21cc297e2e";
        public static string ApolloKey = "oTV6yD84l02o1wu7plq-pw";

        //Translate Azure 
        public static string azureTranslateKey ="06ea0cec9aa24bcf8e2ec7bcd2b2c0f1";
        public static string azureendpoint = "https://eu-ne-tr.cognitiveservices.azure.com/translator/text/v3.0/translate?from=en-US&to=";

        //URLs
        public static string ContextioContactsTemp = "http://wecontextiotemp.azurewebsites.net/index.php/api/Contextio/contacts?account_id=";
        public static string O365Contacts = "https://graph.microsoft.com/v1.0/me/contacts";
        public const string NeverBounceApi = "https://api.neverbounce.com/v4/single/check?key=private_8c48c7117b7b4f1a576091fde510ee12&email=";
        public const string WeatherApiUrl = "https://api.openweathermap.org/data/2.5/weather?q={cityname}&appid=f03346c6295125eb2072af5c6f0664db";
        public const string WeatherIconUrl = "http://openweathermap.org/img/wn/{icon}@4x.png";
        public static Guid ReadAIProviderId = Guid.Parse("BF1FB78E-168C-45D6-A7E4-6E6F2F0039F5");
        //Chargebee planId and Name
        public static string freemiumPlanId = "freemium";
        public static string BasicPlanId = "EmailStorage";
        public static string PremiumPlanId = "premium";
        public static string WhiteLabelUserPlanId = "white-label-user";
        //Chargebee Coupon id and Name
        public static string FreeForverCouponId = "FREEFORVER";
        public static string OnlyCRMCouponId = "ONLYCRM";
        public static string OneTimeCouponId = "ONETIME";
        public static string SUPPERDISCOUNTCouponId = "SUPPERDISCOUNT";
        public static string SUPperCouponId = "SUPPER";
        public static string AdminUserId = "2f46422b-ccfb-427d-847c-29bc1c5d374c";

        // public static string awsAccessKey = "********************";
        // public static string awssecretKey = "xZA/tjfBibWslhu1xhexR2jzd4hZi17PLjE5LJ35";

        public static string awsAccessKey = "********************";
        public static string awssecretKey = "dsOjnpIFCpFMeMQkrrw6vhGVRGZQyvklmIbjSdpf";

        //static UrlType Ids
        public static Guid AccessToken = Guid.Parse("7E71E9F0-9AB3-4491-A738-A2AD89C54E1F");
        public static Guid Authorization = Guid.Parse("BC0C4EB3-B800-4C18-82DE-04A140B3990F");
        public static Guid Me = Guid.Parse("D2055D76-B05F-4D14-886E-E21879069A12");

        public static string contactDatabaseId = "eu-fc-cs-cd";
        public static string eventDatabaseId = "eu-fc-cs-ed";

        public static string contactCosmosUrl = "https://eu-fc-cs-cd.documents.azure.com:443/";
        public static string contactCosmosPKey = "****************************************************************************************";

        public static string eventCosmosUrl = "https://eu-fc-cs-ed.documents.azure.com:443/";
        public static string eventCosmosPKey = "****************************************************************************************";

        public static List<string> contactCosmosContainers = new List<string> { "ActivityCall", "ContactEmail", "ContactHistory", "ContactPhone", "ContactSocial", "ActivityLog", "Note", "Contact" };
        public static readonly List<string> CRMProviders = new List<string> { "a044c04d-d433-4c56-9fb7-ce3c4f66b7ac", "b756b7a7-c77e-4758-8381-b81528fd642f", "dde71356-b613-49c1-9ab2-adc6a59510ec", "1F5CCE79-192C-40AF-A5A6-8E843DB42C20".ToLower() };
        public static List<string> PhoneContacts = new List<string>() { "1f7b38f4-bd9b-4deb-b928-2b122e9f37d2", "adfd27e9-3acd-47d8-baa9-455b570ef2c4", "b6960cee-dd57-46fd-b88c-a2237dec1d2f", "963bdba0-c791-4387-be00-e25515e87041" };
        public static List<string> eventCosmosContainers = new List<string> { "CalendarGroup", "CalendarModel", "EventAttendee", "EventModel" };
        public static List<string> avoidEmails = new List<string> { "@group.calendar.google.com", "noreply@", "no-reply@", "import.calendar.google.com" };
        public static List<string> replaceEmailsForPDL = new List<string> { "@help.", "@mail.", "@support.", "@email." };
        public static List<string> beReadyWithAccountList = new List<string> { "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>" };
        public static List<Header> headerList = new List<Header>();
        public static List<Identifier> identifierList = new List<Identifier>();
        public static List<Url> urlList = new List<Url>();
        public static List<Provider> providerList = new List<Provider>();
        public static List<ProviderUrl> providerUrlList = new List<ProviderUrl>();
        public static List<Language> languageList = new List<Language>();
        public static string TymlineApiUrl = "https://tymeline-linkedin.azurewebsites.net/discocean";
        public static string MoukhaBackendUrl = "https://moukha.backend.get1page.net/api/v3/";
        //public static string MoukhaBackendUrl = "https://ais.get1page.net/api/v1/";
        public static string SupersourcingAIUrl = "https://supersourcing-go.azurewebsites.net/api/v1/";
        public static string OnehashAIUrl = "https://onehash.azurewebsites.net/api/v1/";
        public static string DreamcastAIUrl = "https://dreamcast-data.azurewebsites.net/api/v1/";
        public static string CarabineGroupApiUrl = "https://saleslife-person-data.azurewebsites.net/api/v2/";
        public static string SaleslifeAIUrl = "https://cc494227.1pageplus.com/api/v2/";
        public static string PreMagiceAIUrl = "https://premagic.azurewebsites.net/api/v1/";
        public static string S3BucketUrlForPic = "https://onepagepersonimages.s3.eu-central-1.amazonaws.com/";
        //Metrics
        public static string MetricApi = "https://ap-south-1.aws.data.mongodb-api.com/app/data-mjnxo/endpoint/data/v1/action/insertOne";
        public static string MetricApiKey = "61nqacn6skr0ncYZMGlaRBEjkYKMU9KpYfD7eEPgnNm4qq666uX5T1x14xxlcNug";

        //  public static string TymlineCommunicationDataUrl = MoukhaBackendUrl + "external/communication-advice";
        public static string TymlineCommunicationDataUrl = "https://ais.get1page.net/api/v1/" + "external/communication-advice?lan=";
       
        public static StringBuilder sbnew = new StringBuilder();

        public static List<string> FilterMimeType = new List<string> { ".dll", ".exe", ".sys", ".dat", ".cab" };
        public static Dictionary<string, string> DemoContacts { get; set; } = new Dictionary<string, string> {
            {"Lucian Frias", "<EMAIL>|https://www.linkedin.com/in/fogoros/"},
            {"Yuan Chin", "<EMAIL>|https://www.linkedin.com/in/wysie/"},
            {"Rahul Sharma", "<EMAIL>|https://in.linkedin.com/in/rahul-sharma-ba349914"},
            {"Cirila Granado", "<EMAIL>|https://www.linkedin.com/in/cirila-gutierrez-202537b4/"},
            {"Hui Tang", "<EMAIL>|https://www.linkedin.com/in/tanghui/"},
            {"Rosie Anderson", "<EMAIL>|https://www.linkedin.com/in/rosieanderson/"},
            {"Charles Carter", "<EMAIL>|https://www.linkedin.com/in/charleschuckcarter/"}
       };

        public static List<string> testUserDomains = new List<string> { "mrawesomeapps.com", "mapit4.me", "whatelse.io" };
        public static Guid IsuserLinkedinSettingId=Guid.Parse("9B2C61A9-34F8-4A74-B97C-961E0E5A9D18");
        public static Guid IsLinkedinConfirmedByUserSettingId = Guid.Parse("2F89A107-C58A-463E-BB09-9075FC0472ED");
        //sendgrid tmeplate names
        public enum SendgridTemplateNames
        {
            VerifyYourEmail = 1,
            EmailSuccessfullyVerified = 2,
            WeGotIt = 3,
            WelcomeToWhatElse = 4,
            YourNewPassword = 5,
            OtpEmail = 6,
            WelcomeToWhatElsePasswordLink = 7,
            ForgotPasswordLink = 8,
            OrgDailyReportAttachment = 9
        }
        

       
        public static string returnTime(TimeSpan Duration)
        {
            int seconds = (int)Duration.TotalSeconds;
            if (seconds <= 60)
            {
                return seconds == 60 ? "1 min" : string.Format("{0} sec", seconds);
            }
            else if (seconds > 60 && seconds <= 3600)
            {
                return seconds == 3600 ? "1 hour" : string.Format("{0} min {1} sec", seconds / 60, seconds % 60);
            }
            else
            {
                return string.Format("{0} hour {1} min {2} sec", seconds / 3600, seconds / 60, seconds % 60);
            }
        }
        public static string GetDeviceType(Guid DeviceTypeId)
        {
            try
            {
                string dTypeId = DeviceTypeId.ToString().ToLower();
                switch (dTypeId)
                {
                    case "74acf511-0be3-4648-8148-0acd1eac9264":
                        return "Opera Extn";
                    case "5c620e97-0694-471a-9452-19b5a33338f6":
                        return "Edge Extn";
                    case "a3bff2d0-9e01-4c8b-8c78-1f93f597af66":
                        return "Tizen Phone";
                    case "eb04e9bc-cf02-4035-91c4-2a2ce3c3a5f4":
                        return "Chrome Extn";
                    case "85bf95b2-2315-4c59-9ef9-47f33cfa65ac":
                        return "iPhone";
                    case "db959c80-a012-448b-a23f-512eb3e07783":
                        return "Safari";
                    case "12b321fb-720f-44dc-a6a0-5c888b40f563":
                        return "Mac OS";
                    case "65d709ac-0841-4493-8fe0-6294e068a207":
                        return "Apple TV";
                    case "0d93384e-2ee0-4589-bbfa-658379513bcc":
                        return "Android TV";
                    case "c4560541-a3cd-4ff4-8199-682953d888b2":
                        return "Firefox Extn";
                    case "a59e15e8-e1bf-41a4-8876-75ce8963904c":
                        return "Apple Watch";
                    case "f771a11b-4ec9-45bd-b9f7-7884d6990e84":
                        return "Tizen Watch";
                    case "8dfe90e8-6f2c-4a60-be04-830261556f6b":
                        return "Device";
                    case "121d9c03-c1d8-490b-8026-9d4bd2690970":
                        return "Androd Gear";
                    case "7f606a20-a033-4dde-8aca-bf342614a8f3":
                        return "Windows";
                    case "8322fb32-2a4b-45c9-9af0-bf56a9df1a13":
                        return "XBox";
                    case "1e49b16c-6cfe-427c-9a71-c91a0fd1fc69":
                        return "Android Phone";
                    case "51b4c888-9e76-40d8-a290-d34f09961c32":
                        return "Safari Extn";
                    case "9c329792-9519-4d6c-be9d-f12898113416":
                        return "Website";
                    case "22bfd022-a73a-42de-9714-f26c8e81741f":
                        return "Tizen TV";
                    case "467aae32-8361-43a3-a8c7-be21e32c4f3b":
                        return "Teams";
                    case "9e9f203e-0820-4d58-840e-503edc28b505":
                        return "Customer Admin Portal";
                    default:
                        return "Unknown Device!";
                }

            }
            catch (Exception ex)
            {
                return "";
            }
        }
        public static string GetLoginFlow(Guid loginFlowId)
        {
            try
            {
                string lFlowId = loginFlowId.ToString().ToLower();
                switch (lFlowId)
                {
                    case "c893b1cc-f74f-4afd-bba3-56532cb4c46b":
                        return "LinkedIn";
                    case "4eb93b78-64fd-4a3e-9428-f6dd41d98584":
                        return "Microsoft";
                    case "5cc37a0c-96dd-4e30-93c7-a0a991156640":
                        return "Microsoft";
                    case "327915f0-677a-444c-99a8-1b4998a4623c":
                        return "Microsoft";
                    case "232a6c90-a9f4-4b8c-98b9-fc050e84e184":
                        return "Google";
                    case "034208d2-fe6c-49e6-8d72-70e38598dc2e":
                        return "Apple";
                    case "6f7398b7-a552-4768-ad95-f47b9e1e6faf":
                        return "Magic Link";
                    case "5ccaff6a-06b5-4c31-80c0-f0171fa36422":
                        return "AccessTo1Page";
                    case "":
                        return "App Source";
                    default:
                        return "";
                }
            }
            catch (Exception ex)
            {
                return "";
            }
        }
        public static List<string> GetUniquePhoneNumberQueriesForContact(List<ContactPhone> phones, string ProviderId, string contactProviderId = "")
        {
            List<string> output = new List<string>();
            foreach (var item in phones)
            {
                string phoneLike = "";
                if (ProviderId.ToLower() == "F9695DA4-AE0E-41A3-A0E4-FBEC795D2DE5".ToLower())//zoho
                {
                    if (!string.IsNullOrEmpty(contactProviderId) && contactProviderId.ToLower().Contains(ProviderId.ToLower()))
                    {
                        var number = item.PNumber;
                        var phone = SanitizeNumber(number);
                        phoneLike = "phone=" + phone;
                    }
                    else
                    {
                        var phone = SanitizeNumber(item.PNumber);
                        phoneLike = "phone=" + phone;
                    }
                }
                else if (ProviderId.ToLower() == "8FAFC8C0-A202-4613-A809-102F29332583".ToLower() || ProviderId.ToLower() == "3B6B56D9-E19A-4A5A-8E13-EC20C65BB502".ToLower() || ProviderId.ToLower() == "************************************".ToLower() || ProviderId.ToLower() == "03A067AD-4BD3-4CF9-9D2E-73238A2F5A86".ToLower() || ProviderId.ToLower() == "fd4e5e65-3f71-4c48-a6f2-4b27f584a795".ToLower() || ProviderId.ToLower() == "10A5E65C-0967-4CF7-B959-CE4CB06C4DD7".ToLower() || ProviderId.ToLower() == "1ADEBDC2-54CD-4234-BB96-FA0D4A631B93".ToLower())//zoho recruit, hubspot,Freshsales,dynamic365,agile and pipedrive
                {
                    if (!string.IsNullOrEmpty(item.ProviderId.ToString()) && (PhoneContacts.Contains(item.ProviderId.ToString()) || item.ProviderId.ToString().ToLower() == UserContacts))
                    {
                        var phone = SanitizeNumber(item.PNumber);
                        phoneLike = phone;
                    }
                    else
                        phoneLike = item.PNumber;
                }
                else if (ProviderId.ToLower() == "CF2F8BCC-F9A7-4AEF-900C-32A615DE4AF1".ToLower())
                {
                    var number = LibPhoneNumber.GetNumberForSalesForce(item.SNumber);
                    var num = SanitizeNumber(number);
                    string phone = "phone = '" + number + "' OR mobilephone = '" + number + "'";
                    //   string phonemodified = item.SanitizedNumber.Substring(3);
                    if (num != null && num.Trim().Length >= 10)
                    {
                        string temp = string.Format("({0}) {1}-{2}", num.Substring(0, 3), num.Substring(3, 3), num.Substring(6, 4));
                        phone += " OR " + "phone = '" + temp + "' OR mobilephone = '" + temp + "'";
                    }
                    if (num != null && num.Length < 10)
                    {
                        phone += " OR " + "phone = '" + num + "' OR mobilephone = '" + num + "'";
                    }
                    phoneLike += phone;
                }

                if (!string.IsNullOrEmpty(phoneLike) && !output.Contains(phoneLike))
                    output.Add(phoneLike);
            }
            return output;
        }
        public static async Task<string> GenericMethod(string json, string url, int methodType, string access_token = "0")
        {
            var client = new RestSharp.RestClient(url);
            var request = (methodType == 1) ? new RestRequest(Method.POST) : new RestRequest(Method.GET);
            request.AddHeader("content-type", "application/json;charset=UTF-8");
            if (access_token != "0") { request.AddHeader("authorization", "Bearer " + access_token); }
            request.AddParameter("application/json;charset=UTF-8", json, ParameterType.RequestBody);
            var response = await client.ExecuteAsync(request);
            return response.Content;
        }

        public static async Task<string> RealmMethod(string json, string url, int methodType, string userid = "")
        {
            var client = new RestSharp.RestClient(url);
            var request = (methodType == 1) ? new RestRequest(Method.POST) : new RestRequest(Method.GET);
            request.AddHeader("content-type", "application/json;charset=UTF-8");
            if (!string.IsNullOrEmpty(userid)) { request.AddHeader("userid", userid); }
            if (methodType == 1)
            {
                request.AddParameter("application/json;charset=UTF-8", json, ParameterType.RequestBody);
            }
            var response = await client.ExecuteAsync(request);
            return response.Content;
        }

        public static IRestResponse GenericMethodReturnResponse(string json, string url, int methodType, string access_token = "0")
        {
            var client = new RestSharp.RestClient(url);
            var request = (methodType == 1) ? new RestRequest(Method.POST) : new RestRequest(Method.GET);
            request.AddHeader("content-type", "application/json;charset=UTF-8");
            if (access_token != "0") { request.AddHeader("authorization", "Bearer " + access_token); }
            request.AddParameter("application/json;charset=UTF-8", json, ParameterType.RequestBody);
            var response = client.Execute(request);
            return response;
        }

        public static IRestResponse IntercomRestApi(string json, string url, string access_token)
        {
            var client = new RestClient(url);
            var request = new RestRequest(Method.POST);
            request.AddHeader("Content-Type", "application/json");
            request.AddHeader("Authorization", "Bearer " + access_token);
            request.AddHeader("Accept", "application/json");
            request.AddParameter("application/json;charset=UTF-8", json, ParameterType.RequestBody);
            var response = client.Execute(request);
            return response;
        }
        public static IRestResponse SendGridRestApi(string json, string url, string api_key)
        {
            var client = new RestClient(url);
            var request = new RestRequest(Method.POST);
            request.AddHeader("Content-Type", "application/json");
            request.AddHeader("Authorization", "Bearer " + api_key);
            request.AddHeader("Accept", "application/json");
            request.AddParameter("application/json;charset=UTF-8", json, ParameterType.RequestBody);
            var response = client.Execute(request);
            return response;
        }
        public static IRestResponse SendGridAddContactRestApi(string json, string url, string api_key)
        {
            var client = new RestClient(url);
            var request = new RestRequest(Method.PUT);
            request.AddHeader("Content-Type", "application/json");
            request.AddHeader("Authorization", "Bearer " + api_key);
            request.AddHeader("Accept", "application/json");
            request.AddParameter("application/json;charset=UTF-8", json, ParameterType.RequestBody);
            var response = client.Execute(request);
            return response;
        }
        public static IRestResponse GenericMethodToGetFromDB(string json, string url, int methodType, string access_token = "0")
        {
            var client = new RestSharp.RestClient(url);
            var request = (methodType == 1) ? new RestRequest(Method.POST) : new RestRequest(Method.GET);
            request.AddHeader("content-type", "application/json;charset=UTF-8");
            if (access_token != "0") { request.AddHeader("authtoken", access_token); }
            request.AddParameter("application/json;charset=UTF-8", json, ParameterType.RequestBody);
            var response = client.Execute(request);
            return response;

        }


        public static async Task<string> GenericMethod2(string json, string url, int methodType, string access_token = "0")
        {
            var client = new RestSharp.RestClient(url);
            var request = (methodType == 1) ? new RestRequest(Method.POST) : new RestRequest(Method.GET);
            request.AddHeader("content-type", "application/json;charset=UTF-8");
            if (access_token != "0") { request.AddHeader("authorization", "Bearer " + access_token); }
            request.AddParameter("application/json;charset=UTF-8", json, ParameterType.RequestBody);
            var response = await client.ExecuteAsync(request);
            return response.Content;
        }
        #region Generic Methods

        public static string DecodeBase64String(string s)
        {
            if (!string.IsNullOrEmpty(s))
            {
                if (s.Contains("=="))
                {
                    s = s.Replace("==", "");
                }
                var bc = UrlBase64.Decode(s);
                var tts = Encoding.UTF8.GetString(bc);
                return tts;
            }
            else
            {
                return null;
            }
        }

        public static string GetNestedBodyParts(IList<MessagePart> part, string curr)
        {
            string str = curr;
            try
            {
                if (part == null)
                {
                    return str;
                }
                else
                {
                    if (part.Count(w => w.MimeType != "text/plain") > 0)
                    {
                        foreach (var parts in part.Where(w => w.MimeType != "text/plain"))
                        {
                            if (parts.Parts == null)
                            {
                                try
                                {
                                    if (parts.Body != null && parts.Body.Data != null)
                                    {
                                        var ts = DecodeBase64String(parts.Body.Data);
                                        str += ts;
                                    }
                                }
                                catch (Exception ex)
                                {

                                }
                            }
                            else
                            {
                                return GetNestedBodyParts(parts.Parts, str);
                            }
                        }
                    }
                    else if (part.Count(w => w.MimeType == "text/plain") > 0)
                    {
                        foreach (var parts in part.Where(w => w.MimeType == "text/plain"))
                        {
                            if (parts.Parts == null)
                            {
                                if (parts.Body != null && parts.Body.Data != null)
                                {
                                    var ts = DecodeBase64String(parts.Body.Data);
                                    str += ts;
                                }
                            }
                            else
                            {
                                return GetNestedBodyParts(parts.Parts, str);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {

            }
            return str;
        }

        #endregion
        //public static async Task<string> office365AccountToken(string url, string refreshToken, string client_id, string client_secret)
        //{
        //    var client = new RestClient(url);
        //    var request = new RestRequest(Method.POST);
        //    request.AddHeader("content-type", "application/x-www-form-urlencoded");
        //    request.AddParameter("application/x-www-form-urlencoded", "grant_type=refresh_token&" +
        //        "refresh_token=" + refreshToken +
        //        "&client_id=" + client_id +
        //        "&client_secret=" + client_secret
        //        , ParameterType.RequestBody);
        //    var response = await client.ExecuteTaskAsync(request);
        //    return response.Content;
        //}
        #region SanitizeNumber Methods
        static bool Contains(this string keyString, char c)
        {
            return keyString.IndexOf(c) >= 0;
        }

        public static string SanitizeNumber(string phoneNumber)
        {
            try
            {
                phoneNumber = phoneNumber.Replace("-", "").Replace("(", "").Replace(")", "").Replace(" ", "").Replace(".", "").Replace(",", "");
                if (phoneNumber.StartsWith("0"))
                {
                    if (phoneNumber.Length > 10)
                        phoneNumber = phoneNumber.Substring(1);
                    phoneNumber = phoneNumber.TrimStart('0');
                }
                if (phoneNumber.StartsWith("00"))
                {
                    phoneNumber = phoneNumber.Substring(2);
                    phoneNumber = "+" + phoneNumber;
                }
                return phoneNumber;
            }
            catch (Exception ex)
            {
                return ex.Message;
            }
        }

        //public static string SanitizeNumber(string phoneNumber, string userCountryId)
        //{
        //    try
        //    {
        //        com.google.i18n.phonenumbers.PhoneNumberUtil phoneUtil = com.google.i18n.phonenumbers.PhoneNumberUtil.getInstance();
        //        string snumber = "";
        //        int PType = 0;
        //        phoneNumber = phoneNumber.Replace(" ", "");
        //        phoneNumber = (phoneNumber.StartsWith("+") ? "+" : "") + PhoneWord.ToNumber(phoneNumber);
        //        PhoneNumber numberProto;
        //        if (phoneNumber.StartsWith("+"))
        //        {
        //            numberProto = phoneUtil.parse(phoneNumber, "");
        //            string type = phoneUtil.getNumberType(numberProto).ToString();
        //            PType = (int)Enum.Parse(typeof(PhoneNumberType), type);

        //            if (numberProto.IsPossibleNumber)
        //            {
        //                snumber = phoneUtil.format(numberProto, com.google.i18n.phonenumbers.PhoneNumberUtil.PhoneNumberFormat.INTERNATIONAL);
        //                return snumber + "|" + phoneUtil.getRegionCodeForNumber(numberProto) + "|" + phoneUtil.format(numberProto, com.google.i18n.phonenumbers.PhoneNumberUtil.PhoneNumberFormat.E164) + "|" + PType;
        //            }
        //        }

        //        numberProto = phoneUtil.parse(phoneNumber, userCountryId);
        //        if (numberProto.IsPossibleNumber)
        //        {
        //            string type = phoneUtil.getNumberType(numberProto).ToString();
        //            PType = (int)Enum.Parse(typeof(PhoneNumberType), type);

        //            snumber = phoneUtil.format(numberProto, com.google.i18n.phonenumbers.PhoneNumberUtil.PhoneNumberFormat.INTERNATIONAL);
        //            return snumber + "|" + phoneUtil.getRegionCodeForNumber(numberProto) + "|" + phoneUtil.format(numberProto, com.google.i18n.phonenumbers.PhoneNumberUtil.PhoneNumberFormat.E164) + "|" + PType;
        //        }
        //        else
        //        {
        //            return phoneNumber + "| IN | | ";
        //        }
        //    }
        //    catch (Exception)
        //    {
        //        return "";
        //    }
        //}

        public static string SanitizeNumberE164(string phoneNumber, string countryPrefix)
        {

            var snumber = "";
            PhoneNumbers.PhoneNumberUtil util = PhoneNumbers.PhoneNumberUtil.GetInstance();
            try
            {
                PhoneNumbers.PhoneNumber number = util.Parse(phoneNumber, countryPrefix);
                if (util.IsValidNumber(number))
                {
                    snumber = util.Format(number, PhoneNumbers.PhoneNumberFormat.E164);
                    return snumber;
                }
                else
                    return "";
            }
            catch (Exception ex)
            {
                return "";
            }
        }

        public static string SanitizeNumberForContactPhone(string phoneNumber, string userCountryId)
        {
            try
            {
                PhoneNumbers.PhoneNumberUtil phoneUtil = PhoneNumbers.PhoneNumberUtil.GetInstance();
                string snumber = "";
                int PType = 0;
                phoneNumber = phoneNumber.Replace(" ", "");
                phoneNumber = (phoneNumber.StartsWith("+") ? "+" : "") + PhoneWord.ToNumber(phoneNumber);
                PhoneNumbers.PhoneNumber numberProto;
                if (phoneNumber.StartsWith("+"))
                {
                    numberProto = phoneUtil.ParseAndKeepRawInput(phoneNumber, "");
                    string type = phoneUtil.GetNumberType(numberProto).ToString();
                    PType = (int)Enum.Parse(typeof(PhoneNumbers.PhoneNumberType), type);

                    if (phoneUtil.IsPossibleNumber(numberProto))
                    {
                        snumber = phoneUtil.Format(numberProto, PhoneNumbers.PhoneNumberFormat.NATIONAL);
                        snumber = SanitizeNumber(snumber);
                        return snumber + "|" + phoneUtil.GetRegionCodeForNumber(numberProto) + "|" + phoneUtil.Format(numberProto, PhoneNumbers.PhoneNumberFormat.E164) + "|" + PType;
                    }
                }

                numberProto = phoneUtil.ParseAndKeepRawInput(phoneNumber, userCountryId);
                if (phoneUtil.IsPossibleNumber(numberProto))
                {
                    PhoneNumbers.PhoneNumber altNumberProto = new PhoneNumbers.PhoneNumber();
                    bool altInvalid = false;
                    try
                    {
                        altNumberProto = phoneUtil.ParseAndKeepRawInput("+" + phoneNumber, "");
                        altInvalid = !phoneUtil.IsPossibleNumber(altNumberProto);
                    }
                    catch (Exception)
                    {
                        altInvalid = true;
                    }

                    if (!phoneUtil.IsValidNumber(numberProto) && !altInvalid)
                    {
                        string type = phoneUtil.GetNumberType(altNumberProto).ToString();
                        PType = (int)Enum.Parse(typeof(PhoneNumberType), type);

                        if (phoneUtil.IsPossibleNumber(altNumberProto))
                        {
                            snumber = phoneUtil.Format(altNumberProto, PhoneNumbers.PhoneNumberFormat.NATIONAL);
                            snumber = SanitizeNumber(snumber);
                            return snumber + "|" + phoneUtil.GetRegionCodeForNumber(altNumberProto) + "|" + phoneUtil.Format(altNumberProto, PhoneNumbers.PhoneNumberFormat.E164) + "|" + PType;
                        }
                    }
                    else
                    {
                        string type = phoneUtil.GetNumberType(numberProto).ToString();
                        PType = (int)Enum.Parse(typeof(PhoneNumberType), type);

                        snumber = phoneUtil.Format(numberProto, PhoneNumbers.PhoneNumberFormat.NATIONAL);
                        snumber = SanitizeNumber(snumber);
                        return snumber + "|" + phoneUtil.GetRegionCodeForNumber(numberProto) + "|" + phoneUtil.Format(numberProto, PhoneNumbers.PhoneNumberFormat.E164) + "|" + PType;
                    }
                }
                return phoneNumber + "| IN | | ";
            }
            catch (Exception ex)
            {
                string op = ex.ToString();
                Console.WriteLine(op);
                return "";
            }
        }

        public class PhoneWord
        {
            public static string ToNumber(string raw)
            {
                if (string.IsNullOrWhiteSpace(raw))
                    return "";
                else
                    raw = raw.ToUpperInvariant();

                var newNumber = new StringBuilder();
                foreach (var c in raw)
                {
                    if (" -0123456789".Contains(c))
                        newNumber.Append(c);
                    else
                    {
                        var result = TranslateToNumber(c);
                        if (result != null)
                            newNumber.Append(result);
                    }
                    // otherwise we've skipped a non-numeric char
                }
                return newNumber.ToString();
            }


            static int? TranslateToNumber(char c)
            {
                if ("ABC".Contains(c))
                    return 2;
                else if ("DEF".Contains(c))
                    return 3;
                else if ("GHI".Contains(c))
                    return 4;
                else if ("JKL".Contains(c))
                    return 5;
                else if ("MNO".Contains(c))
                    return 6;
                else if ("PQRS".Contains(c))
                    return 7;
                else if ("TUV".Contains(c))
                    return 8;
                else if ("WXYZ".Contains(c))
                    return 9;
                return null;
            }

        }



        #endregion

        public static int GetTypeForHubspot(string typeString)
        {
            int output = 4;
            switch (typeString)
            {
                case "note":
                    output = 4;
                    break;
                case "task":
                case "todo":
                    output = 3;
                    break;
                case "email":
                    output = 1;
                    break;
                case "meeting":
                    output = 2;
                    break;
                case "call":
                    output = 0;
                    break;
                default:
                    output = 3;
                    break;
            }
            return output;
        }
        public static int GetTypeForSalesforce(string typeString)
        {
            int output = 4;
            switch (typeString)
            {
                case "task":
                    output = 3;
                    break;
                case "email":
                    output = 1;
                    break;
                case "call":
                    output = 0;
                    break;
                default:
                    output = 3;
                    break;
            }
            return output;
        }
        public static int GetTypeForPipedrive(string typeString)
        {
            int output;
            switch (typeString)
            {
                case "call":
                    output = 0;
                    break;
                case "meeting":
                    output = 2;
                    break;
                case "email":
                    output = 1;
                    break;
                case "task":
                    output = 3;
                    break;
                default:
                    output = 3;
                    break;
            }
            return output;
        }
        public static class CustomJsonSerializer
        {
            public static string Serialize<T>(T obj)
            {
                var settings = new JsonSerializerSettings
                {
                    ReferenceLoopHandling = ReferenceLoopHandling.Ignore
                };
                return JsonConvert.SerializeObject(obj, settings);
            }
        }
        public static class StopwordTool
        {
            /// <summary>
            /// Words we want to remove.
            /// </summary>
            static Dictionary<string, bool> _stops = new Dictionary<string, bool>
            {
                { "a", true },
                { "about", true },
                { "above", true },
                { "across", true },
                { "after", true },
                { "afterwards", true },
                { "again", true },
                { "against", true },
                { "all", true },
                { "almost", true },
                { "alone", true },
                { "along", true },
                { "already", true },
                { "also", true },
                { "although", true },
                { "always", true },
                { "am", true },
                { "among", true },
                { "amongst", true },
                { "amount", true },
                { "an", true },
                { "and", true },
                { "another", true },
                { "any", true },
                { "anyhow", true },
                { "anyone", true },
                { "anything", true },
                { "anyway", true },
                { "anywhere", true },
                { "are", true },
                { "around", true },
                { "as", true },
                { "at", true },
                { "back", true },
                { "be", true },
                { "became", true },
                { "because", true },
                { "become", true },
                { "becomes", true },
                { "becoming", true },
                { "been", true },
                { "before", true },
                { "beforehand", true },
                { "behind", true },
                { "being", true },
                { "below", true },
                { "beside", true },
                { "besides", true },
                { "between", true },
                { "beyond", true },
                { "bill", true },
                { "both", true },
                { "bottom", true },
                { "but", true },
                { "by", true },
                { "call", true },
                { "can", true },
                { "cannot", true },
                { "cant", true },
                { "co", true },
                { "computer", true },
                { "con", true },
                { "could", true },
                { "couldnt", true },
                { "cry", true },
                { "de", true },
                { "describe", true },
                { "detail", true },
                { "do", true },
                { "done", true },
                { "down", true },
                { "due", true },
                { "during", true },
                { "each", true },
                { "eg", true },
                { "eight", true },
                { "either", true },
                { "eleven", true },
                { "else", true },
                { "elsewhere", true },
                { "empty", true },
                { "enough", true },
                { "etc", true },
                { "even", true },
                { "ever", true },
                { "every", true },
                { "everyone", true },
                { "everything", true },
                { "everywhere", true },
                { "except", true },
                { "few", true },
                { "fifteen", true },
                { "fify", true },
                { "fill", true },
                { "find", true },
                { "fire", true },
                { "first", true },
                { "five", true },
                { "for", true },
                { "former", true },
                { "formerly", true },
                { "forty", true },
                { "found", true },
                { "four", true },
                { "from", true },
                { "front", true },
                { "full", true },
                { "further", true },
                { "get", true },
                { "give", true },
                { "go", true },
                { "had", true },
                { "has", true },
                { "have", true },
                { "he", true },
                { "hence", true },
                { "her", true },
                { "here", true },
                { "hereafter", true },
                { "hereby", true },
                { "herein", true },
                { "hereupon", true },
                { "hers", true },
                { "herself", true },
                { "him", true },
                { "himself", true },
                { "his", true },
                { "how", true },
                { "however", true },
                { "hundred", true },
                { "i", true },
                { "ie", true },
                { "if", true },
                { "in", true },
                { "inc", true },
                { "indeed", true },
                { "interest", true },
                { "into", true },
                { "is", true },
                { "it", true },
                { "its", true },
                { "itself", true },
                { "keep", true },
                { "last", true },
                { "latter", true },
                { "latterly", true },
                { "least", true },
                { "less", true },
                { "ltd", true },
                { "made", true },
                { "many", true },
                { "may", true },
                { "me", true },
                { "meanwhile", true },
                { "might", true },
                { "mill", true },
                { "mine", true },
                { "more", true },
                { "moreover", true },
                { "most", true },
                { "mostly", true },
                { "move", true },
                { "much", true },
                { "must", true },
                { "my", true },
                { "myself", true },
                { "name", true },
                { "namely", true },
                { "neither", true },
                { "never", true },
                { "nevertheless", true },
                { "next", true },
                { "nine", true },
                { "no", true },
                { "nobody", true },
                { "none", true },
                { "nor", true },
                { "not", true },
                { "nothing", true },
                { "now", true },
                { "nowhere", true },
                { "of", true },
                { "off", true },
                { "often", true },
                { "on", true },
                { "once", true },
                { "one", true },
                { "only", true },
                { "onto", true },
                { "or", true },
                { "other", true },
                { "others", true },
                { "otherwise", true },
                { "our", true },
                { "ours", true },
                { "ourselves", true },
                { "out", true },
                { "over", true },
                { "own", true },
                { "part", true },
                { "per", true },
                { "perhaps", true },
                { "please", true },
                { "put", true },
                { "rather", true },
                { "re", true },
                { "same", true },
                { "see", true },
                { "seem", true },
                { "seemed", true },
                { "seeming", true },
                { "seems", true },
                { "serious", true },
                { "several", true },
                { "she", true },
                { "should", true },
                { "show", true },
                { "side", true },
                { "since", true },
                { "sincere", true },
                { "six", true },
                { "sixty", true },
                { "so", true },
                { "some", true },
                { "somehow", true },
                { "someone", true },
                { "something", true },
                { "sometime", true },
                { "sometimes", true },
                { "somewhere", true },
                { "still", true },
                { "such", true },
                { "system", true },
                { "take", true },
                { "ten", true },
                { "than", true },
                { "that", true },
                { "the", true },
                { "their", true },
                { "them", true },
                { "themselves", true },
                { "then", true },
                { "thence", true },
                { "there", true },
                { "thereafter", true },
                { "thereby", true },
                { "therefore", true },
                { "therein", true },
                { "thereupon", true },
                { "these", true },
                { "they", true },
                { "thick", true },
                { "thin", true },
                { "third", true },
                { "this", true },
                { "those", true },
                { "though", true },
                { "three", true },
                { "through", true },
                { "throughout", true },
                { "thru", true },
                { "thus", true },
                { "to", true },
                { "together", true },
                { "too", true },
                { "top", true },
                { "toward", true },
                { "towards", true },
                { "twelve", true },
                { "twenty", true },
                { "two", true },
                { "un", true },
                { "under", true },
                { "until", true },
                { "up", true },
                { "upon", true },
                { "us", true },
                { "very", true },
                { "via", true },
                { "was", true },
                { "we", true },
                { "well", true },
                { "were", true },
                { "what", true },
                { "whatever", true },
                { "when", true },
                { "whence", true },
                { "whenever", true },
                { "where", true },
                { "whereafter", true },
                { "whereas", true },
                { "whereby", true },
                { "wherein", true },
                { "whereupon", true },
                { "wherever", true },
                { "whether", true },
                { "which", true },
                { "while", true },
                { "whither", true },
                { "who", true },
                { "whoever", true },
                { "whole", true },
                { "whom", true },
                { "whose", true },
                { "why", true },
                { "will", true },
                { "with", true },
                { "within", true },
                { "without", true },
                { "would", true },
                { "yet", true },
                { "you", true },
                { "your", true },
                { "yours", true },
                { "yourself", true },
                { "yourselves", true }
            };

            static char[] _delimiters = new char[]
            {
               ' ',
               ',',
               ';',
               '.'
           };
            public static List<string> RemoveStopwords(string input)
            {
                List<string> flattenList = new List<string>();
                // 1
                // Split parameter into words
                var words = input.Split(_delimiters,
                    StringSplitOptions.RemoveEmptyEntries);
                // 2
                // Allocate new dictionary to store found words
                var found = new Dictionary<string, bool>();
                // 3
                // Store results in this StringBuilder
                StringBuilder builder = new StringBuilder();
                // 4
                // Loop through all words
                foreach (string currentWord in words)
                {
                    // 5
                    // Convert to lowercase
                    string lowerWord = currentWord.ToLower();
                    // 6
                    // If this is a usable word, add it
                    if (!_stops.ContainsKey(lowerWord) &&
                        !found.ContainsKey(lowerWord))
                    {
                        builder.Append(currentWord).Append(' ');
                        flattenList.Add(lowerWord);
                        found.Add(lowerWord, true);
                    }
                }


                // Return string with words removed
                return flattenList;
            }
        }

        public class FileExtensionToMimeType
        {
            private static IDictionary<string, string> _mappings = new Dictionary<string, string>(StringComparer.InvariantCultureIgnoreCase) {

        #region Big freaking list of mime types
        // combination of values from Windows 7 Registry and 
        // from C:\Windows\System32\inetsrv\config\applicationHost.config
        // some added, including .7z and .dat
        {".323", "text/h323"},
        {".3g2", "video/3gpp2"},
        {".3gp", "video/3gpp"},
        {".3gp2", "video/3gpp2"},
        {".3gpp", "video/3gpp"},
        {".7z", "application/x-7z-compressed"},
        {".aa", "audio/audible"},
        {".AAC", "audio/aac"},
        {".aaf", "application/octet-stream"},
        {".aax", "audio/vnd.audible.aax"},
        {".ac3", "audio/ac3"},
        {".aca", "application/octet-stream"},
        {".accda", "application/msaccess.addin"},
        {".accdb", "application/msaccess"},
        {".accdc", "application/msaccess.cab"},
        {".accde", "application/msaccess"},
        {".accdr", "application/msaccess.runtime"},
        {".accdt", "application/msaccess"},
        {".accdw", "application/msaccess.webapplication"},
        {".accft", "application/msaccess.ftemplate"},
        {".acx", "application/internet-property-stream"},
        {".AddIn", "text/xml"},
        {".ade", "application/msaccess"},
        {".adobebridge", "application/x-bridge-url"},
        {".adp", "application/msaccess"},
        {".ADT", "audio/vnd.dlna.adts"},
        {".ADTS", "audio/aac"},
        {".afm", "application/octet-stream"},
        {".ai", "application/postscript"},
        {".aif", "audio/x-aiff"},
        {".aifc", "audio/aiff"},
        {".aiff", "audio/aiff"},
        {".air", "application/vnd.adobe.air-application-installer-package+zip"},
        {".amc", "application/x-mpeg"},
        {".application", "application/x-ms-application"},
        {".art", "image/x-jg"},
        {".asa", "application/xml"},
        {".asax", "application/xml"},
        {".ascx", "application/xml"},
        {".asd", "application/octet-stream"},
        {".asf", "video/x-ms-asf"},
        {".ashx", "application/xml"},
        {".asi", "application/octet-stream"},
        {".asm", "text/plain"},
        {".asmx", "application/xml"},
        {".aspx", "application/xml"},
        {".asr", "video/x-ms-asf"},
        {".asx", "video/x-ms-asf"},
        {".atom", "application/atom+xml"},
        {".au", "audio/basic"},
        {".avi", "video/x-msvideo"},
        {".axs", "application/olescript"},
        {".bas", "text/plain"},
        {".bcpio", "application/x-bcpio"},
        {".bin", "application/octet-stream"},
        {".bmp", "image/bmp"},
        {".c", "text/plain"},
        {".cab", "application/octet-stream"},
        {".caf", "audio/x-caf"},
        {".calx", "application/vnd.ms-office.calx"},
        {".cat", "application/vnd.ms-pki.seccat"},
        {".cc", "text/plain"},
        {".cd", "text/plain"},
        {".cdda", "audio/aiff"},
        {".cdf", "application/x-cdf"},
        {".cer", "application/x-x509-ca-cert"},
        {".chm", "application/octet-stream"},
        {".class", "application/x-java-applet"},
        {".clp", "application/x-msclip"},
        {".cmx", "image/x-cmx"},
        {".cnf", "text/plain"},
        {".cod", "image/cis-cod"},
        {".config", "application/xml"},
        {".contact", "text/x-ms-contact"},
        {".coverage", "application/xml"},
        {".cpio", "application/x-cpio"},
        {".cpp", "text/plain"},
        {".crd", "application/x-mscardfile"},
        {".crl", "application/pkix-crl"},
        {".crt", "application/x-x509-ca-cert"},
        {".cs", "text/plain"},
        {".csdproj", "text/plain"},
        {".csh", "application/x-csh"},
        {".csproj", "text/plain"},
        {".css", "text/css"},
        {".csv", "text/csv"},
        {".cur", "application/octet-stream"},
        {".cxx", "text/plain"},
        {".dat", "application/octet-stream"},
        {".datasource", "application/xml"},
        {".dbproj", "text/plain"},
        {".dcr", "application/x-director"},
        {".def", "text/plain"},
        {".deploy", "application/octet-stream"},
        {".der", "application/x-x509-ca-cert"},
        {".dgml", "application/xml"},
        {".dib", "image/bmp"},
        {".dif", "video/x-dv"},
        {".dir", "application/x-director"},
        {".disco", "text/xml"},
        {".dll", "application/x-msdownload"},
        {".dll.config", "text/xml"},
        {".dlm", "text/dlm"},
        {".doc", "application/msword"},
        {".docm", "application/vnd.ms-word.document.macroEnabled.12"},
        {".docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"},
        {".dot", "application/msword"},
        {".dotm", "application/vnd.ms-word.template.macroEnabled.12"},
        {".dotx", "application/vnd.openxmlformats-officedocument.wordprocessingml.template"},
        {".dsp", "application/octet-stream"},
        {".dsw", "text/plain"},
        {".dtd", "text/xml"},
        {".dtsConfig", "text/xml"},
        {".dv", "video/x-dv"},
        {".dvi", "application/x-dvi"},
        {".dwf", "drawing/x-dwf"},
        {".dwp", "application/octet-stream"},
        {".dxr", "application/x-director"},
        {".eml", "message/rfc822"},
        {".emz", "application/octet-stream"},
        {".eot", "application/octet-stream"},
        {".eps", "application/postscript"},
        {".etl", "application/etl"},
        {".etx", "text/x-setext"},
        {".evy", "application/envoy"},
        {".exe", "application/octet-stream"},
        {".exe.config", "text/xml"},
        {".fdf", "application/vnd.fdf"},
        {".fif", "application/fractals"},
        {".filters", "Application/xml"},
        {".fla", "application/octet-stream"},
        {".flr", "x-world/x-vrml"},
        {".flv", "video/x-flv"},
        {".fsscript", "application/fsharp-script"},
        {".fsx", "application/fsharp-script"},
        {".generictest", "application/xml"},
        {".gif", "image/gif"},
        {".group", "text/x-ms-group"},
        {".gsm", "audio/x-gsm"},
        {".gtar", "application/x-gtar"},
        {".gz", "application/x-gzip"},
        {".h", "text/plain"},
        {".hdf", "application/x-hdf"},
        {".hdml", "text/x-hdml"},
        {".hhc", "application/x-oleobject"},
        {".hhk", "application/octet-stream"},
        {".hhp", "application/octet-stream"},
        {".hlp", "application/winhlp"},
        {".hpp", "text/plain"},
        {".hqx", "application/mac-binhex40"},
        {".hta", "application/hta"},
        {".htc", "text/x-component"},
        {".htm", "text/html"},
        {".html", "text/html"},
        {".htt", "text/webviewhtml"},
        {".hxa", "application/xml"},
        {".hxc", "application/xml"},
        {".hxd", "application/octet-stream"},
        {".hxe", "application/xml"},
        {".hxf", "application/xml"},
        {".hxh", "application/octet-stream"},
        {".hxi", "application/octet-stream"},
        {".hxk", "application/xml"},
        {".hxq", "application/octet-stream"},
        {".hxr", "application/octet-stream"},
        {".hxs", "application/octet-stream"},
        {".hxt", "text/html"},
        {".hxv", "application/xml"},
        {".hxw", "application/octet-stream"},
        {".hxx", "text/plain"},
        {".i", "text/plain"},
        {".ico", "image/x-icon"},
        {".ics", "application/octet-stream"},
        {".idl", "text/plain"},
        {".ief", "image/ief"},
        {".iii", "application/x-iphone"},
        {".inc", "text/plain"},
        {".inf", "application/octet-stream"},
        {".inl", "text/plain"},
        {".ins", "application/x-internet-signup"},
        {".ipa", "application/x-itunes-ipa"},
        {".ipg", "application/x-itunes-ipg"},
        {".ipproj", "text/plain"},
        {".ipsw", "application/x-itunes-ipsw"},
        {".iqy", "text/x-ms-iqy"},
        {".isp", "application/x-internet-signup"},
        {".ite", "application/x-itunes-ite"},
        {".itlp", "application/x-itunes-itlp"},
        {".itms", "application/x-itunes-itms"},
        {".itpc", "application/x-itunes-itpc"},
        {".IVF", "video/x-ivf"},
        {".jar", "application/java-archive"},
        {".java", "application/octet-stream"},
        {".jck", "application/liquidmotion"},
        {".jcz", "application/liquidmotion"},
        {".jfif", "image/pjpeg"},
        {".jnlp", "application/x-java-jnlp-file"},
        {".jpb", "application/octet-stream"},
        {".jpe", "image/jpeg"},
        {".jpeg", "image/jpeg"},
        {".jpg", "image/jpeg"},
        {".js", "application/x-javascript"},
        {".jsx", "text/jscript"},
        {".jsxbin", "text/plain"},
        {".latex", "application/x-latex"},
        {".library-ms", "application/windows-library+xml"},
        {".lit", "application/x-ms-reader"},
        {".loadtest", "application/xml"},
        {".lpk", "application/octet-stream"},
        {".lsf", "video/x-la-asf"},
        {".lst", "text/plain"},
        {".lsx", "video/x-la-asf"},
        {".lzh", "application/octet-stream"},
        {".m13", "application/x-msmediaview"},
        {".m14", "application/x-msmediaview"},
        {".m1v", "video/mpeg"},
        {".m2t", "video/vnd.dlna.mpeg-tts"},
        {".m2ts", "video/vnd.dlna.mpeg-tts"},
        {".m2v", "video/mpeg"},
        {".m3u", "audio/x-mpegurl"},
        {".m3u8", "audio/x-mpegurl"},
        {".m4a", "audio/m4a"},
        {".m4b", "audio/m4b"},
        {".m4p", "audio/m4p"},
        {".m4r", "audio/x-m4r"},
        {".m4v", "video/x-m4v"},
        {".mac", "image/x-macpaint"},
        {".mak", "text/plain"},
        {".man", "application/x-troff-man"},
        {".manifest", "application/x-ms-manifest"},
        {".map", "text/plain"},
        {".master", "application/xml"},
        {".mda", "application/msaccess"},
        {".mdb", "application/x-msaccess"},
        {".mde", "application/msaccess"},
        {".mdp", "application/octet-stream"},
        {".me", "application/x-troff-me"},
        {".mfp", "application/x-shockwave-flash"},
        {".mht", "message/rfc822"},
        {".mhtml", "message/rfc822"},
        {".mid", "audio/mid"},
        {".midi", "audio/mid"},
        {".mix", "application/octet-stream"},
        {".mk", "text/plain"},
        {".mmf", "application/x-smaf"},
        {".mno", "text/xml"},
        {".mny", "application/x-msmoney"},
        {".mod", "video/mpeg"},
        {".mov", "video/quicktime"},
        {".movie", "video/x-sgi-movie"},
        {".mp2", "video/mpeg"},
        {".mp2v", "video/mpeg"},
        {".mp3", "audio/mpeg"},
        {".mp4", "video/mp4"},
        {".mp4v", "video/mp4"},
        {".mpa", "video/mpeg"},
        {".mpe", "video/mpeg"},
        {".mpeg", "video/mpeg"},
        {".mpf", "application/vnd.ms-mediapackage"},
        {".mpg", "video/mpeg"},
        {".mpp", "application/vnd.ms-project"},
        {".mpv2", "video/mpeg"},
        {".mqv", "video/quicktime"},
        {".ms", "application/x-troff-ms"},
        {".msi", "application/octet-stream"},
        {".mso", "application/octet-stream"},
        {".mts", "video/vnd.dlna.mpeg-tts"},
        {".mtx", "application/xml"},
        {".mvb", "application/x-msmediaview"},
        {".mvc", "application/x-miva-compiled"},
        {".mxp", "application/x-mmxp"},
        {".nc", "application/x-netcdf"},
        {".nsc", "video/x-ms-asf"},
        {".nws", "message/rfc822"},
        {".ocx", "application/octet-stream"},
        {".oda", "application/oda"},
        {".odc", "text/x-ms-odc"},
        {".odh", "text/plain"},
        {".odl", "text/plain"},
        {".odp", "application/vnd.oasis.opendocument.presentation"},
        {".ods", "application/oleobject"},
        {".odt", "application/vnd.oasis.opendocument.text"},
        {".one", "application/onenote"},
        {".onea", "application/onenote"},
        {".onepkg", "application/onenote"},
        {".onetmp", "application/onenote"},
        {".onetoc", "application/onenote"},
        {".onetoc2", "application/onenote"},
        {".orderedtest", "application/xml"},
        {".osdx", "application/opensearchdescription+xml"},
        {".p10", "application/pkcs10"},
        {".p12", "application/x-pkcs12"},
        {".p7b", "application/x-pkcs7-certificates"},
        {".p7c", "application/pkcs7-mime"},
        {".p7m", "application/pkcs7-mime"},
        {".p7r", "application/x-pkcs7-certreqresp"},
        {".p7s", "application/pkcs7-signature"},
        {".pbm", "image/x-portable-bitmap"},
        {".pcast", "application/x-podcast"},
        {".pct", "image/pict"},
        {".pcx", "application/octet-stream"},
        {".pcz", "application/octet-stream"},
        {".pdf", "application/pdf"},
        {".pfb", "application/octet-stream"},
        {".pfm", "application/octet-stream"},
        {".pfx", "application/x-pkcs12"},
        {".pgm", "image/x-portable-graymap"},
        {".pic", "image/pict"},
        {".pict", "image/pict"},
        {".pkgdef", "text/plain"},
        {".pkgundef", "text/plain"},
        {".pko", "application/vnd.ms-pki.pko"},
        {".pls", "audio/scpls"},
        {".pma", "application/x-perfmon"},
        {".pmc", "application/x-perfmon"},
        {".pml", "application/x-perfmon"},
        {".pmr", "application/x-perfmon"},
        {".pmw", "application/x-perfmon"},
        {".png", "image/png"},
        {".pnm", "image/x-portable-anymap"},
        {".pnt", "image/x-macpaint"},
        {".pntg", "image/x-macpaint"},
        {".pnz", "image/png"},
        {".pot", "application/vnd.ms-powerpoint"},
        {".potm", "application/vnd.ms-powerpoint.template.macroEnabled.12"},
        {".potx", "application/vnd.openxmlformats-officedocument.presentationml.template"},
        {".ppa", "application/vnd.ms-powerpoint"},
        {".ppam", "application/vnd.ms-powerpoint.addin.macroEnabled.12"},
        {".ppm", "image/x-portable-pixmap"},
        {".pps", "application/vnd.ms-powerpoint"},
        {".ppsm", "application/vnd.ms-powerpoint.slideshow.macroEnabled.12"},
        {".ppsx", "application/vnd.openxmlformats-officedocument.presentationml.slideshow"},
        {".ppt", "application/vnd.ms-powerpoint"},
        {".pptm", "application/vnd.ms-powerpoint.presentation.macroEnabled.12"},
        {".pptx", "application/vnd.openxmlformats-officedocument.presentationml.presentation"},
        {".prf", "application/pics-rules"},
        {".prm", "application/octet-stream"},
        {".prx", "application/octet-stream"},
        {".ps", "application/postscript"},
        {".psc1", "application/PowerShell"},
        {".psd", "application/octet-stream"},
        {".psess", "application/xml"},
        {".psm", "application/octet-stream"},
        {".psp", "application/octet-stream"},
        {".pub", "application/x-mspublisher"},
        {".pwz", "application/vnd.ms-powerpoint"},
        {".qht", "text/x-html-insertion"},
        {".qhtm", "text/x-html-insertion"},
        {".qt", "video/quicktime"},
        {".qti", "image/x-quicktime"},
        {".qtif", "image/x-quicktime"},
        {".qtl", "application/x-quicktimeplayer"},
        {".qxd", "application/octet-stream"},
        {".ra", "audio/x-pn-realaudio"},
        {".ram", "audio/x-pn-realaudio"},
        {".rar", "application/octet-stream"},
        {".ras", "image/x-cmu-raster"},
        {".rat", "application/rat-file"},
        {".rc", "text/plain"},
        {".rc2", "text/plain"},
        {".rct", "text/plain"},
        {".rdlc", "application/xml"},
        {".resx", "application/xml"},
        {".rf", "image/vnd.rn-realflash"},
        {".rgb", "image/x-rgb"},
        {".rgs", "text/plain"},
        {".rm", "application/vnd.rn-realmedia"},
        {".rmi", "audio/mid"},
        {".rmp", "application/vnd.rn-rn_music_package"},
        {".roff", "application/x-troff"},
        {".rpm", "audio/x-pn-realaudio-plugin"},
        {".rqy", "text/x-ms-rqy"},
        {".rtf", "application/rtf"},
        {".rtx", "text/richtext"},
        {".ruleset", "application/xml"},
        {".s", "text/plain"},
        {".safariextz", "application/x-safari-safariextz"},
        {".scd", "application/x-msschedule"},
        {".sct", "text/scriptlet"},
        {".sd2", "audio/x-sd2"},
        {".sdp", "application/sdp"},
        {".sea", "application/octet-stream"},
        {".searchConnector-ms", "application/windows-search-connector+xml"},
        {".setpay", "application/set-payment-initiation"},
        {".setreg", "application/set-registration-initiation"},
        {".settings", "application/xml"},
        {".sgimb", "application/x-sgimb"},
        {".sgml", "text/sgml"},
        {".sh", "application/x-sh"},
        {".shar", "application/x-shar"},
        {".shtml", "text/html"},
        {".sit", "application/x-stuffit"},
        {".sitemap", "application/xml"},
        {".skin", "application/xml"},
        {".sldm", "application/vnd.ms-powerpoint.slide.macroEnabled.12"},
        {".sldx", "application/vnd.openxmlformats-officedocument.presentationml.slide"},
        {".slk", "application/vnd.ms-excel"},
        {".sln", "text/plain"},
        {".slupkg-ms", "application/x-ms-license"},
        {".smd", "audio/x-smd"},
        {".smi", "application/octet-stream"},
        {".smx", "audio/x-smd"},
        {".smz", "audio/x-smd"},
        {".snd", "audio/basic"},
        {".snippet", "application/xml"},
        {".snp", "application/octet-stream"},
        {".sol", "text/plain"},
        {".sor", "text/plain"},
        {".spc", "application/x-pkcs7-certificates"},
        {".spl", "application/futuresplash"},
        {".src", "application/x-wais-source"},
        {".srf", "text/plain"},
        {".SSISDeploymentManifest", "text/xml"},
        {".ssm", "application/streamingmedia"},
        {".sst", "application/vnd.ms-pki.certstore"},
        {".stl", "application/vnd.ms-pki.stl"},
        {".sv4cpio", "application/x-sv4cpio"},
        {".sv4crc", "application/x-sv4crc"},
        {".svc", "application/xml"},
        {".swf", "application/x-shockwave-flash"},
        {".t", "application/x-troff"},
        {".tar", "application/x-tar"},
        {".tcl", "application/x-tcl"},
        {".testrunconfig", "application/xml"},
        {".testsettings", "application/xml"},
        {".tex", "application/x-tex"},
        {".texi", "application/x-texinfo"},
        {".texinfo", "application/x-texinfo"},
        {".tgz", "application/x-compressed"},
        {".thmx", "application/vnd.ms-officetheme"},
        {".thn", "application/octet-stream"},
        {".tif", "image/tiff"},
        {".tiff", "image/tiff"},
        {".tlh", "text/plain"},
        {".tli", "text/plain"},
        {".toc", "application/octet-stream"},
        {".tr", "application/x-troff"},
        {".trm", "application/x-msterminal"},
        {".trx", "application/xml"},
        {".ts", "video/vnd.dlna.mpeg-tts"},
        {".tsv", "text/tab-separated-values"},
        {".ttf", "application/octet-stream"},
        {".tts", "video/vnd.dlna.mpeg-tts"},
        {".txt", "text/plain"},
        {".u32", "application/octet-stream"},
        {".uls", "text/iuls"},
        {".user", "text/plain"},
        {".ustar", "application/x-ustar"},
        {".vb", "text/plain"},
        {".vbdproj", "text/plain"},
        {".vbk", "video/mpeg"},
        {".vbproj", "text/plain"},
        {".vbs", "text/vbscript"},
        {".vcf", "text/x-vcard"},
        {".vcproj", "Application/xml"},
        {".vcs", "text/plain"},
        {".vcxproj", "Application/xml"},
        {".vddproj", "text/plain"},
        {".vdp", "text/plain"},
        {".vdproj", "text/plain"},
        {".vdx", "application/vnd.ms-visio.viewer"},
        {".vml", "text/xml"},
        {".vscontent", "application/xml"},
        {".vsct", "text/xml"},
        {".vsd", "application/vnd.visio"},
        {".vsi", "application/ms-vsi"},
        {".vsix", "application/vsix"},
        {".vsixlangpack", "text/xml"},
        {".vsixmanifest", "text/xml"},
        {".vsmdi", "application/xml"},
        {".vspscc", "text/plain"},
        {".vss", "application/vnd.visio"},
        {".vsscc", "text/plain"},
        {".vssettings", "text/xml"},
        {".vssscc", "text/plain"},
        {".vst", "application/vnd.visio"},
        {".vstemplate", "text/xml"},
        {".vsto", "application/x-ms-vsto"},
        {".vsw", "application/vnd.visio"},
        {".vsx", "application/vnd.visio"},
        {".vtx", "application/vnd.visio"},
        {".wav", "audio/wav"},
        {".wave", "audio/wav"},
        {".wax", "audio/x-ms-wax"},
        {".wbk", "application/msword"},
        {".wbmp", "image/vnd.wap.wbmp"},
        {".wcm", "application/vnd.ms-works"},
        {".wdb", "application/vnd.ms-works"},
        {".wdp", "image/vnd.ms-photo"},
        {".webarchive", "application/x-safari-webarchive"},
        {".webtest", "application/xml"},
        {".wiq", "application/xml"},
        {".wiz", "application/msword"},
        {".wks", "application/vnd.ms-works"},
        {".WLMP", "application/wlmoviemaker"},
        {".wlpginstall", "application/x-wlpg-detect"},
        {".wlpginstall3", "application/x-wlpg3-detect"},
        {".wm", "video/x-ms-wm"},
        {".wma", "audio/x-ms-wma"},
        {".wmd", "application/x-ms-wmd"},
        {".wmf", "application/x-msmetafile"},
        {".wml", "text/vnd.wap.wml"},
        {".wmlc", "application/vnd.wap.wmlc"},
        {".wmls", "text/vnd.wap.wmlscript"},
        {".wmlsc", "application/vnd.wap.wmlscriptc"},
        {".wmp", "video/x-ms-wmp"},
        {".wmv", "video/x-ms-wmv"},
        {".wmx", "video/x-ms-wmx"},
        {".wmz", "application/x-ms-wmz"},
        {".wpl", "application/vnd.ms-wpl"},
        {".wps", "application/vnd.ms-works"},
        {".wri", "application/x-mswrite"},
        {".wrl", "x-world/x-vrml"},
        {".wrz", "x-world/x-vrml"},
        {".wsc", "text/scriptlet"},
        {".wsdl", "text/xml"},
        {".wvx", "video/x-ms-wvx"},
        {".x", "application/directx"},
        {".xaf", "x-world/x-vrml"},
        {".xaml", "application/xaml+xml"},
        {".xap", "application/x-silverlight-app"},
        {".xbap", "application/x-ms-xbap"},
        {".xbm", "image/x-xbitmap"},
        {".xdr", "text/plain"},
        {".xht", "application/xhtml+xml"},
        {".xhtml", "application/xhtml+xml"},
        {".xla", "application/vnd.ms-excel"},
        {".xlam", "application/vnd.ms-excel.addin.macroEnabled.12"},
        {".xlc", "application/vnd.ms-excel"},
        {".xld", "application/vnd.ms-excel"},
        {".xlk", "application/vnd.ms-excel"},
        {".xll", "application/vnd.ms-excel"},
        {".xlm", "application/vnd.ms-excel"},
        {".xls", "application/vnd.ms-excel"},
        {".xlsb", "application/vnd.ms-excel.sheet.binary.macroEnabled.12"},
        {".xlsm", "application/vnd.ms-excel.sheet.macroEnabled.12"},
        {".xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"},
        {".xlt", "application/vnd.ms-excel"},
        {".xltm", "application/vnd.ms-excel.template.macroEnabled.12"},
        {".xltx", "application/vnd.openxmlformats-officedocument.spreadsheetml.template"},
        {".xlw", "application/vnd.ms-excel"},
        {".xml", "text/xml"},
        {".xmta", "application/xml"},
        {".xof", "x-world/x-vrml"},
        {".XOML", "text/plain"},
        {".xpm", "image/x-xpixmap"},
        {".xps", "application/vnd.ms-xpsdocument"},
        {".xrm-ms", "text/xml"},
        {".xsc", "application/xml"},
        {".xsd", "text/xml"},
        {".xsf", "text/xml"},
        {".xsl", "text/xml"},
        {".xslt", "text/xml"},
        {".xsn", "application/octet-stream"},
        {".xss", "application/xml"},
        {".xtp", "application/octet-stream"},
        {".xwd", "image/x-xwindowdump"},
        {".z", "application/x-compress"},
        {".zip", "application/x-zip-compressed"},
        #endregion

        };

            public static string GetMimeType(string extension)
            {
                if (extension == null)
                {
                    throw new ArgumentNullException("extension");
                }

                if (!extension.StartsWith("."))
                {
                    extension = "." + extension;
                }



                string mime;
                if (extension.Equals(".mp3"))

                {
                    mime = "audio/mpeg";
                    return mime;
                }


                return _mappings.TryGetValue(extension, out mime) ? mime : "application/octet-stream";

            }
        }

        public static class DateTimeHelpers
        {
            private static readonly DateTime epoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
            //public static string ToRelativeDate(DateTime dateTime)

            //{

            //    var timeSpan = DateTime.Now - dateTime;



            //    // span is less than or equal to 60 seconds, measure in seconds.

            //    if (timeSpan <= TimeSpan.FromSeconds(60))

            //    {

            //        return timeSpan.Seconds + " " + Resource.secondsago;

            //    }

            //    // span is less than or equal to 60 minutes, measure in minutes.

            //    if (timeSpan <= TimeSpan.FromMinutes(60))

            //    {

            //        return timeSpan.Minutes > 1

            //            ? timeSpan.Minutes + " " + Resource.Minutesago : Resource.Aboutaminuteago;

            //    }

            //    // span is less than or equal to 24 hours, measure in hours.

            //    if (timeSpan <= TimeSpan.FromHours(24))

            //    {

            //        return timeSpan.Hours > 1

            //            ? timeSpan.Hours + " " + Resource.hoursago : Resource.Aboutanhourago;

            //    }

            //    // span is less than or equal to 30 days (1 month), measure in days.

            //    if (timeSpan <= TimeSpan.FromDays(30))

            //    {

            //        return timeSpan.Days > 1

            //            ? timeSpan.Days + " " + Resource.daysago : Resource.Aboutadayago;

            //    }

            //    // span is less than or equal to 365 days (1 year), measure in months.

            //    if (timeSpan <= TimeSpan.FromDays(365))

            //    {

            //        return timeSpan.Days > 30

            //            ? timeSpan.Days / 30 + " " + Resource.Monthsago : Resource.Aboutamonthago;

            //    }

            //    // span is greater than 365 days (1 year), measure in years.

            //    return timeSpan.Days > 365

            //        ? timeSpan.Days / 365 + " " + Resource.Yearsago : Resource.Aboutayearago;

            //}

            public static DateTime EpochMilliSecToDateTime(double unixTimeStamp)
            {
                return new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc).AddMilliseconds(unixTimeStamp).ToLocalTime();
            }
            public static DateTime UnixTimeStampToDateTime(double unixTimeStamp)
            {
                // Unix timestamp is seconds past epoch
                DateTime dtDateTime = new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc);
                dtDateTime = dtDateTime.AddSeconds(unixTimeStamp).ToLocalTime();
                return dtDateTime;
            }

            public static long ToEpoch(DateTime datetime)
            {
                return Convert.ToInt64((datetime - epoch).TotalSeconds);
            }
        }

        public static string passwordEncryption(String password)
        {
            try
            {
                byte[] encData_byte = new byte[password.Length];
                encData_byte = System.Text.Encoding.UTF8.GetBytes(password);
                string encodedData = Convert.ToBase64String(encData_byte);
                return encodedData;
            }
            catch (Exception e)
            {
                throw new Exception("Error in base64Encode" + e.Message);
            }
        }
        public static string passwordDecription(string sData)
        {
            try
            {

                System.Text.UTF8Encoding encoder = new System.Text.UTF8Encoding();
                System.Text.Decoder utf8Decode = encoder.GetDecoder();
                byte[] todecode_byte = Convert.FromBase64String(sData);
                int charCount = utf8Decode.GetCharCount(todecode_byte, 0, todecode_byte.Length);
                char[] decoded_char = new char[charCount];
                utf8Decode.GetChars(todecode_byte, 0, todecode_byte.Length, decoded_char, 0);
                string result = new String(decoded_char);
                return result;
            }
            catch (Exception ex)
            {

                throw;
            }
        }
        //TODO: If needed, rename the old methods to use this new function in ResearchController in OnePage.WebAdmin project
        public static IEnumerable<T> CustomDistinctBy<T, TKey>(this IEnumerable<T> enumerable, Func<T, TKey> keySelector)
        {
            return enumerable.GroupBy(keySelector).Select(grp => grp.First());
        }
        public static string AddToStringList(string dest, string source)
        {
            string output = dest;
            if (!string.IsNullOrEmpty(dest) && !string.IsNullOrEmpty(source) && !dest.ToLower().Contains(source.ToLower()))
            {
                dest = dest + "," + source;
                output = dest;
            }
            else if (string.IsNullOrEmpty(dest) && !string.IsNullOrEmpty(source))
            {
                output = source;
            }
            return output;
        }

        public static string mixpanel()
        {
            try
            {

                using (WebClient wc = new WebClient())
                {
                    wc.Headers.Add("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.2; .NET CLR 1.0.3705;)");
                    byte[] creds = UTF8Encoding.UTF8.GetBytes("1d44f7a8e29de241eaed86366cf72a45");
                    wc.Headers.Add("Authorization", "Basic " + Convert.ToBase64String(creds));
                    var reqparm = new System.Collections.Specialized.NameValueCollection();
                    //reqparm.Add("script", "function main() { return People({\"user_selectors\":[{\"selector\":}]\"2018-07-01\",\"to_date\":\"2018-08-03\"}).reduce(mixpanel.reducer.count()); }");
                    // reqparm.Add("script", "function main(){  return People({user_selectors:[{selector:'has_prefix(string(user[\"Call Started\"]), \"1533028555\")'}]};");
                    reqparm.Add("script", "function main() { return Events({\"from_date\":\"2018-07-01\",\"to_date\":\"2018-08-03\"}).reduce(mixpanel.reducer.count()); }");
                    byte[] responsebytes = wc.UploadValues("https://mixpanel.com/api/2.0/jql", "POST", reqparm);
                    string result = System.Text.Encoding.UTF8.GetString(responsebytes);
                    return result;
                }
            }
            catch (Exception ex)
            {

                throw;
            }

        }

        public static string AddSpacesToSentence(string text)
        {
            if (string.IsNullOrWhiteSpace(text))
                return "";
            StringBuilder newText = new StringBuilder(text.Length * 2);
            newText.Append(text[0]);
            for (int i = 1; i < text.Length; i++)
            {
                if (char.IsUpper(text[i]) && text[i - 1] != ' ')
                    newText.Append(' ');
                newText.Append(text[i]);
            }
            return newText.ToString();
        }

        public static string UppercaseWords(string value)
        {
            char[] array = value.ToCharArray();
            if (array.Length >= 1)
            {
                if (char.IsLower(array[0]))
                {
                    array[0] = char.ToUpper(array[0]);
                }
            }
            for (int i = 1; i < array.Length; i++)
            {
                if (array[i - 1] == ' ')
                {
                    if (char.IsLower(array[i]))
                    {
                        array[i] = char.ToUpper(array[i]);
                    }
                }
            }
            return new string(array);
        }
        public static List<int> GetPipedriveShowOrderForBulkUpdate()
        {
            return new List<int>
            {
                3010,
                2003,
                3030,
                3050// Pipedrive contact
            };
        }

        public static List<int> GetHubSpotShowOrderForBulkUpdate()
        {
            return new List<int>
            {
                3010, // HubSpot contact
                3030  // HubSpot account
            };
        }

        public static List<int> GetDynamicsShowOrdersForBulkUpdate()
        {
            return new List<int>
            {
                3010, // Dynamics contacts
                3020, // Dynamics leads
                3040, // Dynamics opportunities
                3030  // Dynamics account
            };
        }
        public enum ErrorCodes
        {
            [Description("Invalid coupon")]
            E0001 = 1,

            [Description("Already redeemed the coupon")]
            E0002 = 2,
            [Description("coupon id is empty")]
            E0003 = 3,
            [Description("free domains are not allowed")]
            E0004 =4,
            [Description("Linkedin is Already Present")]
            E0005 = 5,
            [Description("permission denied")]
            E0006 = 6,
            [Description("Wrong Payload")]
            E0007=7,
            [Description("UnAuthorized")]
            E0008 = 8,
            [Description("NoEvents")]
            E0009 = 9,
            [Description("NoFutureEvents")]
            E00010 = 10,
            [Description("NoPastEvents")]

            E00011 =11
        }
    
        public enum DreamCast_ErrorCodes
        {
            
            [Description("No Credits")]
            E0001 =1,

            [Description("Exception Error")]
            E0002 =2,

            [Description("UnAuthorized Error")]
            E0003 = 3,

            [Description("Invalid Payload")]
            E0004 =4,

            [Description("Data not been Fetched")]
            E0005 = 5,

            [Description("Contact Id is null")]
            E0006 = 6,

            [Description("Contact Doesnt Exsist")]
            E0007 = 7,

           
        }
    }
}
