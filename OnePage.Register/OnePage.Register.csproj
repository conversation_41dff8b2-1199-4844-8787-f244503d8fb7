﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ApplicationInsightsResourceId>/subscriptions/cdfb8f56-aee6-46ed-8f5b-039909091a9a/resourceGroups/1Page/providers/microsoft.insights/components/eu-fc-ai-lr</ApplicationInsightsResourceId>
    <UserSecretsId>3b7d9458-71c3-4669-99e6-a17b5b63f3e1</UserSecretsId>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="Models\**" />
    <Content Remove="Models\**" />
    <EmbeddedResource Remove="Models\**" />
    <None Remove="Models\**" />
  </ItemGroup>
  <ItemGroup>
    <Compile Remove="Membership\Membership.cs" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="AWSSDK.Extensions.NETCore.Setup" Version="3.7.301" />
    <PackageReference Include="AWSSDK.SQS" Version="3.7.301.24" />
    <PackageReference Include="chargebee" Version="2.10.0" />
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.18.0" />
    <PackageReference Include="Stripe.net" Version="45.11.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.1.5" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\OnePage.Common\OnePage.Common.csproj" />
    <ProjectReference Include="..\OnePage.Models\OnePage.Models.csproj" />
    <ProjectReference Include="..\OnePage.Services\OnePage.Services.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Membership\" />
  </ItemGroup>
</Project>