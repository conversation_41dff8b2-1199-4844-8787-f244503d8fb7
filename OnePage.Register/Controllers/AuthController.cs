//using ChargeBee.Api;
//using ChargeBee.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
//using Microsoft.AspNetCore.Mvc;
using OnePage.Common;
using OnePage.Models.Models;
using OnePage.Models.Models.AdminModel;
using OnePage.Models.Models.DataModel;
using OnePage.Models.Models.OrgModel;
using OnePage.Models.Models.PeopleModel;
using OnePage.Models.Models.TokenModel;
using OnePage.Services;
using OnePage.Register.Interfaces;
using OnePage.Register.Services;
using RestSharp;
using System;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static OnePage.Common.CommonData;
using static OnePage.Models.Models.PropertyModels.AuthModels;
using static OnePage.Models.Models.PropertyModels.ContactDataModel;
using static OnePage.Models.Models.PropertyModels.DataModels;
using System.Net.Http;
using OnePage.Models;
using System.Text;
using Stripe;
using System.Net;
using Microsoft.EntityFrameworkCore;

using OnePage.Services.Interfaces;

using Google.Apis.Drive.v3.Data;
using User = OnePage.Models.Models.PeopleModel.User;
using System.IO;
using Stripe.Checkout;
using Plan = OnePage.Models.Models.Plan;

namespace OnePage.Register.Controllers
{
    public class AuthController : Controller
    {
        private readonly PeopleContext wepdb;
        private readonly OrgContext weodb;
        private readonly AdminContext weadb;
        private readonly DataContext weddb;
        private readonly ContactContext db;
        //private readonly OrgContext weodb;
        private readonly EventRDSContext weedb;
        private readonly BotService botService;

        private readonly OnePage.Models.Models.TokenModel.TokenContext wetdb;

        MetricsService metricsService = new();
        private readonly ProviderService providerService;

        private readonly ITelemetryTracker telemetryTracker;
        private readonly IRedisCaching _redisCaching;
        private readonly IConfiguration _configuration;
        private readonly UserProviderCaching _userProviderCaching;
        private readonly AuthService _authService;
        public AuthController(ITelemetryTracker tracker, IRedisCaching redisCaching, BotService botService, ProviderService providerService, PeopleContext wepdb, DataContext weddb, AdminContext weadb, TokenContext wetdb, OrgContext weodb, EventRDSContext weedb, ContactContext db, UserProviderCaching userProviderCaching, AuthService authService, IConfiguration configuration
            )

        {
            telemetryTracker = tracker;
            // providerService = new(redisCaching);
            _redisCaching = redisCaching;
            this.wepdb = wepdb;
            this.weddb = weddb;
            this.weadb = weadb;
            this.wetdb = wetdb;
            this.weodb = weodb;
            this.db = db;
            _userProviderCaching = userProviderCaching;
            _authService = authService;
            this.weedb = weedb;
            this.providerService = providerService;
            this.botService = botService;
            _configuration = configuration;
            try
            {
                if (CommonData.languageList.Count == 0)
                    CommonData.languageList = weddb.Languages.ToList();
            }
            catch (Exception ex)
            {
                // Log the error but don't fail the controller initialization
                Console.WriteLine($"Failed to load languages in AuthController: {ex.Message}");
                // Initialize empty list to prevent null reference exceptions
                if (CommonData.languageList == null)
                    CommonData.languageList = new List<OnePage.Models.Models.DataModel.Language>();
            }
        }
        [HttpGet] // dont delete
        [Route("statusCheck")]
        public IActionResult AWSElasticBeanstalkStatusCheck()
        {
            try
            {
                return Ok(new {
                    status = "healthy",
                    timestamp = DateTime.UtcNow,
                    environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown",
                    version = "1.0.1",
                    endpoint = "statusCheck"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new {
                    status = "unhealthy",
                    error = ex.Message,
                    timestamp = DateTime.UtcNow,
                    endpoint = "statusCheck"
                });
            }
        }
        [AllowAnonymous]
        [Route("A4F6011E")]
        [HttpPost]
        public async Task<IActionResult> PreloginUsingEmail([FromBody] AuthUserModel2 userModel)
        {
            TelegramService.SendMessageToTestBot2("api call: A4F6011E");
            DateTime apiStart = DateTime.UtcNow;
            Dictionary<string, string> dictionary = new Dictionary<string, string>();
            dictionary.Add("startTime", apiStart.ToString());
            try
            {
                var appid = (userModel.AppId == Guid.Empty) ? CommonData.GlobalAppId : userModel.AppId;
                var tempPassword = "123456";
                var email = userModel.EmailAddress.ToLower().TrimStart().TrimEnd();
                User user = wepdb.Users.FirstOrDefault(w => w.Email == email && w.IsUser && w.AppId == userModel.AppId);
                var appInfo = weadb.Apps.FirstOrDefault(w => w.Id == userModel.AppId && w.IsActive);
                var androidOneSignalId = appInfo.AndroidOneSignalId;
                var androidOneSignalKey = appInfo.AndroidOneSignalApiKey;
                var IosOneSignalId = appInfo.IosoneSignalId;
                var IosOneSignalKey = appInfo.IosoneSignalApiKey;
                var WebOneSignalId = appInfo.WebOneSignalId;
                var WebOneSignalKey = appInfo.WebOneSignalApiKey;
                var supportEmail = appInfo.SupportEmail;
                var supportPhone = (userModel.AppId != CommonData.GlobalAppId) ? "" : (appInfo.SupportPhone ?? "");
                var supportName = appInfo.SupportName;
                var authUrl = weadb.Servers.FirstOrDefault(w => w.ServerType == 17 && w.AppId.ToString().ToLower() == userModel.AppId.ToString().ToLower() && w.IsActive).Url;
                PreloginReturnModel2 preloginReturnModel = new PreloginReturnModel2();
                var domainName = email.Split('@');
                string deviceType = Common.CommonData.GetDeviceType(userModel.DeviceType);
                string loginFlow = CommonData.GetLoginFlow(userModel.LoginFlowId);
                bool istestuser = CommonData.testUserDomains.Contains(domainName[1]);
                if (null != user)
                {
                    // timezone setting id
                    await SetUserSettings(user.Id, CommonData.TimeZoneSettingId, userModel.TimeZone);
                    //  await checkAndApplyForCoupon(user);
                    //   if (user.IsTestUser == false && !userModel.EmailAddress.ToLower().Contains("onmicrosoft.com"))
                    if (user.IsTestUser == false)
                    {
                        if (userModel.AppId == CommonData.OPMaukaAppId)
                        {
                            botService.SendMessageForNewOppExistingUsers(userModel.EmailAddress, userModel.FirstName + " " + userModel.LastName, deviceType, loginFlow);
                        }
                        else
                        {
                            botService.SendMessageForExistingUsers(userModel.EmailAddress, userModel.FirstName + " " + userModel.LastName, deviceType, loginFlow);
                        }
                        // TelegramService.SendMessage("Existing user trying to login: " + userModel.EmailAddress + "\r\n" + "Device Type: " + deviceType + "\r\n" + "Login: " + loginFlow);
                    }
                    dictionary.Add("ai", user.AnalyticsId.ToString());
                    var appLanguage = weadb.AppLanguages.FirstOrDefault(w => w.AppId == user.AppId && w.LanguageId == user.LanguageId);
                    // AppLanguageTemplate appSendgridTemplateId = new AppLanguageTemplate();
                    // if (appLanguage == null)
                    // {
                    //     appLanguage = weadb.AppLanguages.FirstOrDefault(w => w.AppId == CommonData.GlobalAppId && w.LanguageId == 1);
                    //     appSendgridTemplateId = weadb.AppLanguageTemplates.FirstOrDefault(w => w.AppLanguageId == appLanguage.Id && w.TemplateName == "OtpEmail");
                    // }
                    // else
                    // {
                    //     appSendgridTemplateId = weadb.AppLanguageTemplates.FirstOrDefault(w => w.AppLanguageId == appLanguage.Id && w.TemplateName == "OtpEmail");
                    // }
                    var userDevices = wepdb.UserDevices.Where(w => w.UserId == user.Id && w.IsActive == true).ToList();
                    bool loginAllowed = (appInfo.IsOneDevicePerAccount == true) ? ((userDevices.Count > 0) ? false : true) : true;
                    bool IsEmailValid = false;
                    if (istestuser)
                    {
                        loginAllowed = true;
                        IsEmailValid = true;
                    }

                    if (user.IsDisabled == true)
                    {
                        preloginReturnModel.UserType = 2;
                        preloginReturnModel.AuthUrl = "";
                        preloginReturnModel.SupportEmail = supportEmail;
                        preloginReturnModel.SupportPhone = supportPhone;
                        preloginReturnModel.IsLoginAllowed = loginAllowed;
                        preloginReturnModel.IsEmailValid = IsEmailValid;
                        preloginReturnModel.NotificarLoginBlocked = false;
                        preloginReturnModel.LoginType = 0;
                        return Ok(preloginReturnModel);
                    }
                    if (string.IsNullOrEmpty(user.CountryId))
                    {
                        user.CountryId = userModel.CountryId ?? "IN";
                        wepdb.SaveChanges();
                    }
                    if (userModel.IsPreAuthenticated)
                    {
                        user.Password = userModel.Password + "|1";
                        wepdb.SaveChanges();

                        preloginReturnModel.UserType = 0;
                        preloginReturnModel.AuthUrl = authUrl;
                        preloginReturnModel.SupportEmail = supportEmail;
                        preloginReturnModel.SupportPhone = supportPhone;
                        preloginReturnModel.IsLoginAllowed = loginAllowed;
                        preloginReturnModel.IsEmailValid = true;
                        preloginReturnModel.NotificarLoginBlocked = false;
                        preloginReturnModel.IsPreAuthenticated = true;
                        preloginReturnModel.LoginType = 0;
                        return Ok(preloginReturnModel);
                    }
                    if (istestuser)
                    {
                        var encriptedPwd = CommonData.passwordEncryption(tempPassword);
                        user.Password = encriptedPwd;
                        wepdb.SaveChanges();
                    }
                    else
                    {
                        if (userDevices.Count() > 0)
                        {
                            var random = new Random();
                            var number = random.Next(100000, 999999);
                            var encriptedPwd = CommonData.passwordEncryption(number.ToString());
                            var userDeviceTypeId = wepdb.UserDevices.Where(w => w.UserId == user.Id);
                            var msg1 = "Your login code: " + number;
                            var msg2 = "This code can be used to log in to your " + appInfo.Name + " account.";
                            user.Password = encriptedPwd;
                            await Task.Run(() => SendPush(WebOneSignalId, WebOneSignalKey, androidOneSignalId, androidOneSignalKey, IosOneSignalId, IosOneSignalKey, user, msg1, msg2));

                            var res = EmailServices.SendGridOTPMail(true, "", number.ToString(), userModel.EmailAddress, userModel.EmailAddress);
                            user.ModifiedDate = DateTime.UtcNow;
                            wepdb.SaveChanges();
                        }
                        else
                        {
                            var random = new Random();
                            var number = random.Next(100000, 999999);
                            var encriptedPwd = CommonData.passwordEncryption(number.ToString());
                            tempPassword = encriptedPwd;
                            var res = EmailServices.SendGridOTPMail(true, "", number.ToString(), userModel.EmailAddress, userModel.EmailAddress);
                            user.Password = encriptedPwd;
                            user.ModifiedDate = DateTime.UtcNow;
                            wepdb.SaveChanges();
                        }
                    }
                    preloginReturnModel.UserType = 0;
                    preloginReturnModel.AuthUrl = authUrl;
                    preloginReturnModel.SupportEmail = supportEmail;
                    preloginReturnModel.SupportPhone = supportPhone;
                    preloginReturnModel.IsLoginAllowed = loginAllowed;
                    preloginReturnModel.NotificarLoginBlocked = false;
                    preloginReturnModel.LoginType = 0;
                    return Ok(preloginReturnModel); // Existing User

                }
                else
                {
                    //  if (!userModel.EmailAddress.ToLower().Contains("onmicrosoft.com"))
                    if (userModel.AppId == CommonData.OPMaukaAppId)
                    {
                        botService.SendMessageForNewOppNewUsers(userModel.EmailAddress, userModel.FirstName + " " + userModel.LastName, deviceType, loginFlow);
                    }
                    else
                    {
                        botService.SendMessageForNewUsers(userModel.EmailAddress, userModel.FirstName + " " + userModel.LastName, deviceType, loginFlow);
                    }
                    if (userModel.AppId != Common.CommonData.OPAppId && userModel.AppId != Common.CommonData.OPV2AppId && userModel.AppId != OPMaukaAppId && userModel.AppId != OPEnterpriseAppId && userModel.AppId != CommonData.GlobalAppId)
                    {
                        preloginReturnModel.UserType = 4;
                        preloginReturnModel.AuthUrl = "";
                        preloginReturnModel.SupportEmail = supportEmail;
                        preloginReturnModel.SupportPhone = supportPhone;
                        preloginReturnModel.IsLoginAllowed = false;
                        preloginReturnModel.IsEmailValid = true;
                        preloginReturnModel.NotificarLoginBlocked = true;
                        preloginReturnModel.LoginType = 3;
                        return Ok(preloginReturnModel);
                    }
                    if (userModel.IsPreAuthenticated)
                    {
                        Guid userId = Guid.NewGuid();
                        user = new User()
                        {
                            // SanitizedNumber = santizedPhoneNumber,
                            // PType = Int32.Parse(),
                            Id = userId,
                            Password = userModel.Password + "|1",
                            CountryId = userModel.CountryId ?? "IN",
                            //ProvidedNumber = userModel.PhoneNumber,
                            LastLogin = DateTime.UtcNow,
                            PreviousLogin = DateTime.UtcNow,
                            FirstLogin = DateTime.UtcNow,
                            CreatedDate = DateTime.UtcNow,
                            ModifiedDate = DateTime.UtcNow,
                            FirstName = userModel.FirstName,
                            LastName = userModel.LastName,
                            IsDisabled = false,
                            IsUser = true,
                            IsActive = true,
                            Notes = "",
                            AppId = userModel.AppId,
                            LanguageId = 1,
                            Email = email,
                            IsEmailValidated = false,
                            //IsPhoneValidated = false,
                            AnalyticsId = Guid.NewGuid(),
                            IsTestUser = istestuser
                        };
                        wepdb.Users.Add(user);
                        wepdb.SaveChanges();


                        await SetUserSettings(user.Id, CommonData.TimeZoneSettingId, userModel.TimeZone);
                        var couponData = wepdb.Coupons.FirstOrDefault(w => w.UserId.ToString().StartsWith(userModel.Coupon) || w.Id.ToString().Contains(userModel.Coupon) && w.IsCancelled == false);
                        await checkAndApplyForCoupon(user, couponData.Id);
                        dictionary.Add("ai", user.AnalyticsId.ToString());
                        AuthUserModel3 userModel3 = new AuthUserModel3
                        {
                            AppId = userModel.AppId,
                            CountryId = userModel.CountryId,
                            Coupon = userModel.Coupon,
                            EmailAddress = userModel.EmailAddress,
                            PhoneNumber = userModel.PhoneNumber
                        };

                        AddAdditionalTableDataUsingEmail(userModel3, email, userId, supportEmail, supportPhone, supportName);
                        preloginReturnModel.UserType = 1;
                        preloginReturnModel.AuthUrl = authUrl;
                        preloginReturnModel.SupportEmail = supportEmail;
                        preloginReturnModel.SupportPhone = supportPhone;
                        preloginReturnModel.IsLoginAllowed = true;
                        preloginReturnModel.NotificarLoginBlocked = true;
                        preloginReturnModel.IsEmailValid = true;
                        preloginReturnModel.LoginType = 0;
                        return Ok(preloginReturnModel); // New User 
                    }
                    else
                    {
                        bool emailWhiteListExist = wepdb.EmailWhiteLists.Any(w => w.Email.ToLower() == userModel.EmailAddress.ToLower() && w.IsActive == true);
                        var domain = userModel.EmailAddress.Split('@');
                        if (!emailWhiteListExist && (domain.Count() > 0) && (wepdb.DomainBlacklists.Select(W => W.DomainName.ToLower()).Contains(domain[1].ToLower())))
                        {
                            preloginReturnModel.UserType = 3;
                            preloginReturnModel.AuthUrl = "";
                            preloginReturnModel.SupportEmail = supportEmail;
                            preloginReturnModel.SupportPhone = supportPhone;
                            preloginReturnModel.IsLoginAllowed = false;
                            preloginReturnModel.IsEmailValid = true;
                            preloginReturnModel.NotificarLoginBlocked = true;
                            preloginReturnModel.LoginType = 4;
                            return Ok(preloginReturnModel);
                        }
                        var isValidEmail = await _authService.VerifyEmailByKickBox(userModel.EmailAddress);
                        //  var isValidEmail = await IsValidEmail(userModel.EmailAddress);
                        if (isValidEmail == true || emailWhiteListExist == true || istestuser == true)
                        {
                            if (!(istestuser || emailWhiteListExist))
                            {
                                if (!wepdb.EmailWhiteLists.Any(w => w.Email == userModel.EmailAddress.ToLower()))
                                {
                                    EmailWhiteList emailWhite = new EmailWhiteList
                                    {
                                        Email = userModel.EmailAddress.ToLower(),
                                        IsActive = false,
                                        Id = Guid.NewGuid()
                                    };
                                    wepdb.EmailWhiteLists.Add(emailWhite);
                                    wepdb.SaveChanges();
                                }

                                preloginReturnModel.UserType = 2;
                                preloginReturnModel.AuthUrl = authUrl;
                                preloginReturnModel.SupportEmail = supportEmail;
                                preloginReturnModel.SupportPhone = supportPhone;
                                preloginReturnModel.IsLoginAllowed = false;
                                preloginReturnModel.IsEmailValid = istestuser ? true : false;
                                preloginReturnModel.NotificarLoginBlocked = true;
                                preloginReturnModel.LoginType = 0;
                                return Ok(preloginReturnModel);
                                //TODO: send email to admin
                            }

                            if (userModel.AppId == CommonData.MiofertaAppId || userModel.AppId == CommonData.NotificarAWSTestApp)
                            {
                                preloginReturnModel.UserType = 1;
                                preloginReturnModel.AuthUrl = "";
                                preloginReturnModel.SupportEmail = supportEmail;
                                preloginReturnModel.SupportPhone = supportPhone;
                                preloginReturnModel.IsLoginAllowed = true;
                                preloginReturnModel.NotificarLoginBlocked = true;
                                preloginReturnModel.LoginType = 0;
                                return Ok(preloginReturnModel);
                            }
                            Guid userId = Guid.NewGuid();
                            if (istestuser)
                            {
                                var encriptedPwd = CommonData.passwordEncryption(tempPassword);
                                tempPassword = encriptedPwd;
                            }
                            else
                            {
                                var appLanguage = weadb.AppLanguages.FirstOrDefault(w => w.AppId == userModel.AppId && w.LanguageId == 1);
                                var appSendgridTemplateId = "";
                                if (appLanguage == null)
                                {
                                    appLanguage = weadb.AppLanguages.FirstOrDefault(w => w.AppId == CommonData.GlobalAppId && w.LanguageId == 1);
                                    appSendgridTemplateId = weadb.AppLanguageTemplates.FirstOrDefault(w => w.AppLanguageId == appLanguage.Id && w.TemplateName == "OtpEmail").TemplateId;
                                }
                                else
                                {
                                    appSendgridTemplateId = weadb.AppLanguageTemplates.FirstOrDefault(w => w.AppLanguageId == appLanguage.Id && w.TemplateName == "OtpEmail").TemplateId;
                                }
                                var random = new Random();
                                var otp = random.Next(100000, 999999);
                                var encriptedPwd = CommonData.passwordEncryption(otp.ToString());
                                tempPassword = encriptedPwd;
                                var smsMessage = string.Format("Your one time code {0} to complete phone number verification.You have agreed to the terms and conditions at " + appInfo.Tocurl, otp);

                                var res = EmailServices.SendGridOTPMail(true, "", otp.ToString(), userModel.EmailAddress, userModel.EmailAddress);
                            }
                            user = new User()
                            {
                                // SanitizedNumber = santizedPhoneNumber,
                                // PType = Int32.Parse(),
                                Id = userId,
                                Password = tempPassword,
                                CountryId = userModel.CountryId ?? "IN",
                                //ProvidedNumber = userModel.PhoneNumber,
                                LastLogin = DateTime.UtcNow,
                                PreviousLogin = DateTime.UtcNow,
                                FirstLogin = DateTime.UtcNow,
                                CreatedDate = DateTime.UtcNow,
                                ModifiedDate = DateTime.UtcNow,
                                IsDisabled = false,
                                IsUser = true,
                                IsActive = true,
                                Notes = "",
                                AppId = userModel.AppId,
                                LanguageId = 1,
                                Email = email,
                                IsEmailValidated = false,
                                //IsPhoneValidated = false,
                                AnalyticsId = Guid.NewGuid(),
                                IsTestUser = istestuser
                            };
                            if (!string.IsNullOrEmpty(userModel.Coupon))
                            {
                                Models.Models.PeopleModel.Coupon coupon = wepdb.Coupons.FirstOrDefault(w => w.UserId.ToString().StartsWith(userModel.Coupon) || w.Id.ToString().Contains(userModel.Coupon) && w.IsCancelled == false);
                                if (coupon != null)
                                {
                                    user.InvitedBy = coupon.UserId.ToString();
                                }
                                else
                                {
                                    return BadRequest("Wrong Referral Code");
                                }
                            }
                            wepdb.Users.Add(user);
                            wepdb.SaveChanges();


                            var couponData = wepdb.Coupons.FirstOrDefault(w => w.UserId.ToString().StartsWith(userModel.Coupon) || w.Id.ToString().Contains(userModel.Coupon) && w.IsCancelled == false);
                            await checkAndApplyForCoupon(user, couponData.Id);
                            dictionary.Add("ai", user.AnalyticsId.ToString());
                            AuthUserModel3 userModel3 = new AuthUserModel3
                            {
                                AppId = userModel.AppId,
                                CountryId = userModel.CountryId,
                                Coupon = userModel.Coupon,
                                EmailAddress = userModel.EmailAddress,
                                PhoneNumber = userModel.PhoneNumber
                            };

                            AddAdditionalTableDataUsingEmail(userModel3, email, userId, supportEmail, supportPhone, supportName);
                            preloginReturnModel.UserType = 1;
                            preloginReturnModel.AuthUrl = authUrl;
                            preloginReturnModel.SupportEmail = supportEmail;
                            preloginReturnModel.SupportPhone = supportPhone;
                            preloginReturnModel.IsLoginAllowed = true;
                            preloginReturnModel.IsEmailValid = true;
                            preloginReturnModel.NotificarLoginBlocked = true;
                            preloginReturnModel.LoginType = 0;
                            return Ok(preloginReturnModel); // New User 
                        }
                        else
                        {
                            preloginReturnModel.UserType = 4;
                            preloginReturnModel.AuthUrl = "";
                            preloginReturnModel.SupportEmail = supportEmail;
                            preloginReturnModel.SupportPhone = supportPhone;
                            preloginReturnModel.IsLoginAllowed = false;
                            preloginReturnModel.IsEmailValid = false;
                            preloginReturnModel.NotificarLoginBlocked = true;
                            preloginReturnModel.LoginType = 3;
                            return Ok(preloginReturnModel);
                        }
                    }

                }
            }
            catch (Exception ex)
            {
                TelegramService.SendMessageToTestBot2(ex.StackTrace.ToString());
                return BadRequest(ex.ToString());
            }
        }
        async Task checkAndApplyForCoupon(User user, Guid couponId)
        {
            try
            {
                if (couponId != Guid.Empty)
                {
                    var coupons = wepdb.Coupons.Where(w => w.Id == couponId && w.IsRedeemed == true && w.IsCancelled == false).FirstOrDefault();
                    if (coupons != null)
                    {
                        coupons.UserId = user.Id;
                        wepdb.SaveChanges();
                        // var onboarding = new OnboardController();
                        // await onboarding.ApplyOffer(user.Id, coupons.OfferId);
                    }
                }
                else
                {
                    var coupons = wepdb.Coupons.Where(w => w.EventName.Contains(user.Email) && w.IsRedeemed == true && w.IsCancelled == false).FirstOrDefault();
                    if (coupons != null)
                    {
                        coupons.UserId = user.Id;
                        wepdb.SaveChanges();
                        var onboarding = new OnboardController(wepdb, weddb, weadb, wetdb, weodb, _authService);
                        await onboarding.ApplyOffer(user.Id, coupons.OfferId);
                    }
                }
            }
            catch (Exception ex)
            {
                telemetryTracker.TrackException(ex);
            }
        }



        [AllowAnonymous]
        [Route("31BB7363")]
        [HttpPost]
        public async Task<IActionResult> SendErrorEmail([FromBody] UserErrorModel errorModel)
        {
            try
            {
                TelegramService.SendMessageToTestBot2("api call: 31BB7363");
                await EmailServices.SMTPSendErrorEmail(errorModel);
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.ToString());
            }
        }



        [AllowAnonymous]
        [Route("8F232691")]
        [HttpPost]
        public IActionResult ValidateUsingEmailOtp([FromBody] ValidationModel validationModel)
        {
            try
            {
                TelegramService.SendMessageToTestBot2("api call: 8F232691");
                telemetryTracker.TrackEvent("ValidateUsingEmailOtp");
                var email = validationModel.EmailAddress.ToLower().TrimStart().TrimEnd();
                var languageIdExist = weddb.Languages.Any(w => w.Name == validationModel.LanguageId);
                if (email == null && languageIdExist == false) return BadRequest();
                User existingUser = _redisCaching.CheckForItem("userDetail_" + email + "_" + validationModel.AppId, () => wepdb.Users.FirstOrDefault(w => w.Email == email && w.IsUser && w.AppId == validationModel.AppId), CustomJsonSerializer.Serialize, JsonConvert.DeserializeObject<User>, DateTimeOffset.Now.AddDays(1));

                // var userDevice = wepdb.UserDevices.FirstOrDefault(w => w.Id == validationModel.DeviceId);
                if (null != existingUser)
                {
                    telemetryTracker.TrackEvent("existingUser");
                    var deviceType = weddb.DeviceTypes.FirstOrDefault(w => w.Id == validationModel.DeviceTypeId);

                    SetUserSettings(existingUser.Id);
                    var coupons = wepdb.Coupons.Where(w => w.EventName.Contains(validationModel.EmailAddress) && w.IsCancelled == false);
                    if (coupons != null)
                    {
                        foreach (var coupon in coupons)
                        {
                            var onboarding = new OnboardController(wepdb, weddb, weadb, wetdb, weodb, _authService);
                            onboarding.ApplyOffer(existingUser.Id, coupon.Id);
                        }

                    }
                    var encriptedPwd = CommonData.passwordEncryption(validationModel.OTP.ToString());
                    var decriptedPwd = CommonData.passwordDecription(existingUser.Password);
                    var isVerifiedUserPwd = (decriptedPwd == validationModel.OTP.ToString()) ? true : false;
                    var isUserVerified = (existingUser.Password == encriptedPwd) ? true : false;
                    bool isTestUser = (bool)existingUser.IsTestUser;
                    if (isTestUser)
                    {
                        if (validationModel.OTP == decriptedPwd)
                        {
                            var encriptedTestPwd = CommonData.passwordEncryption(validationModel.OTP.ToString());
                            existingUser.Password = encriptedTestPwd;
                            wepdb.SaveChanges();
                        }
                    }
                    else
                    {
                        if (isUserVerified == null || isVerifiedUserPwd == false)
                        {
                            return BadRequest("Wrong Password");
                        }
                    }
                    // var contactExist = wesdb.Contacts.FirstOrDefault(w => w.UserId == existingUser.Id && w.OtherUserId == existingUser.Id);'


                    var returnModel = new ValidateReturnModel();
                    wepdb.UserStatuses.FirstOrDefault(w => w.UserId == existingUser.Id).Otp = true;
                    wepdb.UserStatuses.FirstOrDefault(w => w.UserId == existingUser.Id).Walkthrough = true;
                    wepdb.SaveChanges();
                    UserStatusModel userStatusData = wepdb.UserStatuses.Where(w => w.UserId == existingUser.Id).Select(w => new UserStatusModel()
                    {
                        Id = w.Id,
                        AndroidContacts = w.AndroidContacts,
                        FacebookContacts = w.FacebookContacts,
                        FreeEmail = w.FreeEmail,
                        FreeLinkedIn = w.FreeLinkedIn,
                        FreeStorage = w.FreeStorage,
                        iOSContacts = w.IOscontacts,
                        FreeTwitter = w.FreeTwitter,
                        LinkedInContacts = w.LinkedInContacts,
                        MobileVerification = w.MobileVerification,
                        OTP = w.Otp,
                        ProvisionApp = w.ProvisionApp,
                        PurchaseEmail = w.PurchaseEmail,
                        PurchaseEnterpriseApp = w.PurchaseEnterpriseApp,
                        PurchaseEnterpriseEmail = w.PurchaseEnterpriseApp,
                        PurchaseEnterpriseStorage = w.PurchaseEnterpriseStorage,
                        PurchaseStorage = w.PurchaseStorage,
                        Registration = w.Registration,
                        TwitterContacts = w.TwitterContacts,
                        Walkthrough = w.Walkthrough,
                        ShouldShowAds = (bool)w.ShouldShowAds,
                    }).FirstOrDefault();

                    //if (existingUser.IsVirtualOnly.HasValue && existingUser.IsVirtualOnly.Value == true)
                    //    userStatusData.IsVirtualOnly = existingUser.IsVirtualOnly.Value;
                    //else
                    //    userStatusData.IsVirtualOnly = false;

                    existingUser.PreviousLogin = existingUser.LastLogin;
                    //existingUser.IsPhoneValidated = false;
                    existingUser.IsEmailValidated = true;
                    existingUser.LastLogin = DateTime.UtcNow;
                    existingUser.ModifiedDate = DateTime.UtcNow;
                    existingUser.IsUser = true;
                    if (existingUser.FirstLogin == null)
                        existingUser.FirstLogin = DateTime.UtcNow;
                    //else
                    //    existingUser.LastLogin = DateTime.UtcNow;
                    //  var header = Request.Headers.FirstOrDefault(h => h.Key == "User-Agent").Value.First();
                    Models.Models.TokenModel.Token token = new Models.Models.TokenModel.Token()
                    {
                        Id = Guid.NewGuid(),
                        UserId = existingUser.Id,
                        IsActive = true,
                        CreatedDate = DateTime.UtcNow,
                        ModifiedDate = DateTime.UtcNow,
                        //TTL = DateTime.UtcNow.AddDays(100),
                        AppId = existingUser.AppId,
                        UserAgent = "",
                        UserDeviceId = validationModel.DeviceId
                    };
                    wetdb.Tokens.Add(token);
                    Models.Models.TokenModel.Token webtoken = new Models.Models.TokenModel.Token()
                    {
                        Id = Guid.NewGuid(),
                        UserId = existingUser.Id,
                        IsActive = true,
                        CreatedDate = DateTime.UtcNow,
                        ModifiedDate = DateTime.UtcNow,
                        //TTL = DateTime.UtcNow.AddDays(100),
                        AppId = existingUser.AppId,
                        UserAgent = "",
                        UserDeviceId = validationModel.DeviceId,
                        IsWeb = true
                    };
                    wetdb.Tokens.Add(webtoken);
                    Models.Models.TokenModel.Token webhooktoken = new Models.Models.TokenModel.Token()
                    {
                        Id = Guid.NewGuid(),
                        UserId = existingUser.Id,
                        IsActive = true,
                        CreatedDate = DateTime.UtcNow,
                        ModifiedDate = DateTime.UtcNow,
                        //TTL = DateTime.UtcNow.AddDays(100),
                        AppId = existingUser.AppId,
                        UserAgent = "",
                        Details = "Webhook",
                        UserDeviceId = validationModel.DeviceId,
                        IsWeb = false
                    };
                    wetdb.Tokens.Add(webhooktoken);
                    wetdb.SaveChanges();
                    wepdb.SaveChanges();
                    //Task.Run(() => UpdateOtherTablesUsingEmail(validationModel, email, existingUser));
                    UpdateOtherTablesUsingEmail(validationModel, email, existingUser, token);

                    // int notificationCount = db.MergeContacts.Count(w => w.OtherUserId == existingUser.Id && w.IsActive);
                    var domainName = existingUser.Email.Split('@');
                    var domain = domainName[1];
                    //if (!domain.Contains("mrawesomeapps.com") && !domain.Contains("mapit4.me") && !domain.Contains("whatelse.io"))
                    //{

                    //    if (existingUser.ChargebeeCustomerId != null && existingUser.ChargebeeCustomerId.ToString() != Guid.Empty.ToString())
                    //    {
                    //        try
                    //        {
                    //            var msg = "User DeviceType : " + deviceType.Name;
                    //            ApiConfig.Configure("1Page", "live_Tf3jmCoHTjMZ1cdzlEzuLRK8qsQ6cudxaF");
                    //            EntityResult customerExist = Customer.Retrieve(existingUser.ChargebeeCustomerId).Request();
                    //            Customer customer = customerExist.Customer;
                    //            if (customer != null)
                    //            {
                    //                EntityResult result1 = Comment.Create()
                    //                      .EntityId(customer.Id)
                    //                      .EntityType(ChargeBee.Models.Enums.EntityTypeEnum.Customer)
                    //                      .Notes(msg)
                    //                      .Request();
                    //            }
                    //        }
                    //        catch (Exception ex)
                    //        {
                    //        }
                    //    }
                    //    else
                    //    {
                    //        ApiConfig.Configure("1Page", "live_Tf3jmCoHTjMZ1cdzlEzuLRK8qsQ6cudxaF");
                    //        EntityResult result = ChargeBee.Models.Customer.Create()
                    //                          .FirstName(existingUser.FirstName)
                    //                          .LastName(existingUser.LastName)
                    //                          .Email(existingUser.Email)
                    //                          .Company(existingUser.Company).Request();
                    //        ChargeBee.Models.Customer customer = result.Customer;
                    //        ChargeBee.Models.Card card = result.Card;
                    //        existingUser.ChargebeeCustomerId = customer.Id;
                    //        wepdb.SaveChanges();
                    //        long seconds = DateTimeHelpers.ToEpoch(DateTime.UtcNow);
                    //        EntityResult result2 = Subscription.CreateForCustomer(customer.Id)
                    //                               .PlanId("free")
                    //                               .StartDate(seconds)
                    //                               .Request();

                    //        Subscription subscription = result2.Subscription;
                    //        Customer customer2 = result2.Customer;
                    //        ChargeBee.Models.Card card2 = result2.Card;
                    //        Invoice invoice = result2.Invoice;
                    //        List<UnbilledCharge> unbilledCharges = result2.UnbilledCharges;
                    //        var msg = "User DeviceType : " + deviceType.Name;
                    //        EntityResult result1 = Comment.Create()
                    //                        .EntityId(customer.Id)
                    //                        .EntityType(ChargeBee.Models.Enums.EntityTypeEnum.Customer)
                    //                        .Notes(msg)
                    //                        .Request();

                    //        existingUser.ChargebeeCustomerId = Guid.Empty.ToString();

                    //        //Creating intercom user
                    //        var intercomModel = new IntercomModel();
                    //        intercomModel.role = "user";
                    //        intercomModel.email = existingUser.Email;
                    //        intercomModel.name = existingUser.FirstName + " " + existingUser.LastName;

                    //        var intercomUrl = "https://api.intercom.io/contacts";
                    //        var access_token = "dG9rOmU5MmIxYmMyX2IyNTFfNDkwMl9hNzcxX2Y5MTAyODY5ZDBlNjoxOjA=";
                    //        var json = JsonConvert.SerializeObject(intercomModel);
                    //        var res = IntercomRestApi(json, intercomUrl, access_token);
                    //    }
                    //}

                    returnModel.TokenId = token.Id;
                    returnModel.UserId = existingUser.Id;
                    returnModel.UserStatusModel = userStatusData;
                    returnModel.UserContactId = Guid.Empty;
                    returnModel.NotificationCount = 0;//notificationCount;
                    returnModel.LanguageId = validationModel.LanguageId;
                    returnModel.UnknownPush = false;
                    returnModel.ContactPush = false;
                    returnModel.CRMPush = false;
                    returnModel.DirectoryPush = false;
                    returnModel.IsTestUser = isTestUser;
                    returnModel.FirstName = existingUser.FirstName;
                    returnModel.LastName = existingUser.LastName;
                    returnModel.AnalyticsId = existingUser.AnalyticsId;
                    //returnModel.IncomingCall = existingUser.IncomingCall ?? false;
                    //returnModel.IncomingCallEnd = existingUser.IncomingCallEnd ?? false;
                    //returnModel.OutgoingCall = existingUser.OutgoingCall ?? false;
                    //returnModel.OutgoingCallEnd = existingUser.OutgoingCallEnd ?? false;
                    //returnModel.ExotelExtension = existingUser.ExotelExtension ?? "";
                    //returnModel.ExotelNumber = existingUser.ExotelNumber ?? "";
                    returnModel.WebToken = webtoken.Id;
                    returnModel.WebhookToken = webhooktoken.Id;
                    //returnModel.phoneConfigsModel = existingUser.PhoneConfigs.Where(W => W.UserId == existingUser.Id && W.IsActive).Select(w => new Models.Models.PeopleModel.PhoneConfig() { DID = w.DID, Extn = w.Extn, PhoneNumber = w.PhoneNumber, ProviderId = w.ProviderId, SIPAddress = w.SIPAddress, InstanceUrl = w.InstanceUrl, Prefix = w.Prefix, ApiKey = w.ApiKey, Name = w.Name }).ToList();
                    //   returnModel.CallNotifications = existingUser.CallNotifications;
                    returnModel.IsInternal = false;
                    try
                    {
                        returnModel.IsInternal = weodb.OrgUsers.Where(w => w.UserId == existingUser.Id && w.Org.Name == domain).FirstOrDefault().Org.IsInternal;
                    }
                    catch (Exception ex)
                    {
                        telemetryTracker.TrackException(ex);
                        Console.WriteLine(ex.ToString());
                    }

                    returnModel.OrgIds = weodb.OrgUsers.Where(w => w.UserId == existingUser.Id && w.IsActive).Select(w => w.OrgId).Distinct().ToList();

                    //if (existingUser.CallNotifications)
                    //{
                    //    returnModel.UnknownPush = true;
                    //    returnModel.ContactPush = true;
                    //    returnModel.CRMPush = true;
                    //    returnModel.DirectoryPush = true;
                    //}
                    returnModel.CalendarNotifications = true;
                    returnModel.ServerUrlModel = weadb.Servers.Where(w => w.IsActive && w.AppId == existingUser.AppId).Select(w => new ServerUrlModel()
                    {
                        ServerType = w.ServerType,
                        url = w.Url
                    }).ToList();

                    return Ok(returnModel);
                }
                else
                {
                    return BadRequest();
                }
            }
            catch (Exception ex)
            {
                telemetryTracker.TrackException(ex);
                return BadRequest(ex.ToString());
            }
        }

        private void UpdateOtherTablesUsingEmailIP(ValidationModelWithIP validationModel, string email, User existingUser, Models.Models.TokenModel.Token token)
        {
            try
            {
                DataContext we = weddb;
                OrgContext weo = weodb;
                PeopleContext wepdbs = wepdb;

                bool isPrimary = (validationModel.DeviceTypeId == CommonData.IOSDeviceTypeId) ? false : true;

                // to set user device and token to fasle if user logs in same device without uninstalling
                if (validationModel.DeviceData != "")
                {
                    var userDevices = wepdbs.UserDevices.Where(w => w.UserId == existingUser.Id && w.IsActive == true && w.DeviceData == validationModel.DeviceData).ToList();
                    if (userDevices.Count() > 0)
                    {
                        foreach (var userDeviceItem in userDevices)
                        {
                            userDeviceItem.IsActive = false;
                            wepdbs.SaveChanges();
                            var activeToken = wetdb.Tokens.FirstOrDefault(w => w.UserDeviceId == userDeviceItem.Id && w.IsActive == true);
                            if (activeToken != null)
                            {
                                activeToken.IsActive = false;
                                wetdb.SaveChanges();
                            }
                        }
                    }
                }
                if (!wepdbs.UserDevices.Any(x => x.Id == validationModel.DeviceId))
                {
                    UserDevice userDevice = new UserDevice()
                    {
                        Id = validationModel.DeviceId,
                        DeviceTypeId = validationModel.DeviceTypeId,
                        IsActive = true,
                        CreatedDate = DateTime.UtcNow,
                        ModifiedDate = DateTime.UtcNow,
                        //CountryId = countryId,
                        UserId = existingUser.Id,
                        IsPhoneValidated = false,
                        //SanitizedNumber = santizedPhoneNumber,
                        IsPrimary = isPrimary,
                        IsPersonal = true,
                        //PhoneNumber = providedNumber,
                        //PType = Int32.Parse(numberType),
                        DeviceData = validationModel.DeviceData ?? "",
                        ShouldRefesh = true,
                        ContactPush = false,
                        //CRMPush = true,
                        DirectoryPush = false,
                        UnknownPush = false,
                        CalendarNotifications = true
                        //CallNotifications = user.CallNotifications
                    };
                    wepdbs.UserDevices.Add(userDevice);
                    wepdbs.SaveChanges();
                    we.SaveChanges();
                }

                //var userPhone = wepdbs.UserPhones.FirstOrDefault(w => w.UserId == existingUser.Id && w.SanitizedNumber == existingUser.SanitizedNumber);
                //if (userPhone != null)
                //    userPhone.IsPhoneValidated = false;
                //we.SaveChanges();
                int languageId = we.Languages.FirstOrDefault(w => w.Name == validationModel.LanguageId).Id;
                existingUser.LanguageId = languageId;
                wepdbs.SaveChanges();

                //TODO: Orgdirectory
                //var orgDirectoryExist = weo.OrgDirectories.Where(w => w.SanitizedNumber == existingUser.SanitizedNumber && w.IsActive);
                //foreach (var orgDirectory in orgDirectoryExist)
                //{
                //    var provisionExist = weo.Provisions.FirstOrDefault(w => w.OrgDirectoryId == orgDirectory.Id && w.IsActive == true);
                //    if (provisionExist != null) { provisionExist.UserId = existingUser.Id; }
                //    orgDirectory.UserId = existingUser.Id.ToString();
                //}
                //weo.SaveChanges();

                #region Adding provisions for all providers
                var provisionsExist = weodb.Provisions.Where(w => w.UserId == existingUser.Id && w.ProviderTypeId == CommonData.CRMProviderTyepId).ToList();
                if (provisionsExist.Count == 0)
                {
                    Guid orgID = CommonData.WhatElseCustomerOrgId;

                    bool isProvisioned = false;
                    bool isAccepted = true;
                    bool isTrail = false;
                    bool isClaimed = true;
                    var userDomain = existingUser.Email.Split('@');
                    var domainName = userDomain[1].ToLower();
                    //    var isfreeDomain = weddb.FreeDomains.FirstOrDefault(w => w.DomainName.ToLower() == domainName.ToLower());
                    var orgExist = weodb.Orgs.Any(w => w.Domains == domainName);
                    if (orgExist == true)
                    {
                        var org = weodb.Orgs.FirstOrDefault(w => w.Domains.ToLower() == domainName.ToLower());
                        isProvisioned = true;
                        isAccepted = true;
                        isTrail = false;
                        isClaimed = false;
                        orgID = org.Id;
                    }
                    else
                    {
                        var country = weddb.Countries.FirstOrDefault(w => w.Alpha2 == "IN");
                        var org = new OnePage.Models.Models.OrgModel.Org
                        {
                            Id = Guid.NewGuid(),
                            IsBeta = false,
                            AppId = CommonData.GlobalAppId.ToString(),
                            ModifiedDate = DateTime.UtcNow,
                            CreatedDate = DateTime.UtcNow,
                            AllowedAccounts = 1,
                            Name = domainName.ToLower(),
                            PlanId = Guid.Parse("7dc72a8d-d5fd-4809-81fe-add466cad370"),
                            Countries = country.Name + "|" + country.Prefix,
                            Domains = domainName,
                            IsActive = true,
                            IsRestricted = false,
                            IsProvider = false,
                            IsPurchaser = false,
                            IsAdminGivenFree = false,
                            IsInternal = false
                        };
                        weodb.Orgs.Add(org);
                        weodb.SaveChanges();

                        orgID = org.Id;
                        isProvisioned = true;
                        isAccepted = true;
                        isTrail = false;
                        isClaimed = false;
                    }

                    var orgUserExist = weodb.OrgDirectories.FirstOrDefault(w => w.OrgId == orgID && w.UserId == existingUser.Id.ToString());
                    if (orgUserExist == null)
                    {
                        var orgUser = new OrgUser();
                        orgUser.Id = Guid.NewGuid();
                        orgUser.IsActive = true;
                        orgUser.CreatedDate = DateTime.UtcNow;
                        orgUser.ActivatedOn = DateTime.UtcNow;
                        orgUser.ModifiedDate = DateTime.UtcNow;
                        orgUser.OrgId = orgID;
                        orgUser.UserId = existingUser.Id;
                        orgUser.IsAdmin = false;
                        weodb.OrgUsers.Add(orgUser);
                        weodb.SaveChanges();

                        var orgDirectory = new OrgDirectory();
                        orgDirectory.Id = Guid.NewGuid();
                        orgDirectory.OrgId = orgID;
                        orgDirectory.CreatedDate = DateTime.UtcNow;
                        orgDirectory.ModifiedDate = DateTime.UtcNow;
                        orgDirectory.IsActive = true;
                        orgDirectory.FirstName = existingUser.FirstName ?? "";
                        orgDirectory.MiddleName = existingUser.MiddleName ?? "";
                        orgDirectory.LastName = existingUser.LastName ?? "";
                        orgDirectory.SanitizedNumber = "";
                        orgDirectory.ProvidedNumber = "";
                        orgDirectory.Email = existingUser.Email ?? "";
                        orgDirectory.Designation = "";
                        orgDirectory.Salutation = "";
                        orgDirectory.UserId = existingUser.Id.ToString();
                        orgDirectory.CountryId = existingUser.CountryId;
                        weodb.OrgDirectories.Add(orgDirectory);
                        weodb.SaveChanges();
                    }
                    else
                    {
                        orgUserExist.ModifiedDate = DateTime.UtcNow;
                        var existing = weodb.OrgUsers.Where(w => w.UserId == existingUser.Id).FirstOrDefault();
                        existing.ModifiedDate = DateTime.UtcNow;
                    }
                    UpdateProvidersFromOrg(existingUser, orgID);
                    weodb.SaveChanges();
                }
                #endregion

                var twitterProvision = weodb.Provisions.Where(w => w.OrgId == CommonData.WhatElseCustomerOrgId && w.ProviderId == CommonData.TwitterProviderId && w.UserId == existingUser.Id && w.EmailAddress == existingUser.Email).FirstOrDefault();
                var tprovision = new Provision();
                if (twitterProvision == null)
                {
                    tprovision.Id = Guid.NewGuid();
                    tprovision.OrgId = CommonData.WhatElseCustomerOrgId;
                    tprovision.AppId = existingUser.AppId;
                    tprovision.UserCustomerId = existingUser.ChargebeeCustomerId;
                    tprovision.ProviderTypeId = CommonData.SocialNetworkProviderTypeId;
                    tprovision.ProviderId = CommonData.TwitterProviderId;
                    tprovision.UserId = existingUser.Id;
                    tprovision.UserProviderId = Guid.Empty;
                    tprovision.CreatedDate = DateTime.UtcNow;
                    tprovision.ModifiedDate = DateTime.UtcNow;
                    tprovision.IsActive = true;
                    tprovision.IsConverted = false;
                    tprovision.IsEnterpriseConverted = false;
                    //tprovision.SanitizedNumber = user.SanitizedNumber;
                    tprovision.IsRedeemed = false;
                    //tprovision.PhoneNumber = user.ProvidedNumber ?? "";
                    //tprovision.Salutation = user.Salutation;
                    tprovision.FirstName = existingUser.FirstName ?? "";
                    tprovision.LastName = existingUser.LastName ?? "";
                    tprovision.MiddleName = existingUser.MiddleName ?? "";
                    tprovision.CountryId = existingUser.CountryId ?? "";
                    tprovision.UserId = existingUser.Id;
                    tprovision.IsFree = false;
                    tprovision.IsProvisioned = true;
                    tprovision.IsPayed = false;
                    tprovision.IsRequested = false;
                    tprovision.IsAccepted = true;
                    tprovision.IsPurchasedByUser = false;
                    tprovision.IsPurchasedOnAndroid = false;
                    tprovision.IsPurchasedOnIos = false;
                    tprovision.EmailAddress = existingUser.Email;
                    tprovision.IsClaimed = false;
                    tprovision.IsTrial = false;
                    weodb.Provisions.Add(tprovision);
                    weodb.SaveChanges();
                }
                else
                    tprovision = twitterProvision;
                //var existingTUserProvider = wepdb.UserProviders.Any(w => w.UserId == user.Id && w.IsActive == true && w.ProviderId == CommonData.TwitterProviderId);
                var x = _userProviderCaching
                    .GetUserProvidersFromRedisOrDbAsyncWithUserId(existingUser.Id, CommonData.TwitterProviderId).GetAwaiter()
                    .GetResult();
                var existingTUserProvider = x != null;
                if (!existingTUserProvider)
                {

                    var newTUserProvider1 = new Models.Models.PeopleModel.UserProvider()
                    {
                        Id = Guid.NewGuid(),
                        UserId = existingUser.Id,
                        AppId = existingUser.AppId,
                        OrgId = CommonData.WhatElseCustomerOrgId,
                        Status = 1,
                        CreatedDate = DateTime.UtcNow,
                        ModifiedDate = DateTime.UtcNow,
                        ProviderId = CommonData.TwitterProviderId,
                        IsActive = true,
                        IsAuthenticated = true,
                        ActiveFrom = DateTime.UtcNow,
                        ActiveTill = DateTime.UtcNow.AddYears(10),
                        ProvisionId = tprovision.Id,
                        EmailAddress = existingUser.Email
                    };
                    // wepdb.UserProviders.Add(newTUserProvider1);
                    // wepdb.SaveChanges();
                    _userProviderCaching.AddUserProviderInRedisAndDb(newTUserProvider1).GetAwaiter().GetResult();

                }

                var ppldataProvision = weodb.Provisions.Where(w => w.OrgId == CommonData.WhatElseCustomerOrgId && w.ProviderId == CommonData.PeopleDataProviderId && w.UserId == existingUser.Id && w.EmailAddress == existingUser.Email).FirstOrDefault();

                var provision = new Provision();
                if (ppldataProvision == null)
                {
                    provision.Id = Guid.NewGuid();
                    provision.OrgId = CommonData.WhatElseCustomerOrgId;
                    provision.AppId = existingUser.AppId;
                    provision.UserCustomerId = existingUser.ChargebeeCustomerId;
                    provision.ProviderTypeId = CommonData.CompanyInfoProviderTypeId;
                    provision.ProviderId = CommonData.PeopleDataProviderId;
                    provision.UserId = existingUser.Id;
                    provision.UserProviderId = Guid.Empty;
                    provision.CreatedDate = DateTime.UtcNow;
                    provision.ModifiedDate = DateTime.UtcNow;
                    provision.IsActive = true;
                    provision.IsConverted = false;
                    provision.IsEnterpriseConverted = false;
                    //provision.SanitizedNumber = user.SanitizedNumber;
                    provision.IsRedeemed = false;
                    //provision.PhoneNumber = user.ProvidedNumber ?? "";
                    //provision.Salutation = user.Salutation;
                    provision.FirstName = existingUser.FirstName ?? "";
                    provision.LastName = existingUser.LastName ?? "";
                    provision.MiddleName = existingUser.MiddleName ?? "";
                    provision.CountryId = existingUser.CountryId ?? "";
                    provision.UserId = existingUser.Id;
                    provision.IsFree = false;
                    provision.IsProvisioned = true;
                    provision.IsPayed = false;
                    provision.IsRequested = false;
                    provision.IsAccepted = true;
                    provision.IsPurchasedByUser = false;
                    provision.IsPurchasedOnAndroid = false;
                    provision.IsPurchasedOnIos = false;
                    provision.EmailAddress = existingUser.Email;
                    provision.IsClaimed = false;
                    provision.IsTrial = false;
                    weodb.Provisions.Add(provision);
                    weodb.SaveChanges();
                }
                else
                    provision = ppldataProvision;
                //var existingUserProvider = wepdb.UserProviders.Any(w => w.UserId == user.Id && w.IsActive == true && w.ProviderId == CommonData.PeopleDataProviderId);
                var y = _userProviderCaching
                    .GetUserProvidersFromRedisOrDbAsyncWithUserId(existingUser.Id, CommonData.PeopleDataProviderId).GetAwaiter()
                    .GetResult();
                var existingUserProvider = y != null;
                if (!existingUserProvider)
                {

                    var newUserProvider1 = new Models.Models.PeopleModel.UserProvider()
                    {
                        Id = Guid.NewGuid(),
                        UserId = existingUser.Id,
                        AppId = existingUser.AppId,
                        OrgId = CommonData.WhatElseCustomerOrgId,
                        Status = 1,
                        CreatedDate = DateTime.UtcNow,
                        ModifiedDate = DateTime.UtcNow,
                        ProviderId = CommonData.PeopleDataProviderId,
                        IsActive = true,
                        IsAuthenticated = true,
                        ActiveFrom = DateTime.UtcNow,
                        ActiveTill = DateTime.UtcNow.AddYears(10),
                        ProvisionId = provision.Id,
                        EmailAddress = existingUser.Email
                    };
                    // wepdb.UserProviders.Add(newUserProvider1);
                    // wepdb.SaveChanges();
                    _userProviderCaching.AddUserProviderInRedisAndDb(newUserProvider1).GetAwaiter().GetResult();

                }

            }
            catch (Exception ex)
            {
                telemetryTracker.TrackException(ex);
            }
        }
        private void UpdateOtherTablesUsingEmail(ValidationModel validationModel, string email, User user, Models.Models.TokenModel.Token token)
        {
            try
            {
                DataContext we = weddb;
                //wesdb = new WESyncEntities();
                OrgContext weo = weodb;
                PeopleContext wepdbs = wepdb;
                var existingUser = wepdbs.Users.FirstOrDefault(w => w.Id == user.Id);

                bool isPrimary = (validationModel.DeviceTypeId == CommonData.IOSDeviceTypeId) ? false : true;

                // to set user device and token to fasle if user logs in same device without uninstalling
                if (validationModel.DeviceData != "")
                {
                    var userDevices = wepdbs.UserDevices.Where(w => w.UserId == existingUser.Id && w.IsActive == true && w.DeviceData == validationModel.DeviceData).ToList();
                    if (userDevices.Count() > 0)
                    {
                        foreach (var userDeviceItem in userDevices)
                        {
                            userDeviceItem.IsActive = false;
                            wepdbs.SaveChanges();
                            var activeToken = wetdb.Tokens.FirstOrDefault(w => w.UserDeviceId == userDeviceItem.Id && w.IsActive == true);
                            if (activeToken != null)
                            {
                                activeToken.IsActive = false;
                                wetdb.SaveChanges();
                            }
                        }
                    }
                }
                UserDevice userDevice = new UserDevice()
                {
                    Id = validationModel.DeviceId,
                    DeviceTypeId = validationModel.DeviceTypeId,
                    IsActive = true,
                    CreatedDate = DateTime.UtcNow,
                    ModifiedDate = DateTime.UtcNow,
                    //CountryId = countryId,
                    UserId = existingUser.Id,
                    IsPhoneValidated = false,
                    //SanitizedNumber = santizedPhoneNumber,
                    IsPrimary = isPrimary,
                    IsPersonal = true,
                    //PhoneNumber = providedNumber,
                    //PType = Int32.Parse(numberType),
                    DeviceData = validationModel.DeviceData ?? "",
                    ShouldRefesh = true,
                    ContactPush = false,
                    //CRMPush = true,
                    DirectoryPush = false,
                    UnknownPush = false,
                    CalendarNotifications = true
                    //CallNotifications = user.CallNotifications
                };
                wepdbs.UserDevices.Add(userDevice);
                wepdbs.SaveChanges();
                we.SaveChanges();

                //var userPhone = wepdbs.UserPhones.FirstOrDefault(w => w.UserId == existingUser.Id && w.SanitizedNumber == existingUser.SanitizedNumber);
                //if (userPhone != null)
                //    userPhone.IsPhoneValidated = false;
                //we.SaveChanges();
                int languageId = we.Languages.FirstOrDefault(w => w.Name == validationModel.LanguageId).Id;
                existingUser.LanguageId = languageId;
                wepdbs.SaveChanges();

                //TODO: Orgdirectory
                //var orgDirectoryExist = weo.OrgDirectories.Where(w => w.SanitizedNumber == existingUser.SanitizedNumber && w.IsActive);
                //foreach (var orgDirectory in orgDirectoryExist)
                //{
                //    var provisionExist = weo.Provisions.FirstOrDefault(w => w.OrgDirectoryId == orgDirectory.Id && w.IsActive == true);
                //    if (provisionExist != null) { provisionExist.UserId = existingUser.Id; }
                //    orgDirectory.UserId = existingUser.Id.ToString();
                //}
                //weo.SaveChanges();

                #region Adding provisions for all providers
                var provisionsExist = weodb.Provisions.Where(w => w.UserId == user.Id && w.ProviderTypeId == CommonData.CRMProviderTyepId).ToList();
                if (provisionsExist.Count == 0)
                {
                    Guid orgID = CommonData.WhatElseCustomerOrgId;

                    bool isProvisioned = false;
                    bool isAccepted = true;
                    bool isTrail = false;
                    bool isClaimed = true;
                    var userDomain = user.Email.Split('@');
                    var domainName = userDomain[1].ToLower();
                    // var isfreeDomain = weddb.FreeDomains.FirstOrDefault(w => w.DomainName.ToLower() == domainName.ToLower());
                    var orgExist = weodb.Orgs.Any(w => w.Domains == domainName);
                    if (orgExist == true)
                    {
                        var org = weodb.Orgs.FirstOrDefault(w => w.Domains.ToLower() == domainName.ToLower());
                        isProvisioned = true;
                        isAccepted = true;
                        isTrail = false;
                        isClaimed = false;
                        orgID = org.Id;
                    }
                    else
                    {
                        var country = weddb.Countries.FirstOrDefault(w => w.Alpha2 == "IN");
                        var org = new OnePage.Models.Models.OrgModel.Org();
                        org.Id = Guid.NewGuid();
                        org.IsBeta = false;
                        org.AppId = CommonData.GlobalAppId.ToString();
                        org.ModifiedDate = DateTime.UtcNow;
                        org.CreatedDate = DateTime.UtcNow;
                        org.AllowedAccounts = 1;
                        org.Name = domainName.ToLower();
                        org.PlanId = Guid.Parse("7dc72a8d-d5fd-4809-81fe-add466cad370");
                        org.Countries = country.Name + "|" + country.Prefix;
                        org.Domains = domainName;
                        org.IsActive = true;
                        org.IsRestricted = false;
                        org.IsProvider = false;
                        org.IsPurchaser = false;
                        org.IsAdminGivenFree = false;
                        org.IsInternal = false;
                        weodb.Orgs.Add(org);
                        weodb.SaveChanges();

                        orgID = org.Id;
                        isProvisioned = true;
                        isAccepted = true;
                        isTrail = false;
                        isClaimed = false;

                        //if (isfreeDomain == null)
                        //{
                        //    ApiConfig.Configure("1Page", "live_Tf3jmCoHTjMZ1cdzlEzuLRK8qsQ6cudxaF");

                        //    //ApiConfig.Configure("mapit4me", "live_adovCI4ou0Wcdb0BokjBE0Zcg9N2nFJgz");

                        //    EntityResult result = ChargeBee.Models.Customer.Create()
                        //                 .FirstName("Company")
                        //                 .LastName("OAYAW")
                        //                 .Email(user.Email)
                        //                 .Company("Company - " + domainName).Request();
                        //    ChargeBee.Models.Customer customer = result.Customer;
                        //    ChargeBee.Models.Card card = result.Card;
                        //    //user.ChargebeeCustomerId = customer.Id;
                        //    wepdb.SaveChanges();
                        //    //long seconds = DateTimeHelpers.ToEpoch(DateTime.UtcNow);
                        //    //EntityResult result2 = Subscription.CreateForCustomer(customer.Id)
                        //    //                       .PlanId("standard")
                        //    //                       .StartDate(seconds)
                        //    //                       .Request();

                        //    //Subscription subscription = result2.Subscription;
                        //    //Customer customer2 = result2.Customer;
                        //    //ChargeBee.Models.Card card2 = result2.Card;
                        //    //Invoice invoice = result2.Invoice;
                        //    //List<UnbilledCharge> unbilledCharges = result2.UnbilledCharges;
                        //}
                        //else
                        //{
                        //    ApiConfig.Configure("1Page", "live_Tf3jmCoHTjMZ1cdzlEzuLRK8qsQ6cudxaF");

                        //    //ApiConfig.Configure("mapit4me", "live_adovCI4ou0Wcdb0BokjBE0Zcg9N2nFJgz");

                        //    EntityResult result = ChargeBee.Models.Customer.Create()
                        //                 .FirstName("Company")
                        //                 .LastName("OAYAW")
                        //                 .Email(user.Email)
                        //                 .Company("Freedomain - " + domainName).Request();
                        //    ChargeBee.Models.Customer customer = result.Customer;
                        //    ChargeBee.Models.Card card = result.Card;
                        //    //user.ChargebeeCustomerId = customer.Id;
                        //    wepdb.SaveChanges();
                        //    //long seconds = DateTimeHelpers.ToEpoch(DateTime.UtcNow);
                        //    //EntityResult result2 = Subscription.CreateForCustomer(customer.Id)
                        //    //                       .PlanId("standard")
                        //    //                       .StartDate(seconds)
                        //    //                       .Request();

                        //    //Subscription subscription = result2.Subscription;
                        //    //Customer customer2 = result2.Customer;
                        //    //ChargeBee.Models.Card card2 = result2.Card;
                        //    //Invoice invoice = result2.Invoice;
                        //    //List<UnbilledCharge> unbilledCharges = result2.UnbilledCharges;
                        //}
                    }

                    var orgUserExist = weodb.OrgDirectories.FirstOrDefault(w => w.OrgId == orgID && w.UserId == user.Id.ToString());
                    if (orgUserExist == null)
                    {
                        var orgUser = new OrgUser();
                        orgUser.Id = Guid.NewGuid();
                        orgUser.IsActive = true;
                        orgUser.CreatedDate = DateTime.UtcNow;
                        orgUser.ActivatedOn = DateTime.UtcNow;
                        orgUser.ModifiedDate = DateTime.UtcNow;
                        orgUser.OrgId = orgID;
                        orgUser.UserId = user.Id;
                        orgUser.IsAdmin = false;
                        weodb.OrgUsers.Add(orgUser);
                        weodb.SaveChanges();

                        var orgDirectory = new OrgDirectory();
                        orgDirectory.Id = Guid.NewGuid();
                        orgDirectory.OrgId = orgID;
                        orgDirectory.CreatedDate = DateTime.UtcNow;
                        orgDirectory.ModifiedDate = DateTime.UtcNow;
                        orgDirectory.IsActive = true;
                        orgDirectory.FirstName = user.FirstName ?? "";
                        orgDirectory.MiddleName = user.MiddleName ?? "";
                        orgDirectory.LastName = user.LastName ?? "";
                        orgDirectory.SanitizedNumber = "";
                        orgDirectory.ProvidedNumber = "";
                        orgDirectory.Email = user.Email ?? "";
                        orgDirectory.Designation = "";
                        orgDirectory.Salutation = "";
                        orgDirectory.UserId = user.Id.ToString();
                        orgDirectory.CountryId = user.CountryId;
                        weodb.OrgDirectories.Add(orgDirectory);
                        weodb.SaveChanges();
                    }
                    else
                    {
                        orgUserExist.ModifiedDate = DateTime.UtcNow;
                        var existing = weodb.OrgUsers.Where(w => w.UserId == user.Id).FirstOrDefault();
                        existing.ModifiedDate = DateTime.UtcNow;
                    }
                    UpdateProvidersFromOrg(user, orgID);
                    weodb.SaveChanges();
                }
                #endregion

                var twitterProvision = weodb.Provisions.Where(w => w.OrgId == CommonData.WhatElseCustomerOrgId && w.ProviderId == CommonData.TwitterProviderId && w.UserId == user.Id && w.EmailAddress == user.Email).FirstOrDefault();
                var tprovision = new Provision();
                if (twitterProvision == null)
                {
                    tprovision.Id = Guid.NewGuid();
                    tprovision.OrgId = CommonData.WhatElseCustomerOrgId;
                    tprovision.AppId = user.AppId;
                    tprovision.UserCustomerId = user.ChargebeeCustomerId;
                    tprovision.ProviderTypeId = CommonData.SocialNetworkProviderTypeId;
                    tprovision.ProviderId = CommonData.TwitterProviderId;
                    tprovision.UserId = user.Id;
                    tprovision.UserProviderId = Guid.Empty;
                    tprovision.CreatedDate = DateTime.UtcNow;
                    tprovision.ModifiedDate = DateTime.UtcNow;
                    tprovision.IsActive = true;
                    tprovision.IsConverted = false;
                    tprovision.IsEnterpriseConverted = false;
                    //tprovision.SanitizedNumber = user.SanitizedNumber;
                    tprovision.IsRedeemed = false;
                    //tprovision.PhoneNumber = user.ProvidedNumber ?? "";
                    //tprovision.Salutation = user.Salutation;
                    tprovision.FirstName = user.FirstName ?? "";
                    tprovision.LastName = user.LastName ?? "";
                    tprovision.MiddleName = user.MiddleName ?? "";
                    tprovision.CountryId = user.CountryId ?? "";
                    tprovision.UserId = user.Id;
                    tprovision.IsFree = false;
                    tprovision.IsProvisioned = true;
                    tprovision.IsPayed = false;
                    tprovision.IsRequested = false;
                    tprovision.IsAccepted = true;
                    tprovision.IsPurchasedByUser = false;
                    tprovision.IsPurchasedOnAndroid = false;
                    tprovision.IsPurchasedOnIos = false;
                    tprovision.EmailAddress = user.Email;
                    tprovision.IsClaimed = false;
                    tprovision.IsTrial = false;
                    weodb.Provisions.Add(tprovision);
                    weodb.SaveChanges();
                }
                else
                    tprovision = twitterProvision;
                //var existingTUserProvider = wepdb.UserProviders.Any(w => w.UserId == user.Id && w.IsActive == true && w.ProviderId == CommonData.TwitterProviderId);
                var x = _userProviderCaching
                    .GetUserProvidersFromRedisOrDbAsyncWithUserId(user.Id, CommonData.TwitterProviderId).GetAwaiter()
                    .GetResult();
                var existingTUserProvider = x != null;
                if (!existingTUserProvider)
                {

                    var newTUserProvider1 = new Models.Models.PeopleModel.UserProvider()
                    {
                        Id = Guid.NewGuid(),
                        UserId = user.Id,
                        AppId = user.AppId,
                        OrgId = CommonData.WhatElseCustomerOrgId,
                        Status = 1,
                        CreatedDate = DateTime.UtcNow,
                        ModifiedDate = DateTime.UtcNow,
                        ProviderId = CommonData.TwitterProviderId,
                        IsActive = true,
                        IsAuthenticated = true,
                        ActiveFrom = DateTime.UtcNow,
                        ActiveTill = DateTime.UtcNow.AddYears(10),
                        ProvisionId = tprovision.Id,
                        EmailAddress = user.Email
                    };
                    // wepdb.UserProviders.Add(newTUserProvider1);
                    // wepdb.SaveChanges();
                    _userProviderCaching.AddUserProviderInRedisAndDb(newTUserProvider1).GetAwaiter().GetResult();

                }

                var ppldataProvision = weodb.Provisions.Where(w => w.OrgId == CommonData.WhatElseCustomerOrgId && w.ProviderId == CommonData.PeopleDataProviderId && w.UserId == user.Id && w.EmailAddress == user.Email).FirstOrDefault();

                var provision = new Provision();
                if (ppldataProvision == null)
                {
                    provision.Id = Guid.NewGuid();
                    provision.OrgId = CommonData.WhatElseCustomerOrgId;
                    provision.AppId = user.AppId;
                    provision.UserCustomerId = user.ChargebeeCustomerId;
                    provision.ProviderTypeId = CommonData.CompanyInfoProviderTypeId;
                    provision.ProviderId = CommonData.PeopleDataProviderId;
                    provision.UserId = user.Id;
                    provision.UserProviderId = Guid.Empty;
                    provision.CreatedDate = DateTime.UtcNow;
                    provision.ModifiedDate = DateTime.UtcNow;
                    provision.IsActive = true;
                    provision.IsConverted = false;
                    provision.IsEnterpriseConverted = false;
                    //provision.SanitizedNumber = user.SanitizedNumber;
                    provision.IsRedeemed = false;
                    //provision.PhoneNumber = user.ProvidedNumber ?? "";
                    //provision.Salutation = user.Salutation;
                    provision.FirstName = user.FirstName ?? "";
                    provision.LastName = user.LastName ?? "";
                    provision.MiddleName = user.MiddleName ?? "";
                    provision.CountryId = user.CountryId ?? "";
                    provision.UserId = user.Id;
                    provision.IsFree = false;
                    provision.IsProvisioned = true;
                    provision.IsPayed = false;
                    provision.IsRequested = false;
                    provision.IsAccepted = true;
                    provision.IsPurchasedByUser = false;
                    provision.IsPurchasedOnAndroid = false;
                    provision.IsPurchasedOnIos = false;
                    provision.EmailAddress = user.Email;
                    provision.IsClaimed = false;
                    provision.IsTrial = false;
                    weodb.Provisions.Add(provision);
                    weodb.SaveChanges();
                }
                else
                    provision = ppldataProvision;
                //var existingUserProvider = wepdb.UserProviders.Any(w => w.UserId == user.Id && w.IsActive == true && w.ProviderId == CommonData.PeopleDataProviderId);
                var y = _userProviderCaching
                    .GetUserProvidersFromRedisOrDbAsyncWithUserId(user.Id, CommonData.PeopleDataProviderId).GetAwaiter()
                    .GetResult();
                var existingUserProvider = y != null;
                if (!existingUserProvider)
                {

                    var newUserProvider1 = new Models.Models.PeopleModel.UserProvider()
                    {
                        Id = Guid.NewGuid(),
                        UserId = user.Id,
                        AppId = user.AppId,
                        OrgId = CommonData.WhatElseCustomerOrgId,
                        Status = 1,
                        CreatedDate = DateTime.UtcNow,
                        ModifiedDate = DateTime.UtcNow,
                        ProviderId = CommonData.PeopleDataProviderId,
                        IsActive = true,
                        IsAuthenticated = true,
                        ActiveFrom = DateTime.UtcNow,
                        ActiveTill = DateTime.UtcNow.AddYears(10),
                        ProvisionId = provision.Id,
                        EmailAddress = user.Email
                    };
                    // wepdb.UserProviders.Add(newUserProvider1);
                    // wepdb.SaveChanges();
                    _userProviderCaching.AddUserProviderInRedisAndDb(newUserProvider1).GetAwaiter().GetResult();

                }

            }
            catch (Exception ex)
            {
                telemetryTracker.TrackException(ex);
            }
        }
        void UpdateProvidersFromOrg(User user, Guid orgID)
        {
            try
            {
                var activeProviders = weddb.Providers.Where(w => w.IsActive == true && w.ProviderTypeId != CommonData.CompanyInfoProviderTypeId).ToList();

                foreach (var activeProvider in activeProviders)
                {
                    try
                    {
                        var provider = weddb.Providers.FirstOrDefault(w => w.Id == activeProvider.Id && w.IsActive == true);

                        var orgProviderExist = weodb.OrgProviders.FirstOrDefault(w => w.OrgId == orgID && w.ProviderId == provider.Id && w.IsActive == true);
                        if (orgProviderExist == null)
                        {
                            var orgProvider = new OrgProvider();
                            orgProvider.Id = Guid.NewGuid();
                            orgProvider.CreatedDate = DateTime.UtcNow;
                            orgProvider.ModifiedDate = DateTime.UtcNow;
                            orgProvider.IsActive = true;
                            orgProvider.OrgId = orgID;
                            orgProvider.ProviderId = provider.Id;
                            orgProvider.CanSaveForOffline = true;
                            orgProvider.ForceCallLog = true;
                            weodb.OrgProviders.Add(orgProvider);

                            var weOrgProvider = weodb.OrgProviders.FirstOrDefault(w => w.OrgId == CommonData.WhatElseCustomerOrgId && w.ProviderId == provider.Id && w.IsActive == true);
                            if (weOrgProvider != null)
                            {
                                var weOrgProviderIdentifiers = weodb.OrgIdentifiers.Where(w => w.OrgProviderId == weOrgProvider.Id && w.IsActive == true).ToList();

                                foreach (var weOrgProviderIdentifier in weOrgProviderIdentifiers)
                                {
                                    var orgIdentifier = new OrgIdentifier();
                                    orgIdentifier.Id = Guid.NewGuid();
                                    orgIdentifier.OrgProviderId = orgProvider.Id;
                                    orgIdentifier.IdentifierId = weOrgProviderIdentifier.IdentifierId;
                                    orgIdentifier.IsActive = true;
                                    orgIdentifier.CreatedDate = DateTime.UtcNow;
                                    orgIdentifier.ModifiedDate = DateTime.UtcNow;
                                    orgIdentifier.ShowSequence = 1;
                                    orgIdentifier.Value = weOrgProviderIdentifier.Value;
                                    weodb.OrgIdentifiers.Add(orgIdentifier);
                                    weodb.SaveChanges();
                                }
                            }
                        }
                        var existingProvision = weodb.Provisions.Where(w => w.OrgId == orgID && w.ProviderId == provider.Id && w.UserId == user.Id && w.EmailAddress == user.Email).FirstOrDefault();
                        if (existingProvision == null)
                        {
                            //TODO: comment for on the fly provisioning
                            #region Provider provision
                            var provision = new Provision();
                            provision.Id = Guid.NewGuid();
                            provision.OrgId = orgID; //CommonData.WhatElseCustomerOrgId;
                            provision.AppId = user.AppId;
                            provision.UserCustomerId = user.ChargebeeCustomerId;
                            provision.ProviderTypeId = provider.ProviderTypeId;
                            provision.ProviderId = provider.Id;
                            provision.UserId = user.Id;
                            provision.UserProviderId = Guid.Empty;
                            provision.CreatedDate = DateTime.UtcNow;
                            provision.ModifiedDate = DateTime.UtcNow;
                            provision.IsActive = true;
                            provision.IsConverted = false;
                            provision.IsEnterpriseConverted = false;
                            //provision.SanitizedNumber = user.SanitizedNumber;
                            provision.IsRedeemed = false;
                            //provision.PhoneNumber = user.ProvidedNumber ?? "";
                            //provision.Salutation = user.Salutation;
                            provision.FirstName = user.FirstName ?? "";
                            provision.LastName = user.LastName ?? "";
                            provision.MiddleName = user.MiddleName ?? "";
                            provision.CountryId = user.CountryId ?? "";
                            provision.UserId = user.Id;
                            provision.IsFree = false;
                            provision.IsProvisioned = true;
                            provision.IsPayed = false;
                            provision.IsRequested = false;
                            provision.IsAccepted = true;
                            provision.IsPurchasedByUser = false;
                            provision.IsPurchasedOnAndroid = false;
                            provision.IsPurchasedOnIos = false;
                            provision.EmailAddress = user.Email;
                            provision.IsClaimed = false;
                            provision.IsTrial = false;
                            weodb.Provisions.Add(provision);
                            #endregion
                        }
                    }
                    catch (Exception ex)
                    {
                        telemetryTracker.TrackException(ex);
                        Console.WriteLine(ex.ToString());
                    }
                }
                weodb.SaveChanges();
            }
            catch (Exception ex)
            {
                telemetryTracker.TrackException(ex);
                Console.WriteLine(ex.ToString());
            }
        }
        void SetOrgLevelSettings(Guid userId)
        {
            try
            {
                var orgId = weodb.OrgUsers.First(w => w.UserId == userId && w.OrgId != Common.CommonData.WhatElseCustomerOrgId && w.IsActive == true).OrgId;
                if (orgId != Guid.Empty)
                {
                    Guid TeamsProviderId = Guid.Parse("5CC37A0C-96DD-4E30-93C7-A0A991156640");
                    var orgProviderExists = weodb.OrgProviders.Any(w => w.OrgId == orgId && w.ProviderId == TeamsProviderId && w.IsActive == true);
                    if (orgProviderExists)
                    {
                        var orgProvider = weodb.OrgProviders.First(w => w.OrgId == orgId && w.ProviderId == TeamsProviderId && w.IsActive == true);
                        Guid TeamsConsentIId = Guid.Parse("3A225307-704A-4E89-AC95-07169F88A008");
                        var teamsConsentIdentifier = weodb.OrgIdentifiers.First(w => w.OrgProviderId == orgProvider.Id && w.IsActive == true && w.IdentifierId == TeamsConsentIId);

                        var teamsConsentSettingId = Guid.Parse("2E2A7614-0BBC-40F5-A643-6DDBE3610000");
                        var tcSetting = wepdb.UserSettings.First(w => w.UserId == userId && w.SettingId == teamsConsentSettingId && w.IsActive == true);
                        tcSetting.Value = teamsConsentIdentifier.Value;
                        tcSetting.IsUserUpdated = true;
                        wepdb.SaveChanges();
                    }
                }
            }
            catch (Exception ex)
            {

            }
        }
        void SetUserSettings(Guid userId)
        {
            try
            {
                bool IsPaidUser = weodb.OrgUsers.Any(w => w.UserId == userId && w.Org.IsPaid == true);
                Guid firstSetting = Guid.Parse("1d9bc314-786c-40e4-9fe4-769860c58f01");
                // User without any settings
                var settingList = wepdb.Settings.Where(w => w.IsActive == true).ToList();
                var exisitngSettings = wepdb.UserSettings.Where(x => x.UserId == userId && x.SettingId == firstSetting).FirstOrDefault();
                if (exisitngSettings == null)
                {
                    foreach (var item in settingList.ToList())
                    {
                        try
                        {
                            var vals = item.DefaultValue.Split('|');
                            var userSetting = new UserSetting
                            {
                                Id = Guid.NewGuid(),
                                IsActive = true,
                                SettingId = item.Id,
                                CreatedDate = DateTime.UtcNow,
                                ModifiedDate = DateTime.UtcNow,
                                UserId = userId,
                                DataType = item.DataType,
                                Value = IsPaidUser ? vals[1] : vals[0]
                            };
                            wepdb.UserSettings.Add(userSetting);
                        }
                        catch (Exception ex)
                        {
                            telemetryTracker.TrackException(ex);
                            Console.WriteLine(ex.ToString());
                        }
                    }
                    wepdb.SaveChanges();
                }
                else // User missing some settings
                {
                    var userSettings = wepdb.UserSettings.Where(w => w.UserId == userId).ToList();
                    foreach (var item in settingList.ToList())
                    {
                        try
                        {
                            var vals = item.DefaultValue.Split('|');
                            if (!userSettings.Any(w => w.UserId == userId && w.SettingId == item.Id))
                            {
                                var userSetting = new UserSetting
                                {
                                    Id = Guid.NewGuid(),
                                    IsActive = true,
                                    SettingId = item.Id,
                                    CreatedDate = DateTime.UtcNow,
                                    ModifiedDate = DateTime.UtcNow,
                                    UserId = userId,
                                    DataType = item.DataType,
                                    Value = IsPaidUser ? vals[1] : vals[0]
                                };
                                wepdb.UserSettings.Add(userSetting);
                            }
                            else
                            {
                                var userSetting = userSettings.FirstOrDefault(w => w.UserId == userId && w.SettingId == item.Id);
                                if (string.IsNullOrEmpty(userSetting.Value))
                                {
                                    userSetting.Value = IsPaidUser ? vals[1] : vals[0];
                                    wepdb.SaveChanges();
                                }
                            }
                            //if (item.IsAdmin)
                            //{
                            //    if (!(wepdb.UserSettings.Any(w => w.UserId == userId && w.SettingId == item.Id)))
                            //    {
                            //        var userSetting = new UserSetting
                            //        {
                            //            Id = Guid.NewGuid(),
                            //            IsActive = true,
                            //            SettingId = item.Id,
                            //            CreatedDate = DateTime.UtcNow,
                            //            ModifiedDate = DateTime.UtcNow,
                            //            UserId = userId,
                            //            DataType = item.DataType,
                            //            Value = IsPaidUser ? vals[1] : vals[0]
                            //        };
                            //        wepdb.UserSettings.Add(userSetting);
                            //    }
                            //}
                        }
                        catch (Exception ex)
                        {
                            telemetryTracker.TrackException(ex);
                            Console.WriteLine(ex.ToString());
                        }
                    }
                    wepdb.SaveChanges();

                }

            }
            catch (Exception ex)
            {
                telemetryTracker.TrackException(ex);
                Console.WriteLine(ex.ToString());
            }
        }
        private static void SendPush(string WebOneSignalId, string WebOneSignalKey, string androidOneSignalId, string androidOneSignalKey, string IosOneSignalId, string IosOneSignalKey, User user, string msg1, string msg2)
        {
            Dictionary<string, string> androidOneSignalIdsAndKeys = new Dictionary<string, string>();
            if (androidOneSignalId.Contains(','))
            {
                var androidOneSignalIds = androidOneSignalId.Split(',').ToArray();
                var androidOneSignalKeys = androidOneSignalKey.Split(',').ToArray();
                for (int i = 0; i < androidOneSignalIds.Length; i++)
                {
                    androidOneSignalIdsAndKeys.Add(androidOneSignalIds[i], androidOneSignalKeys[i]);
                }
            }
            else
            {
                androidOneSignalIdsAndKeys.Add(androidOneSignalId, androidOneSignalKey);
            }
            PushServices.IOSPush(IosOneSignalId, IosOneSignalKey, "", user.Id.ToString(), msg2, msg1, "", Guid.Empty.ToString(), 0, 10);
        }
        private void AddAdditionalTableDataUsingEmail(AuthUserModel3 userModel, string email, Guid userId, string supportEmail, string supportPhone, string supportName)
        {
            try
            {
                bool shouldShowAds = (userModel.AppId == CommonData.GlobalAppId) ? true : false;
                User user = _redisCaching.CheckForItem("userDetail_" + userId, () => wepdb.Users.FirstOrDefault(w => w.Id == userId), CustomJsonSerializer.Serialize, JsonConvert.DeserializeObject<User>, DateTimeOffset.Now.AddDays(1));

                Models.Models.PeopleModel.UserStatus userStatus = new Models.Models.PeopleModel.UserStatus()
                {
                    Id = Guid.NewGuid(),
                    UserId = userId,
                    AndroidContacts = false,
                    FacebookContacts = false,
                    FreeEmail = false,
                    FreeLinkedIn = false,
                    FreeFacebook = false,
                    FreeStorage = false,
                    FreeTwitter = false,
                    IOscontacts = false,
                    LinkedInContacts = false,
                    MobileVerification = false,
                    Otp = false,
                    ProvisionApp = false,
                    PurchaseEmail = false,
                    PurchaseEnterpriseApp = false,
                    PurchaseEnterpriseEmail = false,
                    PurchaseEnterpriseStorage = false,
                    PurchaseStorage = false,
                    Registration = false,
                    TwitterContacts = false,
                    Walkthrough = false,
                    ShouldShowAds = shouldShowAds
                };
                wepdb.UserStatuses.Add(userStatus);
                wepdb.SaveChanges();
                var deviceContactProviders = weddb.Providers.Where(w => w.ProviderTypeId == CommonData.DeviceContactsProviderTyepId).ToList();
                foreach (var deviceContactProvider in deviceContactProviders)
                {
                    if (deviceContactProvider.Id != CommonData.ContactAddedByAdmin && deviceContactProvider.Id != CommonData.ManualProviderId)
                    {
                        var provision1 = new Provision();
                        provision1.Id = Guid.NewGuid();
                        provision1.OrgId = CommonData.WhatElseCustomerOrgId;
                        provision1.UserCustomerId = user.ChargebeeCustomerId;
                        provision1.AppId = user.AppId;
                        provision1.ProviderTypeId = deviceContactProvider.ProviderTypeId;
                        provision1.ProviderId = deviceContactProvider.Id;
                        provision1.UserId = user.Id;
                        provision1.UserProviderId = Guid.Empty;
                        provision1.CreatedDate = DateTime.UtcNow;
                        provision1.ModifiedDate = DateTime.UtcNow;
                        provision1.IsActive = true;
                        provision1.IsConverted = false;
                        provision1.IsEnterpriseConverted = false;
                        // provision1.SanitizedNumber = user.SanitizedNumber;
                        provision1.IsRedeemed = false;
                        provision1.PhoneNumber = "";
                        //provision1.Salutation = user.Salutation;
                        provision1.FirstName = user.FirstName ?? "";
                        provision1.LastName = user.LastName ?? "";
                        provision1.MiddleName = user.MiddleName ?? "";
                        //  provision1.CountryId = user.CountryId;
                        provision1.UserId = user.Id;
                        provision1.IsFree = false;
                        provision1.IsProvisioned = true;
                        provision1.IsPayed = false;
                        provision1.IsRequested = false;
                        provision1.IsAccepted = false;
                        provision1.IsPurchasedByUser = false;
                        provision1.IsPurchasedOnAndroid = false;
                        provision1.IsPurchasedOnIos = false;
                        provision1.EmailAddress = deviceContactProvider.InShort;
                        provision1.IsClaimed = true;
                        provision1.IsTrial = false;
                        weodb.Provisions.Add(provision1);
                        weodb.SaveChanges();
                        var newUserProvider1 = new Models.Models.PeopleModel.UserProvider()
                        {
                            Id = Guid.NewGuid(),
                            UserId = userId,
                            AppId = user.AppId,
                            OrgId = CommonData.WhatElseCustomerOrgId,
                            Status = (int)UserProviderStatus.NewProvider,
                            CreatedDate = DateTime.UtcNow,
                            ModifiedDate = DateTime.UtcNow,
                            ProviderId = deviceContactProvider.Id,
                            IsActive = true,
                            ActiveFrom = DateTime.UtcNow,
                            ActiveTill = DateTime.UtcNow.AddYears(10),
                            ProvisionId = provision1.Id,
                            EmailAddress = deviceContactProvider.InShort
                        };
                        // wepdb.UserProviders.Add(newUserProvider1);
                        // wepdb.SaveChanges();
                        _userProviderCaching.AddUserProviderInRedisAndDb(newUserProvider1).GetAwaiter().GetResult();
                        provision1.UserProviderId = newUserProvider1.Id;
                        weodb.SaveChanges();
                    }
                }
            }
            catch (Exception ex)
            {
                telemetryTracker.TrackException(ex);
            }
        }
        [AllowAnonymous]
        [Route("CAADA196")]
        [HttpPost]
        public async Task<IActionResult> MagicLinkFlow([FromBody] AuthUserModel3 userModel)
        {
            TelegramService.SendMessageToTestBot2("api call: CAADA196");
            DateTime apiStart = DateTime.UtcNow;
            Dictionary<string, string> dictionary = new Dictionary<string, string>();
            dictionary.Add("startTime", apiStart.ToString());
            userModel.EmailAddress = userModel.EmailAddress.ToLower();
            try
            {
                PreloginReturnModel2 preloginReturnModel = new PreloginReturnModel2
                {
                    IsSSO = false,
                    SSOURL = ""
                };
                var referredBy = "";
                var couponGuid = Guid.NewGuid();
                telemetryTracker.TrackEvent("MagicLinkFlow");
                var appid = (userModel.AppId == Guid.Empty) ? CommonData.GlobalAppId : userModel.AppId;
                var tempPassword = userModel.DeviceId.ToString();
                var email = userModel.EmailAddress.ToLower().TrimStart().TrimEnd();
                User user = _redisCaching.CheckForItem("userDetail_" + email + "_" + userModel.AppId, () => wepdb.Users.FirstOrDefault(w => w.Email == email && w.IsUser && w.AppId == userModel.AppId), CustomJsonSerializer.Serialize, JsonConvert.DeserializeObject<User>, DateTimeOffset.Now.AddDays(1));

                var appInfo = weadb.Apps.FirstOrDefault(w => w.Id == userModel.AppId && w.IsActive);
                var androidOneSignalId = appInfo.AndroidOneSignalId;
                var androidOneSignalKey = appInfo.AndroidOneSignalApiKey;
                var IosOneSignalId = appInfo.IosoneSignalId;
                var IosOneSignalKey = appInfo.IosoneSignalApiKey;
                var WebOneSignalId = appInfo.WebOneSignalId;
                var WebOneSignalKey = appInfo.WebOneSignalApiKey;
                var supportEmail = appInfo.SupportEmail;
                var supportPhone = (userModel.AppId != CommonData.GlobalAppId) ? "" : (appInfo.SupportPhone ?? "");
                var supportName = appInfo.SupportName;
                var authUrl = weadb.Servers.FirstOrDefault(w => w.ServerType == 1 && w.AppId.ToString().ToLower() == userModel.AppId.ToString().ToLower() && w.IsActive).Url;

                string deviceType = Common.CommonData.GetDeviceType(userModel.DeviceType);
                string loginFlow = CommonData.GetLoginFlow(userModel.LoginFlowId);
                var domainName = email.Split('@');
                bool istestuser = CommonData.testUserDomains.Contains(domainName[1]);
                if (null != user)
                {
                    // if (user.IsTestUser == false && !userModel.EmailAddress.ToLower().Contains("onmicrosoft.com"))
                    if (user.IsTestUser == false)
                    {
                        if (userModel.AppId == CommonData.OPMaukaAppId)
                        {
                            botService.SendMessageForNewOppExistingUsers(userModel.EmailAddress, user.FirstName + " " + user.LastName, deviceType, loginFlow);
                        }
                        else
                        {
                            botService.SendMessageForExistingUsers(userModel.EmailAddress, user.FirstName + " " + user.LastName, deviceType, loginFlow);
                        }
                    }
                    Guid couponId = Guid.Empty;
                    await checkAndApplyForCoupon(user, couponId);
                    user.Password = userModel.DeviceId.ToString();
                    wepdb.SaveChanges();
                    dictionary.Add("ai", user.AnalyticsId.ToString());
                    var appLanguage = weadb.AppLanguages.FirstOrDefault(w => w.AppId == user.AppId && w.LanguageId == user.LanguageId);
                    AppLanguageTemplate appSendgridTemplateId = new AppLanguageTemplate();
                    if (appLanguage == null)
                    {
                        appLanguage = weadb.AppLanguages.FirstOrDefault(w => w.AppId == CommonData.GlobalAppId && w.LanguageId == 1);
                        appSendgridTemplateId = weadb.AppLanguageTemplates.FirstOrDefault(w => w.AppLanguageId == appLanguage.Id && w.TemplateName == "OtpEmail");
                    }
                    else
                    {
                        appSendgridTemplateId = weadb.AppLanguageTemplates.FirstOrDefault(w => w.AppLanguageId == appLanguage.Id && w.TemplateName == "OtpEmail");
                    }
                    //if (user.AppId == CommonData.MiofertaAppId || user.AppId == CommonData.NotificarAWSTestApp)
                    //{
                    //    NotificarLogOutOtherDevices(user);
                    //}
                    var userDevices = wepdb.UserDevices.Where(w => w.UserId == user.Id && w.IsActive == true).ToList();
                    bool loginAllowed = (appInfo.IsOneDevicePerAccount == true) ? ((userDevices.Count > 0) ? false : true) : true;

                    if (user.IsDisabled == true)
                    {
                        preloginReturnModel.UserType = 2;
                        preloginReturnModel.AuthUrl = "";
                        preloginReturnModel.SupportEmail = supportEmail;
                        preloginReturnModel.SupportPhone = supportPhone;
                        preloginReturnModel.IsLoginAllowed = loginAllowed;
                        preloginReturnModel.IsEmailValid = true;
                        preloginReturnModel.NotificarLoginBlocked = false;
                        preloginReturnModel.LoginType = 0;
                        return Ok(preloginReturnModel);
                    }
                    if (string.IsNullOrEmpty(user.CountryId))
                    {
                        user.CountryId = userModel.CountryId ?? "IN";
                        wepdb.SaveChanges();
                    }

                    string domain = userModel.EmailAddress.Split('@')[1].ToLower();
                    if (wepdb.Ssos.Any(w => w.Domain == domain))
                    {
                        var ssoDomain = wepdb.Ssos.First(w => w.Domain == domain);
                        preloginReturnModel.IsSSO = true;
                        preloginReturnModel.SSOURL = ssoDomain.Url;
                        user.Password = userModel.DeviceId.ToString();
                        wepdb.SaveChanges();
                    }
                    if (string.IsNullOrEmpty(preloginReturnModel.SSOURL))
                    {
                        try
                        {
                            var client = new RestSharp.RestClient("https://api.workos.com/passwordless/sessions");
                            client.Timeout = -1;
                            var request = new RestRequest(Method.POST);
                            request.AddHeader("Authorization", "Bearer sk_m33fbMrSbQ6Swwky6sSAW0Aic");
                            request.AddHeader("Content-Type", "application/x-www-form-urlencoded");
                            request.AddParameter("email", userModel.EmailAddress);
                            request.AddParameter("type", "MagicLink");
                            request.AddParameter("state", userModel.DeviceId);
                            IRestResponse response = client.Execute(request);
                            await Task.Delay(2000);
                            JToken opToken = JsonConvert.DeserializeObject<JToken>(response.Content);
                            sbnew.AppendLine("Magic link token: " + opToken.ToString());

                            telemetryTracker.TrackEvent(opToken.ToString());
                            try
                            {
                                if (opToken != null)
                                {
                                    if (opToken["object"] != null && opToken["object"].Value<string>() == "passwordless_session")
                                    {
                                        string link = opToken["link"].Value<string>();
                                        string apiUrl = "https://api.mailmodo.com/api/v1/triggerCampaign/1225510f-b36b-4b54-a1eb-8f948101ced8";

                                        Payload payload = new Payload();
                                        payload.email = userModel.EmailAddress;
                                        DataModel model = new DataModel();
                                        model.url = link;
                                        payload.data = model;
                                        var result = await OnePage.Services.RestClient.Instance.MailModoPostAsync<string>(apiUrl, payload);
                                        sbnew.AppendLine("Mailmodo response: " + result.ToString());
                                        //    await EmailServices.SendMagicLinkOTPMail2(true, user.FirstName, userModel.EmailAddress, true, link);
                                        user.Password = userModel.DeviceId.ToString();

                                    }
                                }
                                else
                                {
                                    var client2 = new RestSharp.RestClient("https://api.workos.com/passwordless/sessions");
                                    client.Timeout = -1;
                                    var request2 = new RestRequest(Method.POST);
                                    request2.AddHeader("Authorization", "Bearer sk_m33fbMrSbQ6Swwky6sSAW0Aic");
                                    request2.AddHeader("Content-Type", "application/x-www-form-urlencoded");
                                    request2.AddParameter("email", userModel.EmailAddress);
                                    request2.AddParameter("type", "MagicLink");
                                    request2.AddParameter("state", userModel.DeviceId);
                                    IRestResponse response2 = client2.Execute(request2);
                                    await Task.Delay(2000);
                                    JToken opToken2 = JsonConvert.DeserializeObject<JToken>(response2.Content);
                                    sbnew.AppendLine("Magic link token: " + opToken2.ToString());
                                    telemetryTracker.TrackEvent(opToken2.ToString());


                                    if (opToken2 != null)
                                    {
                                        if (opToken2["object"] != null && opToken2["object"].Value<string>() == "passwordless_session")
                                        {
                                            string link = opToken2["link"].Value<string>();
                                            string apiUrl = "https://api.mailmodo.com/api/v1/triggerCampaign/1225510f-b36b-4b54-a1eb-8f948101ced8";

                                            Payload payload = new Payload();
                                            payload.email = userModel.EmailAddress;
                                            DataModel model = new DataModel();
                                            model.url = link;
                                            payload.data = model;
                                            var result2 = await OnePage.Services.RestClient.Instance.MailModoPostAsync<string>(apiUrl, payload);
                                            sbnew.AppendLine("Mailmodo response: " + result2);
                                            //await EmailServices.SendMagicLinkOTPMail2(true, "", userModel.EmailAddress, true, link);
                                            user.Password = userModel.DeviceId.ToString();
                                        }
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                StringBuilder sb1 = new StringBuilder();
                                sb1.AppendLine("Exception of magic link api: Message: " + ex.Message + ", StackTrace:" + ex.StackTrace + ", InnerException" + ex.InnerException);
                                UserLoginInfoModel model = new UserLoginInfoModel();
                                model.EmailId = userModel.EmailAddress;
                                model.From = "<EMAIL>";
                                model.Heading = "Api Exception  CAADA196 ";
                                model.Message = sb1.ToString();
                                model.Subject = "Api Exception  CAADA196  ";
                                model.ToList.Add("<EMAIL>");
                                await EmailServices.SMTPSendEmail(model);
                            }
                            wepdb.SaveChanges();
                        }
                        catch (Exception ex)
                        {
                            StringBuilder sb1 = new StringBuilder();
                            sb1.AppendLine("Exception of magic link api: Message: " + ex.Message + ", StackTrace:" + ex.StackTrace + ", InnerException" + ex.InnerException);
                            UserLoginInfoModel model = new UserLoginInfoModel();
                            model.EmailId = userModel.EmailAddress;
                            model.From = "<EMAIL>";
                            model.Heading = "Api Exception  CAADA196 ";
                            model.Message = sb1.ToString();
                            model.Subject = "Api Exception  CAADA196  ";
                            model.ToList.Add("<EMAIL>");
                            await EmailServices.SMTPSendEmail(model);
                            telemetryTracker.TrackException(ex);
                        }
                    }
                    UserLoginInfoModel model2 = new UserLoginInfoModel();
                    model2.EmailId = userModel.EmailAddress;
                    model2.From = "<EMAIL>";
                    model2.Heading = "Mail Vaildation ";
                    model2.Message = sbnew.ToString();
                    model2.Subject = "Mail Vaildation ";
                    model2.ToList.Add("<EMAIL>");
                    await EmailServices.SMTPSendEmail(model2);

                    preloginReturnModel.UserType = 0;
                    preloginReturnModel.AuthUrl = authUrl;
                    preloginReturnModel.SupportEmail = supportEmail;
                    preloginReturnModel.SupportPhone = supportPhone;
                    preloginReturnModel.IsLoginAllowed = loginAllowed;
                    preloginReturnModel.IsEmailValid = true;
                    preloginReturnModel.NotificarLoginBlocked = false;
                    preloginReturnModel.LoginType = 0;
                    return Ok(preloginReturnModel); // Existing User
                }
                else
                {
                    // if (!userModel.EmailAddress.ToLower().Contains("onmicrosoft.com"))
                    if (userModel.AppId == CommonData.OPMaukaAppId)
                    {
                        botService.SendMessageForNewOppNewUsers(userModel.EmailAddress, "", deviceType, loginFlow);
                        //   TelegramService.SendMessageToMoukhaBot("New user trying to login: " + userModel.EmailAddress + "\r\n" + "Device Type: " + deviceType + "\r\n" + "Login: " + loginFlow);
                    }
                    else
                    {
                        botService.SendMessageForNewUsers(userModel.EmailAddress, "", deviceType, loginFlow);
                        //TelegramService.SendMessage("New user trying to login: " + userModel.EmailAddress + "\r\n" + "Device Type: " + deviceType + "\r\n" + "Login: " + loginFlow);
                    }
                    if (!string.IsNullOrEmpty(userModel.Coupon))
                    {
                        Models.Models.PeopleModel.Coupon coupon = wepdb.Coupons.FirstOrDefault(w => (w.UserId.ToString().StartsWith(userModel.Coupon) || w.Id.ToString().StartsWith(userModel.Coupon)) && w.IsCancelled == false);
                        if (coupon == null)
                        {
                            preloginReturnModel.UserType = 2;
                            preloginReturnModel.AuthUrl = "";
                            preloginReturnModel.SupportEmail = supportEmail;
                            preloginReturnModel.SupportPhone = supportPhone;
                            preloginReturnModel.IsLoginAllowed = false;
                            preloginReturnModel.IsInvalidCoupon = true;
                            preloginReturnModel.IsEmailValid = true;
                            preloginReturnModel.NotificarLoginBlocked = false;
                            preloginReturnModel.LoginType = 0;
                            return Ok(preloginReturnModel);
                        }
                        else if (coupon.IsRedeemed)
                        {
                            preloginReturnModel.UserType = 2;
                            preloginReturnModel.AuthUrl = "";
                            preloginReturnModel.SupportEmail = supportEmail;
                            preloginReturnModel.SupportPhone = supportPhone;
                            preloginReturnModel.IsLoginAllowed = true;
                            preloginReturnModel.IsInvalidCoupon = true;
                            preloginReturnModel.IsEmailValid = true;
                            preloginReturnModel.NotificarLoginBlocked = false;
                            preloginReturnModel.LoginType = 0;
                            return Ok(preloginReturnModel);
                        }
                    }
                    //if (userModel.AppId != Common.CommonData.OPAppId)
                    //{
                    //    preloginReturnModel.UserType = 4;
                    //    preloginReturnModel.AuthUrl = "";
                    //    preloginReturnModel.SupportEmail = supportEmail;
                    //    preloginReturnModel.SupportPhone = supportPhone;
                    //    preloginReturnModel.IsLoginAllowed = false;
                    //    preloginReturnModel.IsEmailValid = true;
                    //    preloginReturnModel.NotificarLoginBlocked = true;
                    //    preloginReturnModel.LoginType = 3;
                    //    return Ok(preloginReturnModel);
                    //}
                    bool emailWhiteListExist = wepdb.EmailWhiteLists.Any(w => w.Email.ToLower() == userModel.EmailAddress.ToLower() && w.IsActive == true);
                    if (emailWhiteListExist == false)
                    {
                        var ewl = new EmailWhiteList()
                        {
                            CreatedDate = DateTime.UtcNow,
                            Email = userModel.EmailAddress,
                            Id = Guid.NewGuid(),
                            IsActive = false,
                            IsClaimed = false,
                            ModifiedDate = DateTime.UtcNow
                        };
                        wepdb.EmailWhiteLists.Add(ewl);
                    }
                    var domain = userModel.EmailAddress.Split('@');
                    if (!emailWhiteListExist && (domain.Count() > 0) && (wepdb.DomainBlacklists.Select(W => W.DomainName.ToLower()).Contains(domain[1].ToLower())))
                    {
                        preloginReturnModel.UserType = 1;
                        preloginReturnModel.AuthUrl = "";
                        preloginReturnModel.SupportEmail = supportEmail;
                        preloginReturnModel.SupportPhone = supportPhone;
                        preloginReturnModel.IsLoginAllowed = true;
                        preloginReturnModel.IsEmailValid = true;
                        preloginReturnModel.IsFreeUser = true;
                        preloginReturnModel.NotificarLoginBlocked = true;
                        preloginReturnModel.LoginType = 4;
                        //  return Ok(preloginReturnModel);
                    }
                    var isValidEmail = await _authService.VerifyEmailByKickBox(userModel.EmailAddress);
                    //  var isValidEmail = await IsValidEmail(userModel.EmailAddress);
                    if (isValidEmail == true || emailWhiteListExist == true || istestuser == true || userModel.IsForced)
                    {
                        if (userModel.IsForced)
                        {
                            // send email
                            PropertyModels.ContactDataModel.UserLoginInfoModel emailData = new UserLoginInfoModel();
                            emailData.From = "<EMAIL>";
                            emailData.EmailId = userModel.EmailAddress;
                            emailData.Heading = "New Email WhiteListed!";
                            emailData.Message = " ";
                            emailData.Subject = "New Email WhiteListed!";
                            emailData.ToList.Add("<EMAIL>");
                            var output = EmailServices.SMTPSendEmail(emailData);

                        }
                        if (!(istestuser || emailWhiteListExist))
                        {
                            if (!wepdb.EmailWhiteLists.Any(w => w.Email == userModel.EmailAddress.ToLower()))
                            {
                                EmailWhiteList emailWhite = new EmailWhiteList
                                {
                                    Email = userModel.EmailAddress.ToLower(),
                                    IsActive = false,
                                    Id = Guid.NewGuid()
                                };
                                wepdb.EmailWhiteLists.Add(emailWhite);
                                wepdb.SaveChanges();
                            }


                            preloginReturnModel.UserType = 1;
                            preloginReturnModel.AuthUrl = authUrl;
                            preloginReturnModel.SupportEmail = supportEmail;
                            preloginReturnModel.SupportPhone = supportPhone;
                            preloginReturnModel.IsLoginAllowed = true;
                            preloginReturnModel.IsEmailValid = true;
                            preloginReturnModel.IsFreeUser = true;
                            preloginReturnModel.NotificarLoginBlocked = true;
                            preloginReturnModel.LoginType = 0;
                            //return Ok(preloginReturnModel);
                            //TODO: send email to admin
                        }

                        if (userModel.AppId == CommonData.MiofertaAppId || userModel.AppId == CommonData.NotificarAWSTestApp)
                        {
                            preloginReturnModel.UserType = 1;
                            preloginReturnModel.AuthUrl = "";
                            preloginReturnModel.SupportEmail = supportEmail;
                            preloginReturnModel.SupportPhone = supportPhone;
                            preloginReturnModel.IsLoginAllowed = true;
                            preloginReturnModel.IsEmailValid = true;
                            preloginReturnModel.NotificarLoginBlocked = true;
                            preloginReturnModel.LoginType = 0;
                            return Ok(preloginReturnModel);
                        }
                        Guid userId = Guid.NewGuid();
                        if (istestuser)
                        {
                            var encriptedPwd = CommonData.passwordEncryption(tempPassword);
                            tempPassword = encriptedPwd;
                        }

                        user = new User()
                        {
                            Id = userId,
                            Password = tempPassword,
                            CountryId = userModel.CountryId ?? "IN",
                            LastLogin = DateTime.UtcNow,
                            PreviousLogin = DateTime.UtcNow,
                            FirstLogin = DateTime.UtcNow,
                            CreatedDate = DateTime.UtcNow,
                            ModifiedDate = DateTime.UtcNow,
                            IsDisabled = false,
                            IsUser = true,
                            IsActive = true,
                            Notes = "",
                            AppId = userModel.AppId,
                            LanguageId = 1,
                            Email = email,
                            IsEmailValidated = false,
                            //IsPhoneVailidated = false,
                            AnalyticsId = Guid.NewGuid(),
                            IsTestUser = istestuser
                        };
                        //if (referredBy != "")
                        //{
                        //    user.InviteCode = userModel.Coupon;
                        //}
                        if (!string.IsNullOrEmpty(userModel.Coupon))
                        {
                            Models.Models.PeopleModel.Coupon coupon = wepdb.Coupons.FirstOrDefault(w => (w.UserId.ToString().StartsWith(userModel.Coupon) || w.Id.ToString().StartsWith(userModel.Coupon)) && w.IsCancelled == false);
                            if (coupon != null)
                            {
                                coupon.EventName = coupon.EventName + " - " + user.Email;
                                coupon.IsRedeemed = true;
                                if (coupon.UserId == null)
                                {
                                    user.InvitedBy = CommonData.AdminUserId;
                                    coupon.UserId = user.Id;
                                }
                                else
                                {
                                    user.InvitedBy = coupon.UserId.ToString();
                                }
                            }
                            else
                            {
                                UserLoginInfoModel model3 = new UserLoginInfoModel();
                                model3.EmailId = userModel.EmailAddress;
                                model3.From = "<EMAIL>";
                                model3.Heading = "Invalid coupon ";
                                model3.Message = sbnew.ToString();
                                model3.Subject = "Invalid coupon: ";
                                model3.ToList.Add("<EMAIL>");
                                await EmailServices.SMTPSendEmail(model3);

                                preloginReturnModel.UserType = 2;
                                preloginReturnModel.AuthUrl = "";
                                preloginReturnModel.SupportEmail = supportEmail;
                                preloginReturnModel.SupportPhone = supportPhone;
                                preloginReturnModel.IsLoginAllowed = false;
                                preloginReturnModel.IsInvalidCoupon = true;
                                preloginReturnModel.IsEmailValid = true;
                                preloginReturnModel.NotificarLoginBlocked = false;
                                preloginReturnModel.LoginType = 0;
                                return Ok(preloginReturnModel);
                            }
                        }


                        wepdb.Users.Add(user);
                        wepdb.SaveChanges();


                        Guid couponId = Guid.Empty;
                        if (!string.IsNullOrEmpty(userModel.Coupon))
                        {
                            var couponData = wepdb.Coupons.FirstOrDefault(w => (w.UserId.ToString().StartsWith(userModel.Coupon) || w.Id.ToString().StartsWith(userModel.Coupon)) && w.IsCancelled == false);
                            couponId = couponData.Id;
                        }
                        await checkAndApplyForCoupon(user, couponId);

                        string onetimeUrl = weadb.Servers.FirstOrDefault(w => w.ServerType == 16 && w.AppId.ToString().ToLower() == userModel.AppId.ToString().ToLower() && w.IsActive).Url;
                        onetimeUrl = onetimeUrl + "422B8390?SignalTypeId=E7ABEC3B-580A-4487-9384-32DDE1CF6A01&UserId=" + user.Id.ToString();
                        var result = CommonData.GenericMethod2(null, onetimeUrl, 1);

                        {
                            string emailDomain = userModel.EmailAddress.Split('@')[1].ToLower();
                            if (wepdb.Ssos.Any(w => w.Domain == emailDomain))
                            {
                                var ssoDomain = wepdb.Ssos.First(w => w.Domain == emailDomain);
                                preloginReturnModel.IsSSO = true;
                                preloginReturnModel.SSOURL = ssoDomain.Url;
                                user.Password = userModel.DeviceId.ToString();
                                wepdb.SaveChanges();
                            }
                            else
                            {
                                var client = new RestSharp.RestClient("https://api.workos.com/passwordless/sessions");
                                client.Timeout = -1;
                                var request = new RestRequest(Method.POST);
                                request.AddHeader("Authorization", "Bearer sk_m33fbMrSbQ6Swwky6sSAW0Aic");
                                request.AddHeader("Content-Type", "application/x-www-form-urlencoded");
                                request.AddParameter("email", userModel.EmailAddress);
                                request.AddParameter("type", "MagicLink");
                                request.AddParameter("state", userModel.DeviceId);
                                IRestResponse response = client.Execute(request);
                                await Task.Delay(2000);
                                JToken opToken = JsonConvert.DeserializeObject<JToken>(response.Content);
                                sbnew.AppendLine("Magic link token: " + opToken.ToString());
                                telemetryTracker.TrackEvent(opToken.ToString());
                                try
                                {
                                    if (opToken != null)
                                    {
                                        if (opToken["object"] != null && opToken["object"].Value<string>() == "passwordless_session")
                                        {
                                            string link = opToken["link"].Value<string>();
                                            string apiUrl = "https://api.mailmodo.com/api/v1/triggerCampaign/1225510f-b36b-4b54-a1eb-8f948101ced8";

                                            Payload payload = new Payload();
                                            payload.email = userModel.EmailAddress;
                                            DataModel model = new DataModel();
                                            model.url = link;
                                            payload.data = model;
                                            var result2 = await OnePage.Services.RestClient.Instance.MailModoPostAsync<string>(apiUrl, payload);
                                            sbnew.AppendLine("Mailmodo response: " + result2);
                                            //await EmailServices.SendMagicLinkOTPMail2(true, "", userModel.EmailAddress, true, link);
                                            user.Password = userModel.DeviceId.ToString();
                                        }
                                    }
                                    else
                                    {
                                        var client2 = new RestSharp.RestClient("https://api.workos.com/passwordless/sessions");
                                        client.Timeout = -1;
                                        var request2 = new RestRequest(Method.POST);
                                        request2.AddHeader("Authorization", "Bearer sk_m33fbMrSbQ6Swwky6sSAW0Aic");
                                        request2.AddHeader("Content-Type", "application/x-www-form-urlencoded");
                                        request2.AddParameter("email", userModel.EmailAddress);
                                        request2.AddParameter("type", "MagicLink");
                                        request2.AddParameter("state", userModel.DeviceId);
                                        IRestResponse response2 = client2.Execute(request2);
                                        await Task.Delay(2000);
                                        JToken opToken2 = JsonConvert.DeserializeObject<JToken>(response2.Content);
                                        sbnew.AppendLine("Magic link token: " + opToken2.ToString());
                                        telemetryTracker.TrackEvent(opToken2.ToString());
                                        if (opToken2 != null)
                                        {
                                            if (opToken2["object"] != null && opToken2["object"].Value<string>() == "passwordless_session")
                                            {
                                                string link = opToken2["link"].Value<string>();
                                                string apiUrl = "https://api.mailmodo.com/api/v1/triggerCampaign/1225510f-b36b-4b54-a1eb-8f948101ced8";

                                                Payload payload = new Payload();
                                                payload.email = userModel.EmailAddress;
                                                DataModel model = new DataModel();
                                                model.url = link;
                                                payload.data = model;
                                                var result2 = await OnePage.Services.RestClient.Instance.MailModoPostAsync<string>(apiUrl, payload);
                                                sbnew.AppendLine("Mailmodo response: " + result2);
                                                //await EmailServices.SendMagicLinkOTPMail2(true, "", userModel.EmailAddress, true, link);
                                                user.Password = userModel.DeviceId.ToString();
                                            }
                                        }
                                    }
                                }
                                catch (Exception ex)
                                {
                                    StringBuilder sb1 = new StringBuilder();
                                    sb1.AppendLine("Exception of magic link api: Message: " + ex.Message + ", StackTrace:" + ex.StackTrace + ", InnerException" + ex.InnerException);
                                    UserLoginInfoModel model = new UserLoginInfoModel();
                                    model.EmailId = userModel.EmailAddress;
                                    model.From = "<EMAIL>";
                                    model.Heading = "Api Exception  CAADA196 ";
                                    model.Message = sb1.ToString();
                                    model.Subject = "Api Exception  CAADA196  ";
                                    model.ToList.Add("<EMAIL>");
                                    await EmailServices.SMTPSendEmail(model);
                                }
                                wepdb.SaveChanges();
                            }
                        }
                        // send <NAME_EMAIL>
                        PropertyModels.ContactDataModel.UserLoginInfoModel userLoginInfoModel = new PropertyModels.ContactDataModel.UserLoginInfoModel();
                        userLoginInfoModel.EmailId = user.Email;
                        var res = EmailServices.SMTPSendNewUserLoginInfo(userLoginInfoModel);

                        //await Task.Run(() => AddAdditionalTableDataUsingEmail(userModel, email, userId, supportEmail, supportPhone, supportName));
                        AddAdditionalTableDataUsingEmail(userModel, email, userId, supportEmail, supportPhone, supportName);
                        UserLoginInfoModel model2 = new UserLoginInfoModel();
                        model2.EmailId = userModel.EmailAddress;
                        model2.From = "<EMAIL>";
                        model2.Heading = "Mail Vaildation ";
                        model2.Message = sbnew.ToString();
                        model2.Subject = "Mail Vaildation ";
                        model2.ToList.Add("<EMAIL>");
                        await EmailServices.SMTPSendEmail(model2);
                        preloginReturnModel.UserType = 1;
                        preloginReturnModel.AuthUrl = authUrl;
                        preloginReturnModel.SupportEmail = supportEmail;
                        preloginReturnModel.SupportPhone = supportPhone;
                        preloginReturnModel.IsLoginAllowed = true;
                        preloginReturnModel.IsEmailValid = true;
                        preloginReturnModel.NotificarLoginBlocked = true;
                        preloginReturnModel.LoginType = 0;
                        return Ok(preloginReturnModel); // New User 
                    }
                    else
                    {
                        StringBuilder sb1 = new StringBuilder();
                        sb1.AppendLine("Invalid Email: " + userModel.EmailAddress);
                        UserLoginInfoModel model2 = new UserLoginInfoModel();
                        model2.EmailId = userModel.EmailAddress;
                        model2.From = "<EMAIL>";
                        model2.Heading = "Invalid Email ";
                        model2.Message = sb1.ToString();
                        model2.Subject = "Invalid Email ";
                        model2.ToList.Add("<EMAIL>");
                        await EmailServices.SMTPSendEmail(model2);
                        preloginReturnModel.UserType = 4;
                        preloginReturnModel.AuthUrl = "";
                        preloginReturnModel.SupportEmail = supportEmail;
                        preloginReturnModel.SupportPhone = supportPhone;
                        preloginReturnModel.IsLoginAllowed = false;
                        preloginReturnModel.IsEmailValid = false;
                        preloginReturnModel.NotificarLoginBlocked = true;
                        preloginReturnModel.LoginType = 3;
                        return Ok(preloginReturnModel);
                    }
                }
            }
            catch (Exception ex)
            {
                StringBuilder sb1 = new StringBuilder();
                sb1.AppendLine("Exception of magic link api: Message: " + ex.Message + ", StackTrace:" + ex.StackTrace + ", InnerException" + ex.InnerException);
                UserLoginInfoModel model2 = new UserLoginInfoModel();
                model2.EmailId = userModel.EmailAddress;
                model2.From = "<EMAIL>";
                model2.Heading = "Api Exception  CAADA196 ";
                model2.Message = sb1.ToString();
                model2.Subject = "Api Exception  CAADA196  ";
                model2.ToList.Add("<EMAIL>");
                await EmailServices.SMTPSendEmail(model2);
                dictionary.Add("EndTime", DateTime.UtcNow.ToString());
                TimeSpan apiSpan = DateTime.UtcNow.Subtract(apiStart);
                dictionary.Add("TimeSpan", apiSpan.ToString());
                dictionary.Add("exception", ex.StackTrace.ToString());
                dictionary.Add("exception Message", ex.Message.ToString());
                telemetryTracker.TrackException(ex);
                return BadRequest(ex.ToString());
            }
        }
        public class Payload
        {
            public string email { get; set; }
            //  public string subject { get; set; }
            public DataModel data { get; set; }
        }
        public class DataModel
        {
            public string url { get; set; }
        }
        public async Task<bool> IsValidEmail(string EmailAddress)
        {
            bool result = false;
            if (string.IsNullOrWhiteSpace(EmailAddress) || string.IsNullOrEmpty(EmailAddress))
                result = false;
            else
            {
                string responseText = await CommonData.GenericMethod("", CommonData.NeverBounceApi + EmailAddress, 2); //await restClient.PostAsync<JToken>(CommonData.NeverBounceApi + EmailAddress, null);
                JToken response = JToken.Parse(responseText);// JsonConvert.SerializeObject(responseText);
                if (response != null)
                {
                    string validEmail = response.SelectToken("$.result").Value<string>();
                    if ((validEmail == "valid" || validEmail == "catchall") && response["flags"] != null && response["flags"].Type == JTokenType.Array)
                    {
                        result = true;
                        List<string> flagsList = JsonConvert.DeserializeObject<List<string>>(response["flags"].ToString());
                        if (flagsList.Count > 0)
                        {
                            if (flagsList.Contains("role_account") || flagsList.Contains("profanity") || flagsList.Contains("disposable_email") || flagsList.Contains("squatter_host") || flagsList.Contains("spelling_mistake") || flagsList.Contains("spamtrap_network"))
                            {
                                result = false;
                            }
                            else
                            {
                                result = true;
                            }
                        }
                    }
                    else
                        result = false;
                }
                else
                    result = false;
            }

            return result;
        }

        [AllowAnonymous]
        [Route("EBAF61D6")]
        [HttpPost]
        public IActionResult ValidateLinkedInUser(Guid deviceId)
        {
            TelegramService.SendMessageToTestBot2("api call: EBAF61D6");
            UserValidModel model = new UserValidModel
            {
                IsValid = false,
                AuthUrl = "",
                Email = "",
                UserId = Guid.Empty
            };
            try
            {
                string device = $"{deviceId.ToString().ToLower()}|1";
                if (wepdb.Users.Any(w => w.Password == device))
                {
                    User user = wepdb.Users.FirstOrDefault(w => w.Password == device);
                    model.IsValid = true;
                    model.AppId = user.AppId;
                    model.Email = user.Email;
                    var authUrl = weadb.Servers.FirstOrDefault(w => w.ServerType == 1 && w.AppId == user.AppId && w.IsActive).Url;
                    model.AuthUrl = authUrl;
                    return Ok(model);
                }
                else
                {
                    return Ok(model);
                }
            }
            catch (Exception ex)
            {
                telemetryTracker.TrackException(ex);
                Console.WriteLine(ex.ToString());
                return Ok(model);
            }
        }
        [AllowAnonymous]
        [Route("79EA411C")]
        [HttpPost]
        public async Task<IActionResult> ValidateMagicUser(Guid deviceId)
        {
            try
            {
                TelegramService.SendMessageToTestBot2("api call: 79EA411C");
                TelegramService.SendMessageToTestBot("validateMagic");
                string device = $"{deviceId.ToString().ToLower()}|1";
                if (await wepdb.Users.AnyAsync(w => w.Password.Contains(deviceId.ToString().ToLower())))
                {
                    var user = await wepdb.Users.Where(w => w.Password.Contains(deviceId.ToString().ToLower())).FirstOrDefaultAsync();
                    if (!string.IsNullOrEmpty(user.Email))
                    {
                        var domain = user.Email.Split('@')[1];
                        bool isFreeDomain1 = await weddb.FreeDomains.AsNoTracking().AnyAsync(x => x.DomainName.ToLower() == domain.ToLower());
                        if (isFreeDomain1 && !wepdb.EmailWhiteLists.Any(x => x.Email.ToLower() == user.Email.ToLower() && x.IsActive == true))
                        {
                            return BadRequest("This Domain is Not allowed");
                        }
                    }

                    return Ok(true);
                }
                else
                {
                    TelegramService.SendMessageToTestBot("response false");
                    return Ok(false);
                }
            }
            catch (Exception ex)
            {
                telemetryTracker.TrackException(ex);
                Console.WriteLine(ex.ToString());
                return BadRequest("User not validated");
            }
        }
        [AllowAnonymous]
        [Route("health")]
        [HttpGet]
        public IActionResult HealthCheck()
        {
            try
            {
                return Ok(new {
                    status = "healthy",
                    timestamp = DateTime.UtcNow,
                    environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown",
                    version = "1.0.0"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { status = "unhealthy", error = ex.Message });
            }
        }

        [AllowAnonymous]
        [Route("165C8F0E")]
        [HttpPost]
        public async Task<IActionResult> ValidateMagicLink([FromBody] ValidationModel validationModel)
        {
            try
            {
                TelegramService.SendMessageToTestBot2("api call: 165C8F0E");
                var email = validationModel.EmailAddress.ToLower().TrimStart().TrimEnd();
                var languageIdExist = CommonData.languageList.Any(w => w.Name == validationModel.LanguageId);
                if (email == null && languageIdExist == false)
                    return BadRequest();

                User existingUser = wepdb.Users.FirstOrDefault(w => w.Email == email && w.IsUser && w.AppId == validationModel.AppId);
                if (null != existingUser)
                {
                    var deviceType = weddb.DeviceTypes.FirstOrDefault(w => w.Id == validationModel.DeviceTypeId);

                    if (!string.IsNullOrEmpty(existingUser.Password) && !string.IsNullOrEmpty(validationModel.OTP))
                    {
                        if (existingUser.Password.ToLower() != $"{validationModel.OTP.ToLower()}|1")
                        {
                            return BadRequest("Wrong Password");
                        }
                    }

                    bool isTestUser = (bool)existingUser.IsTestUser;
                    //if (isTestUser == false)
                    //{
                    //    return BadRequest("We only allow business emails at the moment. Please sign in with business email");
                    //}
                    SetUserSettings(existingUser.Id);
                    SetOrgLevelSettings(existingUser.Id);

                    await CheckForSubscription(existingUser.Id);
                    TelegramService.SendMessageToGaganTestBot("2692");
                    // check for activations
                    var checkForActivation = weadb.Activations.Any(w => w.UserId == existingUser.Id && w.ActivationStatus != SubscriptionStatus.Activated.ToString() && w.ActivationStatus == SubscriptionStatus.Invited.ToString());
                    if (checkForActivation)
                    {
                        var activationData = weadb.Activations.FirstOrDefault(w => w.UserId == existingUser.Id);
                        activationData.ActivationStatus = SubscriptionStatus.Activated.ToString();
                        activationData.ActivatedDate = DateTime.UtcNow;
                        activationData.ModifiedDate = DateTime.UtcNow;
                        weadb.SaveChanges();

                        var subscriptionData = weadb.Subscriptionss.FirstOrDefault(w => w.AMPSubscriptionId == activationData.SubscriptionId);
                        if (subscriptionData != null)
                        {
                            Guid tasksProdId = Guid.Parse("08A4E0AA-12AB-40B1-9D8A-CDB8C75073E2");
                            if (subscriptionData.AMPPlanId == "professional-plan")
                            {
                                var userSettings1 = wepdb.UserSettings.Where(w => w.UserId == existingUser.Id && w.IsActive == true).ToList();
                                if (userSettings1.Count > 0)
                                {
                                    var vanishingCredits1 = userSettings1.First(w => w.SettingId == Guid.Parse("FB993427-3680-4FEB-832A-968E9A9BD802") && w.UserId == existingUser.Id && w.IsActive == true);
                                    var vCreditValues1 = int.Parse(vanishingCredits1.Value);
                                    await SetUserSettings(existingUser.Id, Guid.Parse("FB993427-3680-4FEB-832A-968E9A9BD802"), (vCreditValues1 + 100).ToString());
                                }
                                CreditActivity creditActivity1 = new CreditActivity();
                                creditActivity1.Id = Guid.NewGuid();
                                creditActivity1.UserId = existingUser.Id;
                                creditActivity1.Redeemdate = DateTime.UtcNow;
                                creditActivity1.IsActive = true;
                                creditActivity1.CreatedDate = DateTime.UtcNow;
                                creditActivity1.ModifiedDate = DateTime.UtcNow;
                                creditActivity1.Credit = 100;
                                creditActivity1.ReceiptId = DateTime.UtcNow.ToString();
                                creditActivity1.TransactionDateUtc = DateTime.UtcNow;
                                creditActivity1.ProductId = wepdb.InApps.First(w => w.Id == tasksProdId).ProductId;
                                creditActivity1.AutoRenewing = false;
                                wepdb.CreditActivities.Add(creditActivity1);
                                wepdb.SaveChanges();
                            }
                            else if (subscriptionData.AMPPlanId == "enterprise-plan")
                            {
                                var userSettings1 = wepdb.UserSettings.Where(w => w.UserId == existingUser.Id && w.IsActive == true).ToList();
                                if (userSettings1.Count > 0)
                                {
                                    var vanishingCredits1 = userSettings1.First(w => w.SettingId == Guid.Parse("FB993427-3680-4FEB-832A-968E9A9BD802") && w.UserId == existingUser.Id && w.IsActive == true);
                                    var vCreditValues1 = int.Parse(vanishingCredits1.Value);
                                    await SetUserSettings(existingUser.Id, Guid.Parse("FB993427-3680-4FEB-832A-968E9A9BD802"), (vCreditValues1 + 200).ToString());
                                }
                                CreditActivity creditActivity1 = new CreditActivity();
                                creditActivity1.Id = Guid.NewGuid();
                                creditActivity1.UserId = existingUser.Id;
                                creditActivity1.Redeemdate = DateTime.UtcNow;
                                creditActivity1.IsActive = true;
                                creditActivity1.CreatedDate = DateTime.UtcNow;
                                creditActivity1.ModifiedDate = DateTime.UtcNow;
                                creditActivity1.Credit = 200;
                                creditActivity1.ReceiptId = DateTime.UtcNow.ToString();
                                creditActivity1.TransactionDateUtc = DateTime.UtcNow;
                                creditActivity1.ProductId = wepdb.InApps.First(w => w.Id == tasksProdId).ProductId;
                                creditActivity1.AutoRenewing = false;
                                wepdb.CreditActivities.Add(creditActivity1);
                                wepdb.SaveChanges();
                            }

                            // create signal to reset credits
                            //Guid resetCreditSignalTypeId = Guid.Parse("76A9C5B6-EF98-4393-88C4-D299A958FC01");
                            //var checkForExistingSignal = wepdb.Signals.Any(w => w.UserId == existingUser.Id && w.SignalTypeId == resetCreditSignalTypeId);
                            //if (!checkForExistingSignal)
                            //{
                            //    Guid TimeZoneSettingId = Guid.Parse("1D9BC314-786C-40E4-9FE4-769860C58F02");
                            //    var timeZoneUserSetting = wepdb.UserSettings.First(w => w.UserId == existingUser.Id && w.SettingId == TimeZoneSettingId).Value;
                            //    int hourToAdd = 0;
                            //    int minutesToAdd = 0; // 05:30:00
                            //    if (!string.IsNullOrEmpty(timeZoneUserSetting))
                            //    {
                            //        var split = timeZoneUserSetting.Split(":");
                            //        hourToAdd = int.Parse(split[0]);
                            //        minutesToAdd = int.Parse(split[1]);
                            //    }
                            //    var checkTime1 = DateTime.UtcNow.AddHours(hourToAdd);
                            //    var today = checkTime1.AddMinutes(minutesToAdd);
                            //    var nextMonth = today.Month + 1;
                            //    // create signal
                            //    Signal signal = new Signal();
                            //    signal.Id = Guid.NewGuid();
                            //    signal.CreatedDate = DateTime.UtcNow;
                            //    signal.IsActive = true;
                            //    signal.IsComplete = false;
                            //    signal.IsSeen = false;
                            //    signal.Iteration = 1;
                            //    signal.ModifiedDate = DateTime.UtcNow;
                            //    signal.SignalTypeId = resetCreditSignalTypeId;
                            //    double minutes = 0;
                            //    signal.TriggerOn = new DateTime(today.Year, nextMonth, 1);
                            //    signal.UserId = existingUser.Id;
                            //    wepdb.Signals.Add(signal);
                            //    wepdb.SaveChanges();
                            //}
                        }
                    }
                    // create signal for calendar sync
                    Guid calendarSyncSignalTypeId = Guid.Parse("EC1615D6-14C3-405A-8012-F319700DA703");
                    var checkForExistingSyncSignal = wepdb.Signals.Any(w => w.UserId == existingUser.Id && w.SignalTypeId == calendarSyncSignalTypeId && w.IsActive == true);
                    if (!checkForExistingSyncSignal)
                    {
                        // create signal
                        Signal signal = new Signal();
                        signal.Id = Guid.NewGuid();
                        signal.CreatedDate = DateTime.UtcNow;
                        signal.IsActive = true;
                        signal.IsComplete = false;
                        signal.IsSeen = false;
                        signal.Iteration = 1;
                        signal.ModifiedDate = DateTime.UtcNow;
                        signal.SignalTypeId = calendarSyncSignalTypeId;
                        signal.TriggerOn = DateTime.UtcNow.AddMinutes(1);
                        signal.UserId = existingUser.Id;
                        wepdb.Signals.Add(signal);
                        wepdb.SaveChanges();
                    }

                    // create signal for calendar sync
                    Guid calendarSync2SignalTypeId = Guid.Parse("EC1615D6-14C3-405A-8012-F319700DA704");
                    var checkForExistingSyncSignal2 = wepdb.Signals.Any(w => w.UserId == existingUser.Id && w.SignalTypeId == calendarSync2SignalTypeId && w.IsActive == true);
                    if (!checkForExistingSyncSignal2)
                    {
                        Signal signal = new Signal();
                        signal.Id = Guid.NewGuid();
                        signal.CreatedDate = DateTime.UtcNow;
                        signal.IsActive = true;
                        signal.IsComplete = false;
                        signal.IsSeen = false;
                        signal.Iteration = 1;
                        signal.ModifiedDate = DateTime.UtcNow;
                        signal.SignalTypeId = calendarSync2SignalTypeId;
                        signal.TriggerOn = DateTime.UtcNow.AddMinutes(1);
                        signal.UserId = existingUser.Id;
                        wepdb.Signals.Add(signal);
                        wepdb.SaveChanges();
                    }
                    Guid calendarSync3SignalTypeId = Guid.Parse("A2103281-6926-47A1-B3DD-87531D6DE401");
                    var checkForExistingSyncSignal3 = wepdb.Signals.Any(w => w.UserId == existingUser.Id && w.SignalTypeId == calendarSync3SignalTypeId && w.IsActive == true);
                    if (!checkForExistingSyncSignal2)
                    {
                        Signal signal = new Signal();
                        signal.Id = Guid.NewGuid();
                        signal.CreatedDate = DateTime.UtcNow;
                        signal.IsActive = true;
                        signal.IsComplete = false;
                        signal.IsSeen = false;
                        signal.Iteration = 1;
                        signal.ModifiedDate = DateTime.UtcNow;
                        signal.SignalTypeId = calendarSync3SignalTypeId;
                        signal.TriggerOn = DateTime.UtcNow.AddMinutes(1);
                        signal.UserId = existingUser.Id;
                        wepdb.Signals.Add(signal);
                        wepdb.SaveChanges();
                    }
                    TelegramService.SendMessageToGaganTestBot("morning point");
                    if ((existingUser.AppId == CommonData.OPV2AppId || existingUser.AppId == OPEnterpriseAppId || existingUser.AppId == CommonData.GlobalAppId) && existingUser.IsTestUser == false)
                    {
                        var isMorningEmailSettingIdExists = wepdb.Signals.Any(w => w.UserId == existingUser.Id && w.SignalTypeId == CommonData.MorningTriggerMailSignalTypeId);
                        if (!isMorningEmailSettingIdExists)
                        {
                            try
                            {
                                TelegramService.SendMessageToTestBot2("morning signalType not exists");
                                var timeZoneUserSetting = wepdb.UserSettings.First(w => w.UserId == existingUser.Id && w.SettingId == CommonData.TimeZoneSettingId).Value;
                                int hourToAdd = 0;
                                int minutesToAdd = 0; // 05:30:00
                                if (!string.IsNullOrEmpty(timeZoneUserSetting))
                                {
                                    var split = timeZoneUserSetting.Split(":");
                                    hourToAdd = int.Parse(split[0]);
                                    minutesToAdd = int.Parse(split[1]);
                                }

                                Guid morningMailTimeSettingId = Guid.Parse("5A593F51-1CE7-43AF-A4CD-0ECB233DA205");
                                var userTimeSettingValue = wepdb.UserSettings.FirstOrDefault(w => w.UserId == existingUser.Id && w.SettingId == morningMailTimeSettingId);
                                int morningHours = 0;
                                int morningMinutes = 0;
                                if (userTimeSettingValue != null && !string.IsNullOrEmpty(userTimeSettingValue.Value))
                                {
                                    try
                                    {
                                        var userTimeValueSplit = userTimeSettingValue.Value.Split(":");
                                        morningHours = int.Parse(userTimeValueSplit[0]);
                                        morningMinutes = int.Parse(userTimeValueSplit[1]);
                                    }
                                    catch (Exception ex)
                                    {
                                        morningHours = 8;
                                        morningMinutes = 0;
                                    }
                                }
                                else
                                {
                                    morningHours = 8;
                                    morningMinutes = 0;
                                }
                                var timeZones = TimeZoneInfo.GetSystemTimeZones();
                                TimeSpan targetOffset = new TimeSpan(hourToAdd, minutesToAdd, 0);
                                var matchingTimeZones = timeZones.Where(w => w.BaseUtcOffset == targetOffset).FirstOrDefault();

                                TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(matchingTimeZones.Id);

                                DateTimeOffset userTime = TimeZoneInfo.ConvertTime(DateTimeOffset.UtcNow, userTimeZone);
                                userTime = userTime.AddHours(24);
                                var targetTime = new DateTimeOffset(userTime.Year, userTime.Month, userTime.Day, morningHours, morningMinutes, 0, userTime.Offset).UtcDateTime;
                                // create signal
                                Signal signal = new Signal();
                                signal.Id = Guid.NewGuid();
                                signal.CreatedDate = DateTime.UtcNow;
                                signal.IsActive = true;
                                signal.IsComplete = false;
                                signal.IsSeen = false;
                                signal.Iteration = 1;
                                signal.ModifiedDate = DateTime.UtcNow;
                                signal.SignalTypeId = CommonData.MorningTriggerMailSignalTypeId;
                                signal.TriggerOn = targetTime;
                                signal.UserId = existingUser.Id;
                                wepdb.Signals.Add(signal);
                                wepdb.SaveChanges();
                            }
                            catch (Exception ex)
                            {

                            }
                        }

                        var isEveningEmailSettingIdExists = wepdb.Signals.Any(w => w.UserId == existingUser.Id && w.SignalTypeId == CommonData.EveningTriggerMailSignalTypeId);
                        if (!isEveningEmailSettingIdExists)
                        {
                            var timeZoneUserSetting = wepdb.UserSettings.First(w => w.UserId == existingUser.Id && w.SettingId == CommonData.TimeZoneSettingId).Value;
                            int hourToAdd = 0;
                            int minutesToAdd = 0; // 05:30:00
                            if (!string.IsNullOrEmpty(timeZoneUserSetting))
                            {
                                var split = timeZoneUserSetting.Split(":");
                                hourToAdd = int.Parse(split[0]);
                                minutesToAdd = int.Parse(split[1]);
                            }
                            Guid eveningMailTimeSettingId = Guid.Parse("5A593F51-1CE7-43AF-A4CD-0ECB233DA206");
                            var userTimeSettingValue = wepdb.UserSettings.FirstOrDefault(w => w.UserId == existingUser.Id && w.SettingId == eveningMailTimeSettingId);
                            int eveningHours = 0;
                            int eveningMinutes = 0;
                            if (userTimeSettingValue != null && !string.IsNullOrEmpty(userTimeSettingValue.Value))
                            {
                                try
                                {
                                    var userTimeValueSplit = userTimeSettingValue.Value.Split(":");
                                    eveningHours = int.Parse(userTimeValueSplit[0]);
                                    eveningMinutes = int.Parse(userTimeValueSplit[1]);
                                }
                                catch (Exception ex)
                                {
                                    eveningHours = 18;
                                    eveningMinutes = 0;
                                }
                            }
                            else
                            {
                                eveningHours = 18;
                                eveningMinutes = 0;
                            }
                            var timeZones = TimeZoneInfo.GetSystemTimeZones();
                            TimeSpan targetOffset = new TimeSpan(hourToAdd, minutesToAdd, 0);
                            var matchingTimeZones = timeZones.Where(w => w.BaseUtcOffset == targetOffset).FirstOrDefault();

                            TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(matchingTimeZones.Id);

                            DateTimeOffset userTime = TimeZoneInfo.ConvertTime(DateTimeOffset.UtcNow, userTimeZone);
                            var targetTime = new DateTimeOffset(userTime.Year, userTime.Month, userTime.Day, eveningHours, eveningMinutes, 0, userTime.Offset).UtcDateTime;
                            // create signal
                            Signal signal = new Signal();
                            signal.Id = Guid.NewGuid();
                            signal.CreatedDate = DateTime.UtcNow;
                            signal.IsActive = true;
                            signal.IsComplete = false;
                            signal.IsSeen = false;
                            signal.Iteration = 1;
                            signal.ModifiedDate = DateTime.UtcNow;
                            signal.SignalTypeId = CommonData.EveningTriggerMailSignalTypeId;
                            signal.TriggerOn = targetTime;
                            signal.UserId = existingUser.Id;
                            wepdb.Signals.Add(signal);
                            wepdb.SaveChanges();
                        }
                    }
                    var checkForTrialDateSetting = wepdb.UserSettings.FirstOrDefault(w => w.UserId == existingUser.Id && w.SettingId == CommonData.TrialDateSettingId && w.IsActive == true);
                    if (checkForTrialDateSetting != null)
                    {
                        var trialDateSettingValue = checkForTrialDateSetting.Value;
                        if (trialDateSettingValue == "" || trialDateSettingValue == "0")
                        {
                            trialDateSettingValue = existingUser.CreatedDate.ToString();
                            wepdb.SaveChanges();
                        }
                    }

                    var totalCreditSettingDefaultValue = wepdb.Settings.FirstOrDefault(w => w.Id == CommonData.TotalCreditsSettings);
                    var defaultTotalCreditValue = totalCreditSettingDefaultValue.DefaultValue.Split('|');
                    var currentPlanExists = wepdb.UserSettings.Any(w => w.UserId == existingUser.Id && w.SettingId == CommonData.PurchasedPlanSettingId && w.IsActive == true);
                    if (currentPlanExists)
                    {
                        var currentPlan = wepdb.UserSettings.FirstOrDefault(w => w.UserId == existingUser.Id && w.SettingId == PurchasedPlanSettingId && w.IsActive == true);
                        if (currentPlan.Value == "Professional")
                        {
                            await SetUserSettings(existingUser.Id, CommonData.TotalCreditsSettings, defaultTotalCreditValue[1]);
                        }
                        else if (currentPlan.Value == "Enterprise")
                        {
                            await SetUserSettings(existingUser.Id, CommonData.TotalCreditsSettings, defaultTotalCreditValue[2]);
                        }
                        else
                        {
                            await SetUserSettings(existingUser.Id, CommonData.TotalCreditsSettings, defaultTotalCreditValue[0]);
                        }
                    }
                    else
                    {
                        await SetUserSettings(existingUser.Id, CommonData.TotalCreditsSettings, defaultTotalCreditValue[0]);
                    }
                    var coupons = wepdb.Coupons.Where(w => w.EventName.Contains(validationModel.EmailAddress) && w.IsRedeemed == true && w.IsCancelled == false).FirstOrDefault();
                    if (coupons != null)
                    {
                        coupons.UserId = existingUser.Id;
                        wepdb.SaveChanges();
                        var onboarding = new OnboardController(wepdb, weddb, weadb, wetdb, weodb, _authService);
                        onboarding.ApplyOffer(existingUser.Id, coupons.Id);

                    }
                    var returnModel = new ValidateReturnModel();
                    var userStatus = wepdb.UserStatuses.FirstOrDefault(w => w.UserId == existingUser.Id);
                    if (userStatus != null)
                    {
                        userStatus.Otp = true;
                        userStatus.Walkthrough = true;
                        wepdb.SaveChanges();
                    }

                    UserStatusModel userStatusData = wepdb.UserStatuses.Where(w => w.UserId == existingUser.Id).Select(w => new UserStatusModel()
                    {
                        Id = w.Id,
                        AndroidContacts = w.AndroidContacts,
                        FacebookContacts = w.FacebookContacts,
                        FreeEmail = w.FreeEmail,
                        FreeLinkedIn = w.FreeLinkedIn,
                        FreeStorage = w.FreeStorage,
                        iOSContacts = w.IOscontacts,
                        FreeTwitter = w.FreeTwitter,
                        LinkedInContacts = w.LinkedInContacts,
                        MobileVerification = w.MobileVerification,
                        OTP = w.Otp,
                        ProvisionApp = w.ProvisionApp,
                        PurchaseEmail = w.PurchaseEmail,
                        PurchaseEnterpriseApp = w.PurchaseEnterpriseApp,
                        PurchaseEnterpriseEmail = w.PurchaseEnterpriseApp,
                        PurchaseEnterpriseStorage = w.PurchaseEnterpriseStorage,
                        PurchaseStorage = w.PurchaseStorage,
                        Registration = w.Registration,
                        TwitterContacts = w.TwitterContacts,
                        Walkthrough = w.Walkthrough,
                        ShouldShowAds = (bool)w.ShouldShowAds,
                    }).FirstOrDefault();
                    existingUser.PreviousLogin = existingUser.LastLogin;
                    //existingUser.IsPhoneValidated = false;
                    existingUser.IsEmailValidated = true;
                    existingUser.LastLogin = DateTime.UtcNow;
                    existingUser.ModifiedDate = DateTime.UtcNow;
                    existingUser.IsUser = true;
                    if (existingUser.FirstLogin == null)
                        existingUser.FirstLogin = DateTime.UtcNow;
                    Models.Models.TokenModel.Token token = new Models.Models.TokenModel.Token()
                    {
                        Id = Guid.NewGuid(),
                        UserId = existingUser.Id,
                        IsActive = true,
                        CreatedDate = DateTime.UtcNow,
                        ModifiedDate = DateTime.UtcNow,
                        Ttl = DateTime.UtcNow.AddDays(100),
                        AppId = existingUser.AppId,
                        UserAgent = "",
                        UserDeviceId = validationModel.DeviceId
                    };
                    Models.Models.TokenModel.Token webtoken = new Models.Models.TokenModel.Token()
                    {
                        Id = Guid.NewGuid(),
                        UserId = existingUser.Id,
                        IsActive = true,
                        CreatedDate = DateTime.UtcNow,
                        ModifiedDate = DateTime.UtcNow,
                        Ttl = DateTime.UtcNow.AddDays(100),
                        AppId = existingUser.AppId,
                        UserAgent = "",
                        UserDeviceId = validationModel.DeviceId,
                        IsWeb = true
                    };
                    Models.Models.TokenModel.Token webhooktoken = new Models.Models.TokenModel.Token()
                    {
                        Id = Guid.NewGuid(),
                        UserId = existingUser.Id,
                        IsActive = true,
                        CreatedDate = DateTime.UtcNow,
                        ModifiedDate = DateTime.UtcNow,
                        Ttl = DateTime.UtcNow.AddDays(100),
                        AppId = existingUser.AppId,
                        UserAgent = "",
                        Details = "Webhook",
                        UserDeviceId = validationModel.DeviceId,
                        IsWeb = false
                    };
                    wetdb.Tokens.Add(webtoken);
                    wetdb.Tokens.Add(webhooktoken);
                    wetdb.Tokens.Add(token);
                    wetdb.SaveChanges();
                    wepdb.SaveChanges();
                    //Task.Run(() => UpdateOtherTablesUsingEmail(validationModel, email, existingUser));
                    UpdateOtherTablesUsingEmail(validationModel, email, existingUser, token);

                    // int notificationCount = db.MergeContacts.Count(w => w.OtherUserId == existingUser.Id && w.IsActive);
                    var domainName = existingUser.Email.Split('@');
                    var domain = domainName[1];
                    //try
                    //{
                    //    if (!domain.Contains("mrawesomeapps.com") && !domain.Contains("mapit4.me") && !domain.Contains("whatelse.io"))
                    //    {

                    //        if (existingUser.ChargebeeCustomerId != null && existingUser.ChargebeeCustomerId.ToString() != Guid.Empty.ToString())
                    //        {
                    //            try
                    //            {
                    //                var msg = "User DeviceType : " + deviceType.Name;
                    //                ApiConfig.Configure("1Page", "live_Tf3jmCoHTjMZ1cdzlEzuLRK8qsQ6cudxaF");
                    //                EntityResult customerExist = Customer.Retrieve(existingUser.ChargebeeCustomerId).Request();
                    //                Customer customer = customerExist.Customer;
                    //                if (customer != null)
                    //                {
                    //                    EntityResult result1 = Comment.Create()
                    //                          .EntityId(customer.Id)
                    //                          .EntityType(ChargeBee.Models.Enums.EntityTypeEnum.Customer)
                    //                          .Notes(msg)
                    //                          .Request();
                    //                }
                    //            }
                    //            catch (Exception ex)
                    //            {
                    //                telemetryTracker.TrackException(ex);
                    //            }
                    //        }
                    //        else
                    //        {
                    //            //TODO: revisit Chargebee
                    //            ApiConfig.Configure("1Page", "live_Tf3jmCoHTjMZ1cdzlEzuLRK8qsQ6cudxaF");
                    //            EntityResult result = ChargeBee.Models.Customer.Create()
                    //                              .FirstName(existingUser.FirstName)
                    //                              .LastName(existingUser.LastName)
                    //                              .Email(existingUser.Email)
                    //                              .Company(existingUser.Company).Request();
                    //            ChargeBee.Models.Customer customer = result.Customer;
                    //            ChargeBee.Models.Card card = result.Card;
                    //            existingUser.ChargebeeCustomerId = customer.Id;
                    //            wepdb.SaveChanges();
                    //            long seconds = DateTimeHelpers.ToEpoch(DateTime.UtcNow);
                    //            EntityResult result2 = Subscription.CreateForCustomer(customer.Id)
                    //                                   .PlanId("free")
                    //                                   .StartDate(seconds)
                    //                                   .Request();

                    //            Subscription subscription = result2.Subscription;
                    //            Customer customer2 = result2.Customer;
                    //            ChargeBee.Models.Card card2 = result2.Card;
                    //            Invoice invoice = result2.Invoice;
                    //            List<UnbilledCharge> unbilledCharges = result2.UnbilledCharges;
                    //            var msg = "User DeviceType : " + deviceType.Name;
                    //            EntityResult result1 = Comment.Create()
                    //                            .EntityId(customer.Id)
                    //                            .EntityType(ChargeBee.Models.Enums.EntityTypeEnum.Customer)
                    //                            .Notes(msg)
                    //                            .Request();

                    //            existingUser.ChargebeeCustomerId = Guid.Empty.ToString();

                    //            //TODO: freshsales user
                    //            //Creating intercom user
                    //            //var intercomModel = new IntercomModel();
                    //            //intercomModel.role = "user";
                    //            //intercomModel.email = existingUser.Email;
                    //            //intercomModel.name = existingUser.FirstName + " " + existingUser.LastName;

                    //            //var intercomUrl = "https://api.intercom.io/contacts";
                    //            //var access_token = "dG9rOmU5MmIxYmMyX2IyNTFfNDkwMl9hNzcxX2Y5MTAyODY5ZDBlNjoxOjA=";
                    //            //var json = JsonConvert.SerializeObject(intercomModel);
                    //            //var res = IntercomRestApi(json, intercomUrl, access_token);
                    //        }
                    //    }
                    //}
                    //catch (Exception ex)
                    //{
                    //    telemetryTracker.TrackException(ex);
                    //    Console.WriteLine(ex.ToString());
                    //}

                    returnModel.TokenId = token.Id;
                    returnModel.UserId = existingUser.Id;
                    returnModel.UserStatusModel = userStatusData;
                    returnModel.UserContactId = Guid.Empty;
                    returnModel.NotificationCount = 0;//notificationCount;
                    returnModel.LanguageId = validationModel.LanguageId;
                    returnModel.UnknownPush = false;
                    returnModel.ContactPush = false;
                    returnModel.CRMPush = false;
                    returnModel.DirectoryPush = false;
                    returnModel.IsTestUser = isTestUser;
                    returnModel.FirstName = existingUser.FirstName;
                    returnModel.LastName = existingUser.LastName;
                    returnModel.AnalyticsId = existingUser.AnalyticsId;
                    //returnModel.IncomingCall = existingUser.IncomingCall ?? false;
                    //returnModel.IncomingCallEnd = existingUser.IncomingCallEnd ?? false;
                    //returnModel.OutgoingCall = existingUser.OutgoingCall ?? false;
                    //returnModel.OutgoingCallEnd = existingUser.OutgoingCallEnd ?? false;
                    //returnModel.ExotelExtension = existingUser.ExotelExtension ?? "";
                    //returnModel.ExotelNumber = existingUser.ExotelNumber ?? "";
                    returnModel.WebToken = webtoken.Id;
                    returnModel.WebhookToken = webhooktoken.Id;
                    returnModel.InvitedBy = existingUser.InvitedBy;
                    //returnModel.phoneConfigsModel = existingUser.PhoneConfigs.Where(W => W.UserId == existingUser.Id && W.IsActive).Select(w => new PhoneConfig() { DID = w.DID, Extn = w.Extn, PhoneNumber = w.PhoneNumber, ProviderId = w.ProviderId, SIPAddress = w.SIPAddress, Prefix = w.Prefix, InstanceUrl = w.InstanceUrl, ApiKey = w.ApiKey, Name = w.Name }).ToList();
                    returnModel.CallNotifications = false;
                    returnModel.IsInternal = false;
                    try
                    {
                        var orguser = weodb.OrgUsers.Where(w => w.UserId == existingUser.Id && w.Org.Name == domain && w.Org.IsInternal).FirstOrDefault();
                        if (orguser != null)
                            returnModel.IsInternal = orguser.Org.IsInternal;
                    }
                    catch (Exception ex)
                    {
                        telemetryTracker.TrackException(ex);
                        Console.WriteLine(ex.ToString());
                    }
                    returnModel.OrgIds = weodb.OrgUsers.Where(w => w.UserId == existingUser.Id && w.IsActive).Select(w => w.OrgId).Distinct().ToList();

                    //if (existingUser.CallNotifications)
                    //{
                    //    returnModel.UnknownPush = true;
                    //    returnModel.ContactPush = true;
                    //    returnModel.CRMPush = true;
                    //    returnModel.DirectoryPush = true;
                    //}
                    returnModel.CalendarNotifications = true;
                    returnModel.ServerUrlModel = weadb.Servers.Where(w => w.IsActive && w.AppId == existingUser.AppId).Select(w => new ServerUrlModel()
                    {
                        ServerType = w.ServerType,
                        url = w.Url
                    }).ToList();
                    return Ok(returnModel);//new { Token = token.Id, Id = existingUser.Id }
                    //}
                }
                else
                {
                    TelegramService.SendMessageToTestBot2("existing user null");
                    return BadRequest();
                }
            }
            catch (Exception ex)
            {
                //   TelegramService.SendMessageToTestBot2(ex.ToString());
                telemetryTracker.TrackException(ex);
                return BadRequest(ex.ToString());
            }
        }
        async Task SetUserSettings(Guid userId, Guid settingId, string settingValue)
        {
            try
            {
                bool IsPaidUser = weodb.OrgUsers.Any(w => w.UserId == userId && w.Org.IsPaid == true);
                var setting = wepdb.Settings.First(w => w.Id == settingId);
                if (!setting.IsAdmin)
                {
                    if (wepdb.UserSettings.Any(w => w.UserId == userId && w.SettingId == settingId))
                    {
                        var existing = wepdb.UserSettings.First(w => w.UserId == userId && w.SettingId == settingId);
                        existing.Value = settingValue;
                        existing.IsActive = true;
                        existing.ModifiedDate = DateTime.UtcNow;
                    }
                    else
                    {
                        var userSetting = new UserSetting
                        {
                            Id = Guid.NewGuid(),
                            IsActive = true,
                            SettingId = settingId,
                            CreatedDate = DateTime.UtcNow,
                            ModifiedDate = DateTime.UtcNow,
                            UserId = userId,
                            DataType = setting.DataType,
                            Value = settingValue
                        };
                        wepdb.UserSettings.Add(userSetting);
                    }
                    wepdb.SaveChanges();
                }
            }
            catch (Exception ex)
            {
                telemetryTracker.TrackException(ex);
            }
        }

        public async Task CheckForSubscription(Guid userId)
        {
            try
            {
                Guid providerId = Guid.Parse("327915F0-677A-444C-99A8-1B4998A4623C");
                var orgId = Guid.Parse("97859466-D85E-47E7-ABB5-56CF76335318");
                //var redeemed = wepdb.UserProviders.FirstOrDefault(w => w.ProviderId == providerId && w.UserId == userId && w.IsActive == true);
                var redeemed = await _userProviderCaching.GetUserProvidersFromRedisOrDbAsyncWithUserId(userId, providerId);
                Provider provider = weddb.Providers
                     .Include(a => a.ProviderUrls).ThenInclude(w => w.Url).ThenInclude(w => w.Headers)
                     .Include(a => a.ProviderUrls).ThenInclude(w => w.Url).ThenInclude(w => w.Posts)
                     .First(w => w.Id == providerId);
                var identifiersToSend = await providerService.GetIdentifiersForUserProvider(redeemed);

                Url userRightsUrl = provider.ProviderUrls.Where(w => w.Url.ShowOrder == 12400 && w.IsActive.Value).Select(w => w.Url).FirstOrDefault();
                if (userRightsUrl != null)
                {
                    var output = await providerService.MakeRequest<JToken>(userRightsUrl, redeemed, identifiersToSend);
                    List<JToken> outputTokens = output["value"].ToList();
                    if (outputTokens.Count > 0)
                    {
                        foreach (var outputItem in outputTokens.ToList())
                        {
                            try
                            {
                                Guid tasksProdId = Guid.Parse("08A4E0AA-12AB-40B1-9D8A-CDB8C75073E2");
                                Guid PurchaseFromSettingId = Guid.Parse("CA508D5D-700E-4315-A0CD-F19BD46BBF18");
                                Guid PurchasedPlanSettingId = Guid.Parse("CA508D5D-700E-4315-A0CD-F19BD46BBF19");
                                Guid PaymentCompleteSettingId = Guid.Parse("CA508D5D-700E-4315-A0CD-F19BD46BBF11");
                                string userRightId = outputItem.SelectToken("$.id").Value<string>();
                                string catalogId = outputItem.SelectToken("$.catalogId").Value<string>();
                                string serviceIdentifier = outputItem.SelectToken("$.serviceIdentifier").Value<string>();
                                string stateValue = outputItem.SelectToken("$.state").Value<string>();
                                if (stateValue == "active")
                                {
                                    bool shouldContinue = true;
                                    var checkForPaymentComplete = wepdb.UserSettings.FirstOrDefault(w => w.UserId == userId && w.SettingId == PaymentCompleteSettingId && w.IsActive == true);
                                    if (checkForPaymentComplete != null)
                                    {
                                        var paymentCompleteSetting = wepdb.UserSettings.FirstOrDefault(w => w.UserId == userId && w.SettingId == PaymentCompleteSettingId && w.IsActive == true);
                                        if (paymentCompleteSetting.Value == "1")
                                        {
                                            shouldContinue = false;
                                        }
                                        else
                                        {
                                            shouldContinue = true;
                                        }
                                    }
                                    if (shouldContinue == false)
                                        return;
                                    await SetUserSettings(userId, PurchaseFromSettingId, "Microsoft");
                                    await SetUserSettings(userId, PaymentCompleteSettingId, "1");
                                    if (serviceIdentifier.Contains("professional"))
                                    {
                                        await SetUserSettings(userId, PurchasedPlanSettingId, "Professional");
                                        var userSettings1 = wepdb.UserSettings.Where(w => w.UserId == userId && w.IsActive == true).ToList();
                                        if (userSettings1.Count > 0)
                                        {
                                            var vanishingCredits1 = userSettings1.First(w => w.SettingId == Guid.Parse("FB993427-3680-4FEB-832A-968E9A9BD802") && w.UserId == userId && w.IsActive == true);
                                            var vCreditValues1 = int.Parse(vanishingCredits1.Value);
                                            await SetUserSettings(userId, Guid.Parse("FB993427-3680-4FEB-832A-968E9A9BD802"), (vCreditValues1 + 100).ToString());
                                        }
                                        CreditActivity creditActivity1 = new CreditActivity();
                                        creditActivity1.Id = Guid.NewGuid();
                                        creditActivity1.UserId = userId;
                                        creditActivity1.Redeemdate = DateTime.UtcNow;
                                        creditActivity1.IsActive = true;
                                        creditActivity1.CreatedDate = DateTime.UtcNow;
                                        creditActivity1.ModifiedDate = DateTime.UtcNow;
                                        creditActivity1.Credit = 100;
                                        creditActivity1.ReceiptId = DateTime.UtcNow.ToString();
                                        creditActivity1.TransactionDateUtc = DateTime.UtcNow;
                                        creditActivity1.ProductId = wepdb.InApps.First(w => w.Id == tasksProdId).ProductId;
                                        creditActivity1.AutoRenewing = false;
                                        wepdb.CreditActivities.Add(creditActivity1);
                                        wepdb.SaveChanges();
                                    }
                                    else if (serviceIdentifier.Contains("enterprise"))
                                    {
                                        await SetUserSettings(userId, PurchasedPlanSettingId, "Enterprise");
                                        var userSettings1 = wepdb.UserSettings.Where(w => w.UserId == userId && w.IsActive == true).ToList();
                                        if (userSettings1.Count > 0)
                                        {
                                            var vanishingCredits1 = userSettings1.First(w => w.SettingId == Guid.Parse("FB993427-3680-4FEB-832A-968E9A9BD802") && w.UserId == userId && w.IsActive == true);
                                            var vCreditValues1 = int.Parse(vanishingCredits1.Value);
                                            await SetUserSettings(userId, Guid.Parse("FB993427-3680-4FEB-832A-968E9A9BD802"), (vCreditValues1 + 200).ToString());
                                        }
                                        CreditActivity creditActivity1 = new CreditActivity();
                                        creditActivity1.Id = Guid.NewGuid();
                                        creditActivity1.UserId = userId;
                                        creditActivity1.Redeemdate = DateTime.UtcNow;
                                        creditActivity1.IsActive = true;
                                        creditActivity1.CreatedDate = DateTime.UtcNow;
                                        creditActivity1.ModifiedDate = DateTime.UtcNow;
                                        creditActivity1.Credit = 200;
                                        creditActivity1.ReceiptId = DateTime.UtcNow.ToString();
                                        creditActivity1.TransactionDateUtc = DateTime.UtcNow;
                                        creditActivity1.ProductId = wepdb.InApps.First(w => w.Id == tasksProdId).ProductId;
                                        creditActivity1.AutoRenewing = false;
                                        wepdb.CreditActivities.Add(creditActivity1);
                                        wepdb.SaveChanges();
                                    }
                                    else if (serviceIdentifier.Contains("salesperson"))
                                    {
                                        await SetUserSettings(userId, PurchasedPlanSettingId, "Salesperson");
                                        var userSettings1 = wepdb.UserSettings.Where(w => w.UserId == userId && w.IsActive == true).ToList();
                                        if (userSettings1.Count > 0)
                                        {
                                            var vanishingCredits1 = userSettings1.First(w => w.SettingId == Guid.Parse("FB993427-3680-4FEB-832A-968E9A9BD802") && w.UserId == userId && w.IsActive == true);
                                            var vCreditValues1 = int.Parse(vanishingCredits1.Value);
                                            await SetUserSettings(userId, Guid.Parse("FB993427-3680-4FEB-832A-968E9A9BD802"), (vCreditValues1 + 200).ToString());
                                        }
                                        CreditActivity creditActivity1 = new CreditActivity();
                                        creditActivity1.Id = Guid.NewGuid();
                                        creditActivity1.UserId = userId;
                                        creditActivity1.Redeemdate = DateTime.UtcNow;
                                        creditActivity1.IsActive = true;
                                        creditActivity1.CreatedDate = DateTime.UtcNow;
                                        creditActivity1.ModifiedDate = DateTime.UtcNow;
                                        creditActivity1.Credit = 200;
                                        creditActivity1.ReceiptId = DateTime.UtcNow.ToString();
                                        creditActivity1.TransactionDateUtc = DateTime.UtcNow;
                                        creditActivity1.ProductId = wepdb.InApps.First(w => w.Id == tasksProdId).ProductId;
                                        creditActivity1.AutoRenewing = false;
                                        wepdb.CreditActivities.Add(creditActivity1);
                                        wepdb.SaveChanges();
                                    }
                                }
                                else if (stateValue == "inactive")
                                {
                                    Guid TrialCompleteSettingId = Guid.Parse("CA508D5D-700E-4315-A0CD-F19BD46BBF10");
                                    await SetUserSettings(userId, PaymentCompleteSettingId, "0");
                                    await SetUserSettings(userId, PurchasedPlanSettingId, "");
                                    var isTrialComplete = wepdb.UserSettings.FirstOrDefault(w => w.SettingId == TrialCompleteSettingId && w.UserId == userId && w.IsActive == true);
                                    if (isTrialComplete != null)
                                    {
                                        if (isTrialComplete.Value == "1")
                                        {
                                            await SetUserSettings(userId, Guid.Parse("FB993427-3680-4FEB-832A-968E9A9BD802"), "0");
                                        }
                                        else if (isTrialComplete.Value == "0")
                                        {
                                            await SetUserSettings(userId, Guid.Parse("FB993427-3680-4FEB-832A-968E9A9BD802"), "10");
                                        }
                                    }
                                }
                            }
                            catch (Exception ex)
                            {

                            }
                        }
                    }
                }

            }
            catch (Exception ex)
            {

            }
        }
        [AllowAnonymous]
        [Route("EA83DF34")]
        [HttpPost]
        public IActionResult ValidateMagicLinkWithIP([FromBody] ValidationModelWithIP validationModel)
        {
            try
            {
                TelegramService.SendMessageToTestBot2("api call: EA83DF34");
                var email = validationModel.EmailAddress.ToLower().TrimStart().TrimEnd();
                var languageIdExist = CommonData.languageList.Any(w => w.Name == validationModel.LanguageId);
                if (email == null && languageIdExist == false)
                    return BadRequest();

                string dt = "";
                switch (validationModel.DeviceTypeId.ToString())
                {

                    case "74acf511-0be3-4648-8148-0acd1eac9264":
                        dt = "Opera Extn";
                        break;
                    case "5c620e97-0694-471a-9452-19b5a33338f6":
                        dt = "Edge Extn";
                        break;
                    case "a3bff2d0-9e01-4c8b-8c78-1f93f597af66":
                        dt = "Tizen Phone";
                        break;
                    case "eb04e9bc-cf02-4035-91c4-2a2ce3c3a5f4":
                        dt = "Chrome Extn";
                        break;
                    case "85bf95b2-2315-4c59-9ef9-47f33cfa65ac":
                        dt = "iPhone";
                        break;
                    case "db959c80-a012-448b-a23f-512eb3e07783":
                        dt = "Safari";
                        break;
                    case "12b321fb-720f-44dc-a6a0-5c888b40f563":
                        dt = "Mac OS";
                        break;
                    case "65d709ac-0841-4493-8fe0-6294e068a207":
                        dt = "Apple TV";
                        break;
                    case "0d93384e-2ee0-4589-bbfa-658379513bcc":
                        dt = "Android TV";
                        break;
                    case "c4560541-a3cd-4ff4-8199-682953d888b2":
                        dt = "Firefox Extn";
                        break;
                    case "a59e15e8-e1bf-41a4-8876-75ce8963904c":
                        dt = "Apple Watch";
                        break;
                    case "f771a11b-4ec9-45bd-b9f7-7884d6990e84":
                        dt = "Tizen Watch";
                        break;
                    case "8dfe90e8-6f2c-4a60-be04-830261556f6b":
                        dt = "Device";
                        break;
                    case "121d9c03-c1d8-490b-8026-9d4bd2690970":
                        dt = "Androd Gear";
                        break;
                    case "7f606a20-a033-4dde-8aca-bf342614a8f3":
                        dt = "Windows";
                        break;
                    case "8322fb32-2a4b-45c9-9af0-bf56a9df1a13":
                        dt = "XBox";
                        break;
                    case "1e49b16c-6cfe-427c-9a71-c91a0fd1fc69":
                        dt = "Android Phone";
                        break;
                    case "51b4c888-9e76-40d8-a290-d34f09961c32":
                        dt = "Safari Extn";
                        break;
                    case "9c329792-9519-4d6c-be9d-f12898113416":
                        dt = "Website";
                        break;
                    case "22bfd022-a73a-42de-9714-f26c8e81741f":
                        dt = "Tizen TV";
                        break;
                    case "467aae32-8361-43a3-a8c7-be21e32c4f3b":
                        dt = "Teams";
                        break;
                    case "9e9f203e-0820-4d58-840e-503edc28b505":
                        dt = "Customer Admin Portal";
                        break;
                    default:
                        dt = "Unknown Device!";
                        break;
                }

                User existingUser = new User();
                if (!string.IsNullOrEmpty(validationModel.EmailAddress))
                {
                    try
                    {
                        existingUser = _redisCaching.CheckForItem("userDetail_" + email + "_" + validationModel.AppId, () => wepdb.Users.FirstOrDefault(w => w.Email == email && w.IsUser && w.AppId == validationModel.AppId), CustomJsonSerializer.Serialize, JsonConvert.DeserializeObject<User>, DateTimeOffset.Now.AddDays(1));
                    }
                    catch (Exception ex)
                    {
                        existingUser = wepdb.Users.FirstOrDefault(w => w.Email == email && w.IsUser && w.AppId == validationModel.AppId);
                    }
                }
                else if (validationModel.DeviceId != Guid.Empty)
                {
                    existingUser = wepdb.Users.FirstOrDefault(w => w.Password.ToLower().Contains(validationModel.DeviceId.ToString().ToLower()) && w.IsUser && w.AppId == validationModel.AppId);
                }

                if (null != existingUser)
                {
                    var deviceType = weddb.DeviceTypes.FirstOrDefault(w => w.Id == validationModel.DeviceTypeId);

                    if (!string.IsNullOrEmpty(existingUser.Password) && !string.IsNullOrEmpty(validationModel.OTP))
                    {
                        if (existingUser.Password.ToLower() != $"{validationModel.OTP.ToLower()}|1")
                        {
                            return BadRequest("Wrong Password");
                        }
                    }

                    bool isTestUser = (bool)existingUser.IsTestUser;
                    SetUserSettings(existingUser.Id);

                    var coupons = wepdb.Coupons.Where(w => w.EventName.Contains(validationModel.EmailAddress) && w.IsRedeemed == true && w.IsCancelled == false).FirstOrDefault();
                    if (coupons != null)
                    {
                        coupons.UserId = existingUser.Id;
                        wepdb.SaveChanges();
                        var onboarding = new OnboardController(wepdb, weddb, weadb, wetdb, weodb, _authService);
                        onboarding.ApplyOffer(existingUser.Id, coupons.Id);

                    }
                    //TelegramService.SendMessage("Existing user trying to login: " + existingUser.Email + ", " + "Device Type: " + dt);
                    var returnModel = new ValidateReturnModel();
                    wepdb.UserStatuses.FirstOrDefault(w => w.UserId == existingUser.Id).Otp = true;
                    wepdb.UserStatuses.FirstOrDefault(w => w.UserId == existingUser.Id).Walkthrough = true;
                    wepdb.SaveChanges();
                    UserStatusModel userStatusData = wepdb.UserStatuses.Where(w => w.UserId == existingUser.Id).Select(w => new UserStatusModel()
                    {
                        Id = w.Id,
                        AndroidContacts = w.AndroidContacts,
                        FacebookContacts = w.FacebookContacts,
                        FreeEmail = w.FreeEmail,
                        FreeLinkedIn = w.FreeLinkedIn,
                        FreeStorage = w.FreeStorage,
                        iOSContacts = w.IOscontacts,
                        FreeTwitter = w.FreeTwitter,
                        LinkedInContacts = w.LinkedInContacts,
                        MobileVerification = w.MobileVerification,
                        OTP = w.Otp,
                        ProvisionApp = w.ProvisionApp,
                        PurchaseEmail = w.PurchaseEmail,
                        PurchaseEnterpriseApp = w.PurchaseEnterpriseApp,
                        PurchaseEnterpriseEmail = w.PurchaseEnterpriseApp,
                        PurchaseEnterpriseStorage = w.PurchaseEnterpriseStorage,
                        PurchaseStorage = w.PurchaseStorage,
                        Registration = w.Registration,
                        TwitterContacts = w.TwitterContacts,
                        Walkthrough = w.Walkthrough,
                        ShouldShowAds = (bool)w.ShouldShowAds,
                    }).FirstOrDefault();
                    existingUser.PreviousLogin = existingUser.LastLogin;
                    //existingUser.IsPhoneValidated = false;
                    existingUser.IsEmailValidated = true;
                    existingUser.LastLogin = DateTime.UtcNow;
                    existingUser.ModifiedDate = DateTime.UtcNow;
                    existingUser.IsUser = true;
                    if (existingUser.FirstLogin == null)
                        existingUser.FirstLogin = DateTime.UtcNow;
                    Models.Models.TokenModel.Token token = new Models.Models.TokenModel.Token()
                    {
                        Id = Guid.NewGuid(),
                        UserId = existingUser.Id,
                        IsActive = true,
                        CreatedDate = DateTime.UtcNow,
                        ModifiedDate = DateTime.UtcNow,
                        Ttl = DateTime.UtcNow.AddDays(100),
                        AppId = existingUser.AppId,
                        UserAgent = "",
                        Ip = validationModel.IPAdress,
                        BrowserData = validationModel.BrowserData,
                        UserDeviceId = validationModel.DeviceId
                    };
                    Models.Models.TokenModel.Token webtoken = new Models.Models.TokenModel.Token()
                    {
                        Id = Guid.NewGuid(),
                        UserId = existingUser.Id,
                        IsActive = true,
                        CreatedDate = DateTime.UtcNow,
                        ModifiedDate = DateTime.UtcNow,
                        Ttl = DateTime.UtcNow.AddDays(100),
                        AppId = existingUser.AppId,
                        UserAgent = "",
                        Ip = validationModel.IPAdress,
                        BrowserData = validationModel.BrowserData,
                        UserDeviceId = validationModel.DeviceId,
                        IsWeb = true
                    };
                    Models.Models.TokenModel.Token webhooktoken = new Models.Models.TokenModel.Token()
                    {
                        Id = Guid.NewGuid(),
                        UserId = existingUser.Id,
                        IsActive = true,
                        CreatedDate = DateTime.UtcNow,
                        ModifiedDate = DateTime.UtcNow,
                        Ttl = DateTime.UtcNow.AddDays(100),
                        AppId = existingUser.AppId,
                        UserAgent = "",
                        Details = "Webhook",
                        Ip = validationModel.IPAdress,
                        BrowserData = validationModel.BrowserData,
                        UserDeviceId = validationModel.DeviceId,
                        IsWeb = false
                    };
                    wetdb.Tokens.Add(webtoken);
                    wetdb.Tokens.Add(webhooktoken);
                    wetdb.Tokens.Add(token);
                    wetdb.SaveChanges();
                    wepdb.SaveChanges();
                    //Task.Run(() => UpdateOtherTablesUsingEmail(validationModel, email, existingUser));
                    UpdateOtherTablesUsingEmailIP(validationModel, existingUser.Email, existingUser, token);

                    // int notificationCount = db.MergeContacts.Count(w => w.OtherUserId == existingUser.Id && w.IsActive);
                    var domainName = existingUser.Email.Split('@');
                    var domain = domainName[1];
                    //try
                    //{
                    //    if (!domain.Contains("mrawesomeapps.com") && !domain.Contains("mapit4.me") && !domain.Contains("whatelse.io"))
                    //    {

                    //        if (existingUser.ChargebeeCustomerId != null && existingUser.ChargebeeCustomerId.ToString() != Guid.Empty.ToString())
                    //        {
                    //            try
                    //            {
                    //                var msg = "User DeviceType : " + deviceType.Name;
                    //                ApiConfig.Configure("1Page", "live_Tf3jmCoHTjMZ1cdzlEzuLRK8qsQ6cudxaF");
                    //                EntityResult customerExist = Customer.Retrieve(existingUser.ChargebeeCustomerId).Request();
                    //                Customer customer = customerExist.Customer;
                    //                if (customer != null)
                    //                {
                    //                    EntityResult result1 = Comment.Create()
                    //                          .EntityId(customer.Id)
                    //                          .EntityType(ChargeBee.Models.Enums.EntityTypeEnum.Customer)
                    //                          .Notes(msg)
                    //                          .Request();
                    //                }
                    //            }
                    //            catch (Exception ex)
                    //            {
                    //                telemetryTracker.TrackException(ex);
                    //            }
                    //        }
                    //        else
                    //        {
                    //            //TODO: revisit Chargebee
                    //            ApiConfig.Configure("1Page", "live_Tf3jmCoHTjMZ1cdzlEzuLRK8qsQ6cudxaF");
                    //            EntityResult result = ChargeBee.Models.Customer.Create()
                    //                              .FirstName(existingUser.FirstName)
                    //                              .LastName(existingUser.LastName)
                    //                              .Email(existingUser.Email)
                    //                              .Company(existingUser.Company).Request();
                    //            ChargeBee.Models.Customer customer = result.Customer;
                    //            ChargeBee.Models.Card card = result.Card;
                    //            existingUser.ChargebeeCustomerId = customer.Id;
                    //            wepdb.SaveChanges();
                    //            long seconds = DateTimeHelpers.ToEpoch(DateTime.UtcNow);
                    //            EntityResult result2 = Subscription.CreateForCustomer(customer.Id)
                    //                                   .PlanId("free")
                    //                                   .StartDate(seconds)
                    //                                   .Request();

                    //            Subscription subscription = result2.Subscription;
                    //            Customer customer2 = result2.Customer;
                    //            ChargeBee.Models.Card card2 = result2.Card;
                    //            Invoice invoice = result2.Invoice;
                    //            List<UnbilledCharge> unbilledCharges = result2.UnbilledCharges;
                    //            var msg = "User DeviceType : " + deviceType.Name;
                    //            EntityResult result1 = Comment.Create()
                    //                            .EntityId(customer.Id)
                    //                            .EntityType(ChargeBee.Models.Enums.EntityTypeEnum.Customer)
                    //                            .Notes(msg)
                    //                            .Request();

                    //            existingUser.ChargebeeCustomerId = Guid.Empty.ToString();

                    //            //TODO: freshsales user
                    //            //Creating intercom user
                    //            //var intercomModel = new IntercomModel();
                    //            //intercomModel.role = "user";
                    //            //intercomModel.email = existingUser.Email;
                    //            //intercomModel.name = existingUser.FirstName + " " + existingUser.LastName;

                    //            //var intercomUrl = "https://api.intercom.io/contacts";
                    //            //var access_token = "dG9rOmU5MmIxYmMyX2IyNTFfNDkwMl9hNzcxX2Y5MTAyODY5ZDBlNjoxOjA=";
                    //            //var json = JsonConvert.SerializeObject(intercomModel);
                    //            //var res = IntercomRestApi(json, intercomUrl, access_token);
                    //        }
                    //    }
                    //}
                    //catch (Exception ex)
                    //{
                    //    telemetryTracker.TrackException(ex);
                    //    Console.WriteLine(ex.ToString());
                    //}

                    returnModel.TokenId = token.Id;
                    returnModel.UserId = existingUser.Id;
                    returnModel.UserStatusModel = userStatusData;
                    returnModel.UserContactId = Guid.Empty;
                    returnModel.NotificationCount = 0;//notificationCount;
                    returnModel.LanguageId = validationModel.LanguageId;
                    returnModel.UnknownPush = false;
                    returnModel.ContactPush = false;
                    returnModel.CRMPush = false;
                    returnModel.DirectoryPush = false;
                    returnModel.IsTestUser = isTestUser;
                    returnModel.FirstName = existingUser.FirstName;
                    returnModel.LastName = existingUser.LastName;
                    returnModel.AnalyticsId = existingUser.AnalyticsId;
                    //returnModel.IncomingCall = existingUser.IncomingCall ?? false;
                    //returnModel.IncomingCallEnd = existingUser.IncomingCallEnd ?? false;
                    //returnModel.OutgoingCall = existingUser.OutgoingCall ?? false;
                    //returnModel.OutgoingCallEnd = existingUser.OutgoingCallEnd ?? false;
                    //returnModel.ExotelExtension = existingUser.ExotelExtension ?? "";
                    //returnModel.ExotelNumber = existingUser.ExotelNumber ?? "";
                    returnModel.WebToken = webtoken.Id;
                    returnModel.WebhookToken = webhooktoken.Id;
                    returnModel.InvitedBy = existingUser.InvitedBy;
                    //returnModel.phoneConfigsModel = existingUser.PhoneConfigs.Where(W => W.UserId == existingUser.Id && W.IsActive).Select(w => new PhoneConfig() { DID = w.DID, Extn = w.Extn, PhoneNumber = w.PhoneNumber, ProviderId = w.ProviderId, SIPAddress = w.SIPAddress, Prefix = w.Prefix, InstanceUrl = w.InstanceUrl, ApiKey = w.ApiKey, Name = w.Name }).ToList();
                    returnModel.CallNotifications = false;
                    returnModel.IsInternal = false;
                    try
                    {
                        var orguser = weodb.OrgUsers.Where(w => w.UserId == existingUser.Id && w.Org.Name == domain && w.Org.IsInternal).FirstOrDefault();
                        if (orguser != null)
                            returnModel.IsInternal = orguser.Org.IsInternal;
                    }
                    catch (Exception ex)
                    {
                        telemetryTracker.TrackException(ex);
                        Console.WriteLine(ex.ToString());
                    }
                    returnModel.OrgIds = weodb.OrgUsers.Where(w => w.UserId == existingUser.Id && w.IsActive).Select(w => w.OrgId).Distinct().ToList();

                    //if (existingUser.CallNotifications)
                    //{
                    //    returnModel.UnknownPush = true;
                    //    returnModel.ContactPush = true;
                    //    returnModel.CRMPush = true;
                    //    returnModel.DirectoryPush = true;
                    //}
                    returnModel.CalendarNotifications = true;
                    returnModel.ServerUrlModel = weadb.Servers.Where(w => w.IsActive && w.AppId == existingUser.AppId).Select(w => new ServerUrlModel()
                    {
                        ServerType = w.ServerType,
                        url = w.Url
                    }).ToList();

                    return Ok(returnModel); //new { Token = token.Id, Id = existingUser.Id }
                    //}
                }
                else
                {
                    return BadRequest();
                }
            }
            catch (Exception ex)
            {
                telemetryTracker.TrackException(ex);
                return BadRequest(ex.ToString());
            }
        }


        [AllowAnonymous]
        [Route("153BD4CD")]
        [HttpPost]
        public async Task<IActionResult> ValidateForZoomUser(string uid)
        {
            try
            {
                TelegramService.SendMessageToTestBot2("api call: 153BD4CD");
                Response.Headers.Add("X-Frame-Options", "DENY");
                Response.Headers.Add("X-Content-Type-Options", "nosniff");
                Response.Headers.Add("Content-Security-Policy", "default-src 'self'");
                Response.Headers.Add("Referrer-Policy", "no-referrer");
                //Response.Headers.Add("Strict-Transport-Security", "max-age=31536000; includeSubDomains");
                var upiExists = wepdb.UserProviderIdentifiers.Any(w => w.Value == uid);
                if (upiExists)
                {
                    // Guid zRefreshTokenIId = Guid.Parse("87180d8f-b3d7-425f-a6a3-f793d93e4fa5");
                    Guid userId = Guid.Empty;
                    string zRefreshTokenValue = "";
                    var upis = await wepdb.UserProviderIdentifiers.Where(w => w.Value == uid && w.IsActive == true).ToListAsync();
                    foreach (var upi in upis)
                    {
                        var up = await wepdb.UserProviders.FirstOrDefaultAsync(w => w.Id == upi.UserProviderId && w.IsActive == true && w.AppId == GlobalAppId);
                        if (up != null)
                        {
                            userId = up.UserId;
                            break;
                        }
                    }
                    // zRefreshTokenValue = wepdb.UserProviderIdentifiers.First(w => w.IdentifierId == zRefreshTokenIId).Value;
                    User existingUser = _redisCaching.CheckForItem("userDetail_" + userId, () => wepdb.Users.FirstOrDefault(w => w.Id == userId), CustomJsonSerializer.Serialize, JsonConvert.DeserializeObject<User>, DateTimeOffset.Now.AddDays(1));

                    Guid deviceId = new Guid();
                    var passwordArray = existingUser.Password.Split('|');
                    if (passwordArray.Length > 0)
                    {
                        deviceId = Guid.Parse(passwordArray[0]);
                    }
                    string dt = "";
                    var dTypeId = "32372a70-84a5-4390-8071-8f89bcf9c6fa";
                    switch (dTypeId)
                    {

                        case "74acf511-0be3-4648-8148-0acd1eac9264":
                            dt = "Opera Extn";
                            break;
                        case "5c620e97-0694-471a-9452-19b5a33338f6":
                            dt = "Edge Extn";
                            break;
                        case "a3bff2d0-9e01-4c8b-8c78-1f93f597af66":
                            dt = "Tizen Phone";
                            break;
                        case "eb04e9bc-cf02-4035-91c4-2a2ce3c3a5f4":
                            dt = "Chrome Extn";
                            break;
                        case "85bf95b2-2315-4c59-9ef9-47f33cfa65ac":
                            dt = "iPhone";
                            break;
                        case "db959c80-a012-448b-a23f-512eb3e07783":
                            dt = "Safari";
                            break;
                        case "12b321fb-720f-44dc-a6a0-5c888b40f563":
                            dt = "Mac OS";
                            break;
                        case "65d709ac-0841-4493-8fe0-6294e068a207":
                            dt = "Apple TV";
                            break;
                        case "0d93384e-2ee0-4589-bbfa-658379513bcc":
                            dt = "Android TV";
                            break;
                        case "c4560541-a3cd-4ff4-8199-682953d888b2":
                            dt = "Firefox Extn";
                            break;
                        case "a59e15e8-e1bf-41a4-8876-75ce8963904c":
                            dt = "Apple Watch";
                            break;
                        case "f771a11b-4ec9-45bd-b9f7-7884d6990e84":
                            dt = "Tizen Watch";
                            break;
                        case "8dfe90e8-6f2c-4a60-be04-830261556f6b":
                            dt = "Device";
                            break;
                        case "121d9c03-c1d8-490b-8026-9d4bd2690970":
                            dt = "Androd Gear";
                            break;
                        case "7f606a20-a033-4dde-8aca-bf342614a8f3":
                            dt = "Windows";
                            break;
                        case "8322fb32-2a4b-45c9-9af0-bf56a9df1a13":
                            dt = "XBox";
                            break;
                        case "1e49b16c-6cfe-427c-9a71-c91a0fd1fc69":
                            dt = "Android Phone";
                            break;
                        case "51b4c888-9e76-40d8-a290-d34f09961c32":
                            dt = "Safari Extn";
                            break;
                        case "9c329792-9519-4d6c-be9d-f12898113416":
                            dt = "Website";
                            break;
                        case "22bfd022-a73a-42de-9714-f26c8e81741f":
                            dt = "Tizen TV";
                            break;
                        case "467aae32-8361-43a3-a8c7-be21e32c4f3b":
                            dt = "Teams";
                            break;
                        case "32372a70-84a5-4390-8071-8f89bcf9c6fa":
                            dt = "Zoom";
                            break;
                        case "9e9f203e-0820-4d58-840e-503edc28b505":
                            dt = "Customer Admin Portal";
                            break;
                        default:
                            dt = "Unknown Device!";
                            break;
                    }

                    if (null != existingUser)
                    {
                        SetUserSettings(existingUser.Id);
                        // TelegramService.SendMessage(existingUser.Email + " logged into Device Type: " + dt);
                        var returnModel = new ValidateReturnModelForSocial();
                        var us = await wepdb.UserStatuses.FirstOrDefaultAsync(w => w.UserId == existingUser.Id);
                        us.Otp = true;
                        us.Walkthrough = true;
                        await wepdb.SaveChangesAsync();
                        UserStatusModel userStatusData = await wepdb.UserStatuses.Where(w => w.UserId == existingUser.Id).Select(w => new UserStatusModel()
                        {
                            Id = w.Id,
                            AndroidContacts = w.AndroidContacts,
                            FacebookContacts = w.FacebookContacts,
                            FreeEmail = w.FreeEmail,
                            FreeLinkedIn = w.FreeLinkedIn,
                            FreeStorage = w.FreeStorage,
                            iOSContacts = w.IOscontacts,
                            FreeTwitter = w.FreeTwitter,
                            LinkedInContacts = w.LinkedInContacts,
                            MobileVerification = w.MobileVerification,
                            OTP = w.Otp,
                            ProvisionApp = w.ProvisionApp,
                            PurchaseEmail = w.PurchaseEmail,
                            PurchaseEnterpriseApp = w.PurchaseEnterpriseApp,
                            PurchaseEnterpriseEmail = w.PurchaseEnterpriseApp,
                            PurchaseEnterpriseStorage = w.PurchaseEnterpriseStorage,
                            PurchaseStorage = w.PurchaseStorage,
                            Registration = w.Registration,
                            TwitterContacts = w.TwitterContacts,
                            Walkthrough = w.Walkthrough,
                            ShouldShowAds = (bool)w.ShouldShowAds,
                        }).FirstOrDefaultAsync();
                        existingUser.PreviousLogin = existingUser.LastLogin;
                        //existingUser.IsPhoneValidated = false;
                        existingUser.IsEmailValidated = true;
                        existingUser.LastLogin = DateTime.UtcNow;
                        existingUser.ModifiedDate = DateTime.UtcNow;
                        existingUser.IsUser = true;
                        if (existingUser.FirstLogin == null)
                            existingUser.FirstLogin = DateTime.UtcNow;
                        Models.Models.TokenModel.Token token = new Models.Models.TokenModel.Token()
                        {
                            Id = Guid.NewGuid(),
                            UserId = existingUser.Id,
                            IsActive = true,
                            CreatedDate = DateTime.UtcNow,
                            ModifiedDate = DateTime.UtcNow,
                            Ttl = DateTime.UtcNow.AddDays(100),
                            AppId = existingUser.AppId,
                            UserAgent = "",
                            Ip = "",
                            BrowserData = "",
                            UserDeviceId = deviceId
                        };
                        Models.Models.TokenModel.Token webtoken = new Models.Models.TokenModel.Token()
                        {
                            Id = Guid.NewGuid(),
                            UserId = existingUser.Id,
                            IsActive = true,
                            CreatedDate = DateTime.UtcNow,
                            ModifiedDate = DateTime.UtcNow,
                            Ttl = DateTime.UtcNow.AddDays(100),
                            AppId = existingUser.AppId,
                            UserAgent = "",
                            Ip = "",
                            BrowserData = "",
                            UserDeviceId = deviceId,
                            IsWeb = true
                        };
                        Models.Models.TokenModel.Token webhooktoken = new Models.Models.TokenModel.Token()
                        {
                            Id = Guid.NewGuid(),
                            UserId = existingUser.Id,
                            IsActive = true,
                            CreatedDate = DateTime.UtcNow,
                            ModifiedDate = DateTime.UtcNow,
                            Ttl = DateTime.UtcNow.AddDays(100),
                            AppId = existingUser.AppId,
                            UserAgent = "",
                            Details = "Webhook",
                            Ip = "",
                            BrowserData = "",
                            UserDeviceId = deviceId,
                            IsWeb = false
                        };
                        wetdb.Tokens.Add(webtoken);
                        wetdb.Tokens.Add(webhooktoken);
                        wetdb.Tokens.Add(token);
                        await wetdb.SaveChangesAsync();
                        await wepdb.SaveChangesAsync();

                        ValidationModelWithIP validationModel = new ValidationModelWithIP();
                        validationModel.IPAdress = "";
                        validationModel.AppId = CommonData.GlobalAppId;
                        validationModel.OTP = "";
                        validationModel.Coupon = "";
                        validationModel.IsPrimaryDevice = true;
                        validationModel.DeviceId = deviceId;
                        validationModel.DeviceData = "";
                        validationModel.BrowserData = "";
                        validationModel.CountryId = "";
                        validationModel.EmailAddress = existingUser.Email;
                        validationModel.IsPreAuthenticated = true;
                        validationModel.LanguageId = "English (US)";
                        validationModel.PhoneNumber = "";
                        validationModel.DeviceTypeId = Guid.Parse(dTypeId);
                        UpdateOtherTablesUsingEmailIP(validationModel, existingUser.Email, existingUser, token);

                        var domainName = existingUser.Email.Split('@');
                        var domain = domainName[1];
                        var calendarUserProviderExists = wepdb.UserProviders.Any(w => w.UserId == existingUser.Id && (w.ProviderId == CommonData.MicrosoftProviderId || w.ProviderId == CommonData.GoogleCalendarProviderId || w.ProviderId == CommonData.ZohoCalendarProviderId || w.ProviderId == CommonData.CalendlyProviderId || w.ProviderId == CommonData.CalendarHeroProviderId) && w.IsActive == true);
                        var slice = existingUser.Email.Split('@');
                        var orgDetail = await weodb.Orgs
                            .Where(x => x.AppId.ToLower() == existingUser.AppId.ToString().ToLower() &&
                                        x.IsActive == true && x.Name.ToLower() == domain.ToLower()).FirstOrDefaultAsync();
                        if (orgDetail != null)
                        {
                            if (orgDetail.IsPaid == true)
                            {
                                returnModel.WebOneSignalId = orgDetail.WebOneSignalId;
                                returnModel.WebOneSignalKey = orgDetail.WebOneSignalKey;
                            }
                            else
                            {

                                orgDetail = weodb.Orgs.Where(x => x.Id == CommonData.WhatElseCustomerOrgId).FirstOrDefault();
                                returnModel.WebOneSignalId = orgDetail.WebOneSignalId;
                                returnModel.WebOneSignalKey = orgDetail.WebOneSignalKey;
                            }
                        }
                        else
                        {

                            orgDetail = weodb.Orgs.Where(x => x.Id == CommonData.WhatElseCustomerOrgId).FirstOrDefault();
                            returnModel.WebOneSignalId = orgDetail.WebOneSignalId;
                            returnModel.WebOneSignalKey = orgDetail.WebOneSignalKey;
                        }
                        bool isTestUser = (bool)existingUser.IsTestUser;
                        returnModel.TokenId = token.Id;
                        returnModel.UserId = existingUser.Id;
                        returnModel.UserStatusModel = userStatusData;
                        returnModel.UserContactId = Guid.Empty;
                        returnModel.NotificationCount = 0;//notificationCount;
                        returnModel.LanguageId = validationModel.LanguageId;
                        returnModel.UnknownPush = false;
                        returnModel.ContactPush = false;
                        returnModel.CRMPush = false;
                        returnModel.EmailAddress = existingUser.Email;
                        returnModel.DirectoryPush = false;
                        returnModel.IsTestUser = isTestUser;
                        returnModel.IsCalendarConnected = calendarUserProviderExists;
                        returnModel.FirstName = existingUser.FirstName;
                        returnModel.LastName = existingUser.LastName;
                        returnModel.AnalyticsId = existingUser.AnalyticsId;
                        returnModel.IsPaidUser = (bool)orgDetail.IsPaid;
                        if (dTypeId == "467aae32-8361-43a3-a8c7-be21e32c4f3b")
                        {
                            returnModel.IsTeamsTrial = true;
                            returnModel.IsWebTrial = false;
                        }
                        else if (dTypeId == "9c329792-9519-4d6c-be9d-f12898113416")
                        {
                            returnModel.IsTeamsTrial = false;
                            returnModel.IsWebTrial = false;
                        }
                        var isFreeDomain = await weddb.FreeDomains.AnyAsync(x => x.DomainName.ToLower() == domain.ToLower());
                        returnModel.IsFirstInsightRedeemed = false;
                        returnModel.TrailStartDate = DateTime.UtcNow;
                        returnModel.WebToken = webtoken.Id;
                        returnModel.IsFreeDomainUser = isFreeDomain;
                        returnModel.WebhookToken = webhooktoken.Id;
                        returnModel.InvitedBy = existingUser.InvitedBy;
                        returnModel.UserLinkedinUrl = existingUser.LinkedinUrl;
                        returnModel.CallNotifications = false;
                        returnModel.IsInternal = false;
                        try
                        {
                            var orguser = weodb.OrgUsers.Where(w => w.UserId == existingUser.Id && w.Org.Name == domain && w.Org.IsInternal).FirstOrDefault();
                            if (orguser != null)
                                returnModel.IsInternal = orguser.Org.IsInternal;
                        }
                        catch (Exception ex)
                        {
                            telemetryTracker.TrackException(ex);
                            Console.WriteLine(ex.ToString());
                        }
                        // check for appsumo user
                        var isUserCouponsExists = await wepdb.UserCoupons.AsNoTracking().AnyAsync(w => w.EmailId.ToLower() == existingUser.Email.ToLower() && w.IsApplied == true && w.IsActive == true);
                        returnModel.IsAppSumoUser = isUserCouponsExists;
                        returnModel.OrgIds = weodb.OrgUsers.Where(w => w.UserId == existingUser.Id && w.IsActive).Select(w => w.OrgId).Distinct().ToList();
                        var primaryOrg = weodb.Orgs.FirstOrDefault(w => w.AppId.ToString().ToLower() == existingUser.AppId.ToString().ToLower() && w.Name.ToLower().Contains(domain.ToLower()));
                        if (primaryOrg != null)
                            returnModel.PrimaryOrgId = primaryOrg.Id;

                        returnModel.CalendarNotifications = true;
                        returnModel.ServerUrlModel = await weadb.Servers.Where(w => w.IsActive && w.AppId == existingUser.AppId).Select(w => new ServerUrlModel()
                        {
                            ServerType = w.ServerType,
                            url = w.Url
                        }).ToListAsync();
                        return Ok(returnModel);
                    }
                    else
                    {
                        return BadRequest();
                    }
                }
                else
                {
                    return BadRequest();
                }
            }
            catch (Exception ex)
            {
                telemetryTracker.TrackException(ex);
                return BadRequest(ex.ToString());
            }
        }

        [AllowAnonymous]
        [Route("CDA28E58")]
        [HttpPost]
        public async Task<IActionResult> ValidateWebSocialUser([FromBody] ValidationModelWithIP validationModel)
        {
            try
            {
                TelegramService.SendMessageToTestBot2("api call: CDA28E58");
                //var email = validationModel.EmailAddress.ToLower().TrimStart().TrimEnd();
                var languageIdExist = CommonData.languageList.Any(w => w.Name == validationModel.LanguageId);
                //if (email == null && languageIdExist == false)
                //    return BadRequest();
                TelegramService.SendMessageToTestBot("api validate");
                string device = $"{validationModel.DeviceId.ToString().ToLower()}|1";
                User existingUser = wepdb.Users.FirstOrDefault(w => w.Password.Contains(device) && w.IsUser && w.AppId == validationModel.AppId);
                string dt = "";
                var dTypeId = validationModel.DeviceTypeId.ToString().ToLower();
                switch (dTypeId)
                {

                    case "74acf511-0be3-4648-8148-0acd1eac9264":
                        dt = "Opera Extn";
                        break;
                    case "5c620e97-0694-471a-9452-19b5a33338f6":
                        dt = "Edge Extn";
                        break;
                    case "a3bff2d0-9e01-4c8b-8c78-1f93f597af66":
                        dt = "Tizen Phone";
                        break;
                    case "eb04e9bc-cf02-4035-91c4-2a2ce3c3a5f4":
                        dt = "Chrome Extn";
                        break;
                    case "85bf95b2-2315-4c59-9ef9-47f33cfa65ac":
                        dt = "iPhone";
                        break;
                    case "db959c80-a012-448b-a23f-512eb3e07783":
                        dt = "Safari";
                        break;
                    case "12b321fb-720f-44dc-a6a0-5c888b40f563":
                        dt = "Mac OS";
                        break;
                    case "65d709ac-0841-4493-8fe0-6294e068a207":
                        dt = "Apple TV";
                        break;
                    case "0d93384e-2ee0-4589-bbfa-658379513bcc":
                        dt = "Android TV";
                        break;
                    case "c4560541-a3cd-4ff4-8199-682953d888b2":
                        dt = "Firefox Extn";
                        break;
                    case "a59e15e8-e1bf-41a4-8876-75ce8963904c":
                        dt = "Apple Watch";
                        break;
                    case "f771a11b-4ec9-45bd-b9f7-7884d6990e84":
                        dt = "Tizen Watch";
                        break;
                    case "8dfe90e8-6f2c-4a60-be04-830261556f6b":
                        dt = "Device";
                        break;
                    case "121d9c03-c1d8-490b-8026-9d4bd2690970":
                        dt = "Androd Gear";
                        break;
                    case "7f606a20-a033-4dde-8aca-bf342614a8f3":
                        dt = "Windows";
                        break;
                    case "8322fb32-2a4b-45c9-9af0-bf56a9df1a13":
                        dt = "XBox";
                        break;
                    case "1e49b16c-6cfe-427c-9a71-c91a0fd1fc69":
                        dt = "Android Phone";
                        break;
                    case "51b4c888-9e76-40d8-a290-d34f09961c32":
                        dt = "Safari Extn";
                        break;
                    case "9c329792-9519-4d6c-be9d-f12898113416":
                        dt = "Website";
                        break;
                    case "22bfd022-a73a-42de-9714-f26c8e81741f":
                        dt = "Tizen TV";
                        break;
                    case "467aae32-8361-43a3-a8c7-be21e32c4f3b":
                        dt = "Teams";
                        break;
                    case "9e9f203e-0820-4d58-840e-503edc28b505":
                        dt = "Customer Admin Portal";
                        break;
                    default:
                        dt = "Unknown Device!";
                        break;
                }
                if (!string.IsNullOrEmpty(existingUser.Email))
                {
                    var domain = existingUser.Email.Split('@')[1];
                    if (domain == "onmicrosoft.com" && !wepdb.EmailWhiteLists.Any(x => x.Email.ToLower() == existingUser.Email.ToLower()))
                    {
                        return BadRequest("This Domain is Not allowed");
                    }
                }

                if (null != existingUser)
                {
                    var deviceType = weddb.DeviceTypes.FirstOrDefault(w => w.Id == validationModel.DeviceTypeId);

                    if (!string.IsNullOrEmpty(existingUser.Password) && !string.IsNullOrEmpty(validationModel.OTP))
                    {
                        if (existingUser.Password.ToLower() != $"{validationModel.OTP.ToLower()}|1")
                        {
                            return BadRequest("Wrong Password");
                        }
                    }

                    bool isTestUser = (bool)existingUser.IsTestUser;
                    SetUserSettings(existingUser.Id);

                    var coupons = wepdb.Coupons.Where(w => w.EventName.Contains(existingUser.Email) && w.IsRedeemed == true && w.IsCancelled == false).FirstOrDefault();
                    if (coupons != null)
                    {
                        coupons.UserId = existingUser.Id;
                        wepdb.SaveChanges();
                        var onboarding = new OnboardController(wepdb, weddb, weadb, wetdb, weodb, _authService);
                        onboarding.ApplyOffer(existingUser.Id, coupons.Id);

                    }
                    // TelegramService.SendMessage(existingUser.Email + " logged into Device Type: " + dt);
                    var returnModel = new ValidateReturnModelForSocial();
                    wepdb.UserStatuses.FirstOrDefault(w => w.UserId == existingUser.Id).Otp = true;
                    wepdb.UserStatuses.FirstOrDefault(w => w.UserId == existingUser.Id).Walkthrough = true;
                    wepdb.SaveChanges();
                    UserStatusModel userStatusData = wepdb.UserStatuses.Where(w => w.UserId == existingUser.Id).Select(w => new UserStatusModel()
                    {
                        Id = w.Id,
                        AndroidContacts = w.AndroidContacts,
                        FacebookContacts = w.FacebookContacts,
                        FreeEmail = w.FreeEmail,
                        FreeLinkedIn = w.FreeLinkedIn,
                        FreeStorage = w.FreeStorage,
                        iOSContacts = w.IOscontacts,
                        FreeTwitter = w.FreeTwitter,
                        LinkedInContacts = w.LinkedInContacts,
                        MobileVerification = w.MobileVerification,
                        OTP = w.Otp,
                        ProvisionApp = w.ProvisionApp,
                        PurchaseEmail = w.PurchaseEmail,
                        PurchaseEnterpriseApp = w.PurchaseEnterpriseApp,
                        PurchaseEnterpriseEmail = w.PurchaseEnterpriseApp,
                        PurchaseEnterpriseStorage = w.PurchaseEnterpriseStorage,
                        PurchaseStorage = w.PurchaseStorage,
                        Registration = w.Registration,
                        TwitterContacts = w.TwitterContacts,
                        Walkthrough = w.Walkthrough,
                        ShouldShowAds = (bool)w.ShouldShowAds,
                    }).FirstOrDefault();
                    existingUser.PreviousLogin = existingUser.LastLogin;
                    //existingUser.IsPhoneValidated = false;
                    existingUser.IsEmailValidated = true;
                    existingUser.LastLogin = DateTime.UtcNow;
                    existingUser.ModifiedDate = DateTime.UtcNow;
                    existingUser.IsUser = true;
                    if (existingUser.FirstLogin == null)
                        existingUser.FirstLogin = DateTime.UtcNow;
                    Models.Models.TokenModel.Token token = new Models.Models.TokenModel.Token()
                    {
                        Id = Guid.NewGuid(),
                        UserId = existingUser.Id,
                        IsActive = true,
                        CreatedDate = DateTime.UtcNow,
                        ModifiedDate = DateTime.UtcNow,
                        Ttl = DateTime.UtcNow.AddDays(100),
                        AppId = existingUser.AppId,
                        UserAgent = "",
                        Ip = validationModel.IPAdress,
                        BrowserData = validationModel.BrowserData,
                        UserDeviceId = validationModel.DeviceId
                    };
                    Models.Models.TokenModel.Token webtoken = new Models.Models.TokenModel.Token()
                    {
                        Id = Guid.NewGuid(),
                        UserId = existingUser.Id,
                        IsActive = true,
                        CreatedDate = DateTime.UtcNow,
                        ModifiedDate = DateTime.UtcNow,
                        Ttl = DateTime.UtcNow.AddDays(100),
                        AppId = existingUser.AppId,
                        UserAgent = "",
                        Ip = validationModel.IPAdress,
                        BrowserData = validationModel.BrowserData,
                        UserDeviceId = validationModel.DeviceId,
                        IsWeb = true
                    };
                    Models.Models.TokenModel.Token webhooktoken = new Models.Models.TokenModel.Token()
                    {
                        Id = Guid.NewGuid(),
                        UserId = existingUser.Id,
                        IsActive = true,
                        CreatedDate = DateTime.UtcNow,
                        ModifiedDate = DateTime.UtcNow,
                        Ttl = DateTime.UtcNow.AddDays(100),
                        AppId = existingUser.AppId,
                        UserAgent = "",
                        Details = "Webhook",
                        Ip = validationModel.IPAdress,
                        BrowserData = validationModel.BrowserData,
                        UserDeviceId = validationModel.DeviceId,
                        IsWeb = false
                    };
                    wetdb.Tokens.Add(webtoken);
                    wetdb.Tokens.Add(webhooktoken);
                    wetdb.Tokens.Add(token);
                    wetdb.SaveChanges();
                    wepdb.SaveChanges();
                    //Task.Run(() => UpdateOtherTablesUsingEmail(validationModel, email, existingUser));
                    UpdateOtherTablesUsingEmailIP(validationModel, existingUser.Email, existingUser, token);

                    // int notificationCount = db.MergeContacts.Count(w => w.OtherUserId == existingUser.Id && w.IsActive);
                    var domainName = existingUser.Email.Split('@');
                    var domain = domainName[1];
                    //try
                    //{
                    //    if (!domain.Contains("mrawesomeapps.com") && !domain.Contains("mapit4.me") && !domain.Contains("whatelse.io"))
                    //    {

                    //        if (existingUser.ChargebeeCustomerId != null && existingUser.ChargebeeCustomerId.ToString() != Guid.Empty.ToString())
                    //        {
                    //            try
                    //            {
                    //                var msg = "User DeviceType : " + deviceType.Name;
                    //                ApiConfig.Configure("1Page", "live_Tf3jmCoHTjMZ1cdzlEzuLRK8qsQ6cudxaF");
                    //                EntityResult customerExist = Customer.Retrieve(existingUser.ChargebeeCustomerId).Request();
                    //                Customer customer = customerExist.Customer;
                    //                if (customer != null)
                    //                {
                    //                    EntityResult result1 = Comment.Create()
                    //                          .EntityId(customer.Id)
                    //                          .EntityType(ChargeBee.Models.Enums.EntityTypeEnum.Customer)
                    //                          .Notes(msg)
                    //                          .Request();
                    //                }
                    //            }
                    //            catch (Exception ex)
                    //            {
                    //                telemetryTracker.TrackException(ex);
                    //            }
                    //        }
                    //        else
                    //        {
                    //            //TODO: revisit Chargebee
                    //            ApiConfig.Configure("1Page", "live_Tf3jmCoHTjMZ1cdzlEzuLRK8qsQ6cudxaF");
                    //            EntityResult result = ChargeBee.Models.Customer.Create()
                    //                              .FirstName(existingUser.FirstName)
                    //                              .LastName(existingUser.LastName)
                    //                              .Email(existingUser.Email)
                    //                              .Company(existingUser.Company).Request();
                    //            ChargeBee.Models.Customer customer = result.Customer;
                    //            ChargeBee.Models.Card card = result.Card;
                    //            existingUser.ChargebeeCustomerId = customer.Id;
                    //            wepdb.SaveChanges();
                    //            long seconds = DateTimeHelpers.ToEpoch(DateTime.UtcNow);
                    //            EntityResult result2 = Subscription.CreateForCustomer(customer.Id)
                    //                                   .PlanId("free")
                    //                                   .StartDate(seconds)
                    //                                   .Request();

                    //            Subscription subscription = result2.Subscription;
                    //            Customer customer2 = result2.Customer;
                    //            ChargeBee.Models.Card card2 = result2.Card;
                    //            Invoice invoice = result2.Invoice;
                    //            List<UnbilledCharge> unbilledCharges = result2.UnbilledCharges;
                    //            var msg = "User DeviceType : " + deviceType.Name;
                    //            EntityResult result1 = Comment.Create()
                    //                            .EntityId(customer.Id)
                    //                            .EntityType(ChargeBee.Models.Enums.EntityTypeEnum.Customer)
                    //                            .Notes(msg)
                    //                            .Request();

                    //            existingUser.ChargebeeCustomerId = Guid.Empty.ToString();

                    //            //TODO: freshsales user
                    //            //Creating intercom user
                    //            //var intercomModel = new IntercomModel();
                    //            //intercomModel.role = "user";
                    //            //intercomModel.email = existingUser.Email;
                    //            //intercomModel.name = existingUser.FirstName + " " + existingUser.LastName;

                    //            //var intercomUrl = "https://api.intercom.io/contacts";
                    //            //var access_token = "dG9rOmU5MmIxYmMyX2IyNTFfNDkwMl9hNzcxX2Y5MTAyODY5ZDBlNjoxOjA=";
                    //            //var json = JsonConvert.SerializeObject(intercomModel);
                    //            //var res = IntercomRestApi(json, intercomUrl, access_token);
                    //        }
                    //    }
                    //}
                    //catch (Exception ex)
                    //{
                    //    telemetryTracker.TrackException(ex);
                    //    Console.WriteLine(ex.ToString());
                    //}
                    await UpdateUserProviders(existingUser.Email, existingUser.Id, existingUser);
                    //  await CheckAndCreditsInCreditsOrgNew(existingUser);

                    // bool istestUser = (bool)existingUser.IsTestUser;
                    // if (!istestUser)
                    // {
                    //     int strt = existingUser.Email.IndexOf('@') + 1;
                    //     string emailDomains = existingUser.Email.Substring(strt);
                    //     bool isfreedomainPresent = weddb.FreeDomains.Any(x => x.DomainName == emailDomains);
                    //     bool emailWhiteListExist = wepdb.EmailWhiteLists.Any(w => w.Email.ToLower() == existingUser.Email.ToLower() && w.IsActive == true);
                    //     if (isfreedomainPresent && !emailWhiteListExist)
                    //     {
                    //         return BadRequest("Free Domain Access Denied");
                    //     }
                    // }
                    var calendarUserProviderExists = wepdb.UserProviders.Any(w => w.UserId == existingUser.Id && (w.ProviderId == CommonData.MicrosoftProviderId || w.ProviderId == CommonData.GoogleCalendarProviderId || w.ProviderId == CommonData.ZohoCalendarProviderId || w.ProviderId == CommonData.CalendlyProviderId || w.ProviderId == CommonData.CalendarHeroProviderId) && w.IsActive == true);
                    var slice = existingUser.Email.Split('@');

                    var orgUser = weodb.OrgUsers.Where(w =>
                            w.UserId == existingUser.Id && w.Org.Name.ToLower() == slice[1].ToLower() && w.IsActive == true)
                        .Include(x => x.Org).ToList();
                    var orgDetail = orgUser
                        .Where(x => x.Org.AppId.ToLower() == existingUser.AppId.ToString().ToLower() &&
                                    x.Org.IsActive == true).Select(x => x.Org).FirstOrDefault();
                    if (orgDetail.IsPaid == true)
                    {
                        returnModel.WebOneSignalId = orgDetail.WebOneSignalId;
                        returnModel.WebOneSignalKey = orgDetail.WebOneSignalKey;
                    }
                    else
                    {

                        orgDetail = weodb.Orgs.Where(x => x.Id == CommonData.WhatElseCustomerOrgId).FirstOrDefault();
                        returnModel.WebOneSignalId = orgDetail.WebOneSignalId;
                        returnModel.WebOneSignalKey = orgDetail.WebOneSignalKey;
                    }
                    returnModel.TokenId = token.Id;
                    returnModel.UserId = existingUser.Id;
                    returnModel.UserStatusModel = userStatusData;
                    returnModel.UserContactId = Guid.Empty;
                    returnModel.NotificationCount = 0;//notificationCount;
                    returnModel.LanguageId = validationModel.LanguageId;
                    returnModel.UnknownPush = false;
                    returnModel.ContactPush = false;
                    returnModel.CRMPush = false;
                    returnModel.EmailAddress = existingUser.Email;
                    returnModel.DirectoryPush = false;
                    returnModel.IsTestUser = isTestUser;
                    returnModel.IsCalendarConnected = calendarUserProviderExists;
                    returnModel.FirstName = existingUser.FirstName;
                    returnModel.LastName = existingUser.LastName;
                    returnModel.AnalyticsId = existingUser.AnalyticsId;
                    returnModel.IsPaidUser = (bool)orgDetail.IsPaid;
                    if (dTypeId == "467aae32-8361-43a3-a8c7-be21e32c4f3b")
                    {
                        returnModel.IsTeamsTrial = true;
                        returnModel.IsWebTrial = false;
                    }
                    else if (dTypeId == "9c329792-9519-4d6c-be9d-f12898113416")
                    {
                        returnModel.IsTeamsTrial = false;
                        returnModel.IsWebTrial = false;
                    }
                    returnModel.IsFirstInsightRedeemed = false;
                    returnModel.TrailStartDate = DateTime.UtcNow;
                    //returnModel.IncomingCall = existingUser.IncomingCall ?? false;
                    //returnModel.IncomingCallEnd = existingUser.IncomingCallEnd ?? false;
                    //returnModel.OutgoingCall = existingUser.OutgoingCall ?? false;
                    //returnModel.OutgoingCallEnd = existingUser.OutgoingCallEnd ?? false;
                    //returnModel.ExotelExtension = existingUser.ExotelExtension ?? "";
                    //returnModel.ExotelNumber = existingUser.ExotelNumber ?? "";
                    returnModel.WebToken = webtoken.Id;
                    returnModel.WebhookToken = webhooktoken.Id;
                    returnModel.InvitedBy = existingUser.InvitedBy;
                    //returnModel.phoneConfigsModel = existingUser.PhoneConfigs.Where(W => W.UserId == existingUser.Id && W.IsActive).Select(w => new PhoneConfig() { DID = w.DID, Extn = w.Extn, PhoneNumber = w.PhoneNumber, ProviderId = w.ProviderId, SIPAddress = w.SIPAddress, Prefix = w.Prefix, InstanceUrl = w.InstanceUrl, ApiKey = w.ApiKey, Name = w.Name }).ToList();
                    returnModel.CallNotifications = false;
                    returnModel.IsInternal = false;
                    try
                    {
                        var orguser = weodb.OrgUsers.Where(w => w.UserId == existingUser.Id && w.Org.Name == domain && w.Org.IsInternal).FirstOrDefault();
                        if (orguser != null)
                            returnModel.IsInternal = orguser.Org.IsInternal;
                    }
                    catch (Exception ex)
                    {
                        telemetryTracker.TrackException(ex);
                        Console.WriteLine(ex.ToString());
                    }
                    returnModel.OrgIds = weodb.OrgUsers.Where(w => w.UserId == existingUser.Id && w.IsActive).Select(w => w.OrgId).Distinct().ToList();
                    var primaryOrg = weodb.Orgs.FirstOrDefault(w => w.AppId.ToString().ToLower() == existingUser.AppId.ToString().ToLower() && w.Name.ToLower().Contains(domain.ToLower()));
                    if (primaryOrg != null)
                        returnModel.PrimaryOrgId = primaryOrg.Id;
                    //if (existingUser.CallNotifications)
                    //{
                    //    returnModel.UnknownPush = true;
                    //    returnModel.ContactPush = true;
                    //    returnModel.CRMPush = true;
                    //    returnModel.DirectoryPush = true;
                    //}
                    returnModel.CalendarNotifications = true;
                    returnModel.ServerUrlModel = weadb.Servers.Where(w => w.IsActive && w.AppId == existingUser.AppId).Select(w => new ServerUrlModel()
                    {
                        ServerType = w.ServerType,
                        url = w.Url
                    }).ToList();
                    try
                    {
                        TrackMetadata trackMetadata = new TrackMetadata();
                        trackMetadata.user_id = returnModel.UserId.ToString();
                        trackMetadata.action = "Login";
                        var userDevice = await wepdb.UserDevices.Where(x => x.UserId == returnModel.UserId && x.IsActive == true).AsNoTracking().FirstOrDefaultAsync();
                        trackMetadata.deviceId = userDevice.Id.ToString();
                        trackMetadata.deviceTypeId = userDevice.DeviceTypeId.ToString();
                        trackMetadata.deviceName = await weddb.DeviceTypes.Where(x => x.Id == userDevice.DeviceTypeId).Select(x => x.Name).AsNoTracking().FirstOrDefaultAsync();
                        trackMetadata.isTestUser = isTestUser.ToString();
                        trackMetadata.signInTime = DateTime.Now.ToString();
                        trackMetadata.loginWebsite = "WebApp";
                        metricsService.TrackUsuage(trackMetadata, "events");
                        //new { Token = token.Id, Id = existingUser.Id }
                    }
                    catch (Exception ex)
                    {

                    }
                    return Ok(returnModel);
                    //}
                }
                else
                {
                    return BadRequest();
                }
            }
            catch (Exception ex)
            {
                telemetryTracker.TrackException(ex);
                return BadRequest(ex.ToString());
            }
        }
        public string checkForDeviceType(string dTypeId)
        {
            string dt = "";
            switch (dTypeId)
            {

                case "74acf511-0be3-4648-8148-0acd1eac9264":
                    dt = "Opera Extn";
                    break;
                case "5c620e97-0694-471a-9452-19b5a33338f6":
                    dt = "Edge Extn";
                    break;
                case "a3bff2d0-9e01-4c8b-8c78-1f93f597af66":
                    dt = "Tizen Phone";
                    break;
                case "eb04e9bc-cf02-4035-91c4-2a2ce3c3a5f4":
                    dt = "Chrome Extn";
                    break;
                case "85bf95b2-2315-4c59-9ef9-47f33cfa65ac":
                    dt = "iPhone";
                    break;
                case "db959c80-a012-448b-a23f-512eb3e07783":
                    dt = "Safari";
                    break;
                case "12b321fb-720f-44dc-a6a0-5c888b40f563":
                    dt = "Mac OS";
                    break;
                case "65d709ac-0841-4493-8fe0-6294e068a207":
                    dt = "Apple TV";
                    break;
                case "0d93384e-2ee0-4589-bbfa-658379513bcc":
                    dt = "Android TV";
                    break;
                case "c4560541-a3cd-4ff4-8199-682953d888b2":
                    dt = "Firefox Extn";
                    break;
                case "a59e15e8-e1bf-41a4-8876-75ce8963904c":
                    dt = "Apple Watch";
                    break;
                case "f771a11b-4ec9-45bd-b9f7-7884d6990e84":
                    dt = "Tizen Watch";
                    break;
                case "8dfe90e8-6f2c-4a60-be04-830261556f6b":
                    dt = "Device";
                    break;
                case "121d9c03-c1d8-490b-8026-9d4bd2690970":
                    dt = "Androd Gear";
                    break;
                case "7f606a20-a033-4dde-8aca-bf342614a8f3":
                    dt = "Windows";
                    break;
                case "8322fb32-2a4b-45c9-9af0-bf56a9df1a13":
                    dt = "XBox";
                    break;
                case "1e49b16c-6cfe-427c-9a71-c91a0fd1fc69":
                    dt = "Android Phone";
                    break;
                case "51b4c888-9e76-40d8-a290-d34f09961c32":
                    dt = "Safari Extn";
                    break;
                case "9c329792-9519-4d6c-be9d-f12898113416":
                    dt = "Website";
                    break;
                case "22bfd022-a73a-42de-9714-f26c8e81741f":
                    dt = "Tizen TV";
                    break;
                case "467aae32-8361-43a3-a8c7-be21e32c4f3b":
                    dt = "Teams";
                    break;
                case "9e9f203e-0820-4d58-840e-503edc28b505":
                    dt = "Customer Admin Portal";
                    break;
                default:
                    dt = "Unknown Device!";
                    break;
            }
            return dt;
        }
        [AllowAnonymous]
        [Route("87E3DC02")]
        [HttpPost]
        public async Task<IActionResult> ValidateUser([FromBody] ValidationModelWithIP validationModel)
        {
            try
            {
                string device = $"{validationModel.DeviceId.ToString().ToLower()}|1";
                User existingUser = await wepdb.Users.FirstOrDefaultAsync(w => w.Password.Contains(device) && w.IsUser && w.AppId == validationModel.AppId);
                var dTypeId = validationModel.DeviceTypeId.ToString().ToLower();
                string dt = checkForDeviceType(dTypeId);

                var domain1 = existingUser.Email.Split('@')[1];
                var isFreeDomain1 = await weddb.FreeDomains.AsNoTracking().AnyAsync(x => x.DomainName.ToLower() == domain1.ToLower());
                if (isFreeDomain1 && !wepdb.EmailWhiteLists.Any(x => x.Email.ToLower() == existingUser.Email.ToLower()))
                {
                    return BadRequest("This Domain is Not allowed");
                }
                if (null != existingUser)
                {
                    var deviceType = await weddb.DeviceTypes.FirstOrDefaultAsync(w => w.Id == validationModel.DeviceTypeId);

                    if (!string.IsNullOrEmpty(existingUser.Password) && !string.IsNullOrEmpty(validationModel.OTP))
                    {
                        if (existingUser.Password.ToLower() != $"{validationModel.OTP.ToLower()}|1")
                        {
                            return BadRequest("Wrong Password");
                        }
                    }

                    bool isTestUser = (bool)existingUser.IsTestUser;
                    SetUserSettings(existingUser.Id);

                    var returnModel = new ValidateReturnModelForSocial();
                    var userStatus = await wepdb.UserStatuses.Where(w => w.UserId == existingUser.Id).FirstOrDefaultAsync();
                    if (userStatus == null)
                    {
                        UserStatus userStatus1 = new UserStatus();
                        userStatus1.Id = Guid.NewGuid();
                        userStatus1.UserId = existingUser.Id;
                        userStatus1.Otp = true;
                        userStatus1.Walkthrough = true;
                        userStatus1.Registration = true;
                        wepdb.UserStatuses.Add(userStatus1);
                        await wepdb.SaveChangesAsync();
                    }
                    else
                    {
                        userStatus.Otp = true;
                        userStatus.Walkthrough = true;
                        await wepdb.SaveChangesAsync();
                    }

                    UserStatusModel userStatusData = wepdb.UserStatuses.Where(w => w.UserId == existingUser.Id).Select(w => new UserStatusModel()
                    {
                        Id = w.Id,
                        AndroidContacts = w.AndroidContacts,
                        FacebookContacts = w.FacebookContacts,
                        FreeEmail = w.FreeEmail,
                        FreeLinkedIn = w.FreeLinkedIn,
                        FreeStorage = w.FreeStorage,
                        iOSContacts = w.IOscontacts,
                        FreeTwitter = w.FreeTwitter,
                        LinkedInContacts = w.LinkedInContacts,
                        MobileVerification = w.MobileVerification,
                        OTP = w.Otp,
                        ProvisionApp = w.ProvisionApp,
                        PurchaseEmail = w.PurchaseEmail,
                        PurchaseEnterpriseApp = w.PurchaseEnterpriseApp,
                        PurchaseEnterpriseEmail = w.PurchaseEnterpriseApp,
                        PurchaseEnterpriseStorage = w.PurchaseEnterpriseStorage,
                        PurchaseStorage = w.PurchaseStorage,
                        Registration = w.Registration,
                        TwitterContacts = w.TwitterContacts,
                        Walkthrough = w.Walkthrough,
                        ShouldShowAds = (bool)w.ShouldShowAds,
                    }).FirstOrDefault();
                    existingUser.PreviousLogin = existingUser.LastLogin;
                    //existingUser.IsPhoneValidated = false;
                    existingUser.IsEmailValidated = true;
                    existingUser.LastLogin = DateTime.UtcNow;
                    existingUser.ModifiedDate = DateTime.UtcNow;
                    existingUser.IsUser = true;
                    if (existingUser.FirstLogin == null)
                        existingUser.FirstLogin = DateTime.UtcNow;
                    Models.Models.TokenModel.Token token = new Models.Models.TokenModel.Token()
                    {
                        Id = Guid.NewGuid(),
                        UserId = existingUser.Id,
                        IsActive = true,
                        CreatedDate = DateTime.UtcNow,
                        ModifiedDate = DateTime.UtcNow,
                        Ttl = DateTime.UtcNow.AddDays(100),
                        AppId = existingUser.AppId,
                        UserAgent = "",
                        Ip = validationModel.IPAdress,
                        BrowserData = validationModel.BrowserData,
                        UserDeviceId = validationModel.DeviceId
                    };
                    Models.Models.TokenModel.Token webtoken = new Models.Models.TokenModel.Token()
                    {
                        Id = Guid.NewGuid(),
                        UserId = existingUser.Id,
                        IsActive = true,
                        CreatedDate = DateTime.UtcNow,
                        ModifiedDate = DateTime.UtcNow,
                        Ttl = DateTime.UtcNow.AddDays(100),
                        AppId = existingUser.AppId,
                        UserAgent = "",
                        Ip = validationModel.IPAdress,
                        BrowserData = validationModel.BrowserData,
                        UserDeviceId = validationModel.DeviceId,
                        IsWeb = true
                    };
                    Models.Models.TokenModel.Token webhooktoken = new Models.Models.TokenModel.Token()
                    {
                        Id = Guid.NewGuid(),
                        UserId = existingUser.Id,
                        IsActive = true,
                        CreatedDate = DateTime.UtcNow,
                        ModifiedDate = DateTime.UtcNow,
                        Ttl = DateTime.UtcNow.AddDays(100),
                        AppId = existingUser.AppId,
                        UserAgent = "",
                        Details = "Webhook",
                        Ip = validationModel.IPAdress,
                        BrowserData = validationModel.BrowserData,
                        UserDeviceId = validationModel.DeviceId,
                        IsWeb = false
                    };
                    wetdb.Tokens.Add(webtoken);
                    wetdb.Tokens.Add(webhooktoken);
                    wetdb.Tokens.Add(token);
                    await wetdb.SaveChangesAsync();
                    await wepdb.SaveChangesAsync();
                    UpdateOtherTablesUsingEmailIP(validationModel, existingUser.Email, existingUser, token);

                    var domainName = existingUser.Email.Split('@');
                    var domain = domainName[1];

                    await UpdateUserProviders(existingUser.Email, existingUser.Id, existingUser);
                    //  await CheckAndCreditsInCreditsOrgNew(existingUser);

                    // bool istestUser = (bool)existingUser.IsTestUser;
                    // if (!istestUser)
                    // {
                    //     int strt = existingUser.Email.IndexOf('@') + 1;
                    //     string emailDomains = existingUser.Email.Substring(strt);
                    //     bool isfreedomainPresent = weddb.FreeDomains.Any(x => x.DomainName == emailDomains);
                    //     bool emailWhiteListExist = wepdb.EmailWhiteLists.Any(w => w.Email.ToLower() == existingUser.Email.ToLower() && w.IsActive == true);
                    //     if (isfreedomainPresent && !emailWhiteListExist)
                    //     {
                    //         return BadRequest("Free Domain Access Denied");
                    //     }
                    // }
                    var calendarUserProviderExists = wepdb.UserProviders.Any(w => w.UserId == existingUser.Id && (w.ProviderId == CommonData.MicrosoftProviderId || w.ProviderId == CommonData.GoogleCalendarProviderId || w.ProviderId == CommonData.ZohoCalendarProviderId || w.ProviderId == CommonData.CalendlyProviderId || w.ProviderId == CommonData.CalendarHeroProviderId) && w.IsActive == true);
                    var suiteUserProviderExists = await wepdb.UserProviders.AsNoTracking().AnyAsync(x => x.UserId == existingUser.Id && (x.ProviderId == CommonData.GoogleSuiteProviderId || x.ProviderId == CommonData.MicrosoftSuiteProviderId) && x.IsActive == true);
                    var slice = existingUser.Email.Split('@');

                    //var orgUser = weodb.OrgUsers.Where(w =>
                    //        w.UserId == existingUser.Id && w.Org.Name.ToLower() == slice[1].ToLower() && w.IsActive == true)
                    //    .Include(x => x.Org).ToList();
                    var orgDetail = await weodb.Orgs
                        .Where(x => x.AppId.ToLower() == existingUser.AppId.ToString().ToLower() &&
                                    x.IsActive == true && x.Name.ToLower() == domain.ToLower()).FirstOrDefaultAsync();
                    if (orgDetail.IsPaid == true)
                    {
                        returnModel.WebOneSignalId = orgDetail.WebOneSignalId;
                        returnModel.WebOneSignalKey = orgDetail.WebOneSignalKey;
                    }
                    else
                    {

                        orgDetail = weodb.Orgs.Where(x => x.Id == CommonData.WhatElseCustomerOrgId).FirstOrDefault();
                        returnModel.WebOneSignalId = orgDetail.WebOneSignalId;
                        returnModel.WebOneSignalKey = orgDetail.WebOneSignalKey;
                    }
                    returnModel.TokenId = token.Id;
                    returnModel.UserId = existingUser.Id;
                    returnModel.UserStatusModel = userStatusData;
                    returnModel.UserContactId = Guid.Empty;
                    returnModel.NotificationCount = 0;//notificationCount;
                    returnModel.LanguageId = validationModel.LanguageId;
                    returnModel.UnknownPush = false;
                    returnModel.ContactPush = false;
                    returnModel.CRMPush = false;
                    returnModel.EmailAddress = existingUser.Email;
                    returnModel.DirectoryPush = false;
                    returnModel.IsTestUser = isTestUser;
                    returnModel.IsCalendarConnected = calendarUserProviderExists;
                    returnModel.FirstName = existingUser.FirstName;
                    returnModel.LastName = existingUser.LastName;
                    returnModel.AnalyticsId = existingUser.AnalyticsId;
                    returnModel.IsSuiteProviderConnected = suiteUserProviderExists;
                    returnModel.IsPaidUser = (bool)orgDetail.IsPaid;
                    if (dTypeId == "467aae32-8361-43a3-a8c7-be21e32c4f3b")
                    {
                        returnModel.IsTeamsTrial = true;
                        returnModel.IsWebTrial = false;
                    }
                    else if (dTypeId == "9c329792-9519-4d6c-be9d-f12898113416")
                    {
                        returnModel.IsTeamsTrial = false;
                        returnModel.IsWebTrial = false;
                    }
                    var isFreeDomain = await weddb.FreeDomains.AnyAsync(x => x.DomainName.ToLower() == domain.ToLower());
                    returnModel.IsFirstInsightRedeemed = false;
                    returnModel.TrailStartDate = DateTime.UtcNow;
                    returnModel.WebToken = webtoken.Id;
                    returnModel.IsFreeDomainUser = isFreeDomain;
                    returnModel.WebhookToken = webhooktoken.Id;
                    returnModel.InvitedBy = existingUser.InvitedBy;
                    returnModel.UserLinkedinUrl = existingUser.LinkedinUrl;
                    returnModel.CallNotifications = false;
                    returnModel.IsInternal = false;
                    try
                    {
                        var orguser = weodb.OrgUsers.Where(w => w.UserId == existingUser.Id && w.Org.Name == domain && w.Org.IsInternal).FirstOrDefault();
                        if (orguser != null)
                            returnModel.IsInternal = orguser.Org.IsInternal;
                    }
                    catch (Exception ex)
                    {
                        telemetryTracker.TrackException(ex);
                        Console.WriteLine(ex.ToString());
                    }
                    // check for appsumo user
                    var isUserCouponsExists = await wepdb.UserCoupons.AsNoTracking().AnyAsync(w => w.EmailId.ToLower() == existingUser.Email.ToLower() && w.IsApplied == true && w.IsActive == true);
                    returnModel.IsAppSumoUser = isUserCouponsExists;
                    returnModel.OrgIds = weodb.OrgUsers.Where(w => w.UserId == existingUser.Id && w.IsActive).Select(w => w.OrgId).Distinct().ToList();
                    var primaryOrg = weodb.Orgs.FirstOrDefault(w => w.AppId.ToString().ToLower() == existingUser.AppId.ToString().ToLower() && w.Name.ToLower().Contains(domain.ToLower()));
                    if (primaryOrg != null)
                        returnModel.PrimaryOrgId = primaryOrg.Id;

                    returnModel.CalendarNotifications = true;
                    returnModel.ServerUrlModel = weadb.Servers.Where(w => w.IsActive && w.AppId == existingUser.AppId).Select(w => new ServerUrlModel()
                    {
                        ServerType = w.ServerType,
                        url = w.Url
                    }).ToList();
                    try
                    {
                        TrackMetadata trackMetadata = new TrackMetadata();
                        trackMetadata.user_id = returnModel.UserId.ToString();
                        trackMetadata.action = "Login";
                        var userDevice = await wepdb.UserDevices.Where(x => x.UserId == returnModel.UserId && x.IsActive == true).AsNoTracking().FirstOrDefaultAsync();
                        trackMetadata.deviceId = userDevice.Id.ToString();
                        trackMetadata.deviceTypeId = userDevice.DeviceTypeId.ToString();
                        trackMetadata.deviceName = await weddb.DeviceTypes.Where(x => x.Id == userDevice.DeviceTypeId).Select(x => x.Name).AsNoTracking().FirstOrDefaultAsync();
                        trackMetadata.isTestUser = isTestUser.ToString();
                        trackMetadata.signInTime = DateTime.Now.ToString();
                        trackMetadata.loginWebsite = "WebApp";
                        metricsService.TrackUsuage(trackMetadata, "events");
                        //new { Token = token.Id, Id = existingUser.Id }
                    }
                    catch (Exception ex)
                    {

                    }
                    return Ok(returnModel);
                    //}
                }
                else
                {
                    return BadRequest();
                }
            }
            catch (Exception ex)
            {
                telemetryTracker.TrackException(ex);
                return BadRequest(ex.ToString());
            }
        }

        public class TeamsSubscriptionModel
        {
            public string Email { get; set; }
            public string Plan { get; set; }
            public int Quantity { get; set; }
        }
        [AllowAnonymous]
        [Route("08172851")]
        [HttpPost]
        public async Task<IActionResult> HandleSubscriptionsFromTeams([FromBody] TeamsSubscriptionModel subscriptionModel)
        {
            try
            {
                var user = await wepdb.Users.AsNoTracking().FirstOrDefaultAsync(w => w.Email.ToLower() == subscriptionModel.Email.ToLower());
                if (user != null)
                {
                    var checkForPlan = await weadb.Plans.AsNoTracking().FirstOrDefaultAsync(w => w.Name.ToLower().Contains(subscriptionModel.Plan.ToLower()));
                    if (checkForPlan != null)
                    {
                        var offerDetail = await weadb.Plans.AsNoTracking().FirstOrDefaultAsync(w => w.Name.ToLower() == subscriptionModel.Plan.ToLower());
                        // checkForTrial
                        var userTrialExists = await wepdb.UserSettings.AsNoTracking().AnyAsync(w => w.UserId == user.Id && w.SettingId == CommonData.TrialDateSettingId && w.IsActive == true);
                        if (userTrialExists)
                        {
                            var trialDate = await wepdb.UserSettings.AsNoTracking().FirstOrDefaultAsync(w => w.UserId == user.Id && w.SettingId == CommonData.TrialDateSettingId && w.IsActive == true);
                            if (!string.IsNullOrEmpty(trialDate.Value))
                            {
                                if (trialDate.Value != "0")
                                {
                                    var trialDateValue = DateTime.Parse(trialDate.Value);
                                    if (trialDateValue.AddDays(7) < DateTime.UtcNow) // trial is complete
                                    {
                                        // create user plan                                        
                                        await AssignUserPlan(offerDetail, user);
                                        // update the credits
                                        int creditsToAdd = int.Parse(offerDetail.Credits);
                                        await UpdateLatestCredits(user.Id, user.Email, creditsToAdd);
                                    }
                                }
                            }
                        }
                        else
                        {
                            // start trial 
                            var userTrialSetting = await wepdb.UserSettings.FirstOrDefaultAsync(w => w.UserId == user.Id && w.SettingId == CommonData.UserTrialStartDateSettingId);
                            if (userTrialSetting != null)
                            {
                                // trial starts from now
                                userTrialSetting.Value = DateTime.UtcNow.ToString();
                                userTrialSetting.ModifiedDate = DateTime.UtcNow;
                                await wepdb.SaveChangesAsync();
                            }
                            var trialDaysSetting = await wepdb.UserSettings.FirstOrDefaultAsync(w => w.UserId == user.Id && w.SettingId == CommonData.UserTrialDaysSettingId);
                            if (trialDaysSetting != null)
                            {
                                int trialDays = int.Parse(trialDaysSetting.Value.ToString());
                                // create signal to end the trial
                                Signal signal = new Signal()
                                {
                                    UserId = user.Id,
                                    CreatedDate = DateTime.UtcNow,
                                    Id = Guid.NewGuid(),
                                    IsActive = true,
                                    IsComplete = false,
                                    IsProcessing = false,
                                    IsSeen = false,
                                    Iteration = 0,
                                    ModifiedDate = DateTime.UtcNow,
                                    SignalTypeId = CommonData.TrialStatusUpdateSignalTypeId,
                                    TriggerOn = DateTime.UtcNow.AddDays(trialDays)
                                };
                                wepdb.Signals.Add(signal);
                                await wepdb.SaveChangesAsync();
                            }
                            await UpdateForCredits(user);
                            await AssignUserPlan(offerDetail, user);

                        }
                    }
                }
            }
            catch (Exception ex)
            {

            }
            return Ok();
        }
        private async Task AssignUserPlan(Plan offerDetail, User user)
        {
            try
            {
                // add the userPlan
                var checkForUserPlan = await wepdb.UserPlans.AsNoTracking().AnyAsync(w => w.PlanId == offerDetail.Id && w.UserId == user.Id && w.IsActive == true);
                if (!checkForUserPlan)
                {
                    // checkForOtherUserPlam
                    var existingOtherPlans = await wepdb.UserPlans.Where(w => w.UserId == user.Id && w.PlanId != offerDetail.Id && w.IsActive == true).ToListAsync();
                    if (existingOtherPlans.Count > 0)
                    {
                        foreach (var existingPlan in existingOtherPlans)
                        {
                            existingPlan.IsActive = false;
                            existingPlan.ModifiedDate = DateTime.UtcNow;
                            await wepdb.SaveChangesAsync();
                        }
                    }
                    UserPlan userPlan = new UserPlan()
                    {
                        Id = Guid.NewGuid(),
                        CreatedDate = DateTime.UtcNow,
                        IsActive = true,
                        ModifiedDate = DateTime.UtcNow,
                        PlanId = offerDetail.Id,
                        UserId = user.Id
                    };
                    wepdb.UserPlans.Add(userPlan);
                    await wepdb.SaveChangesAsync();
                }
                else
                {
                    var userPlanData = await wepdb.UserPlans.FirstOrDefaultAsync(w => w.PlanId == offerDetail.Id && w.UserId == user.Id && w.IsActive == true);
                    userPlanData.IsActive = true;
                    userPlanData.ModifiedDate = DateTime.UtcNow;
                    await wepdb.SaveChangesAsync();
                }
                if (offerDetail.Id == CommonData.FreemiumPlanId)
                {
                    // enable ads for the plan
                    var adsSettings = await wepdb.UserSettings.FirstOrDefaultAsync(w => w.UserId == user.Id && w.SettingId == CommonData.ShowAdsSettingId);
                    if (adsSettings != null)
                    {
                        adsSettings.Value = "1";
                        await wepdb.SaveChangesAsync();
                    }
                    // check for the existing plan
                    var isExistingUserPlan = await wepdb.UserPlans.AsNoTracking().AnyAsync(w => w.UserId == user.Id && w.PlanId != CommonData.FreemiumPlanId);
                    if (!isExistingUserPlan)
                    {
                        // add 20 credits for the user
                        await UpdateLatestCredits(user.Id, user.Email, 20);
                    }
                }
                else
                {
                    // remove ads for the plan
                    var adsSettings = await wepdb.UserSettings.FirstOrDefaultAsync(w => w.UserId == user.Id && w.SettingId == CommonData.ShowAdsSettingId);
                    if (adsSettings != null)
                    {
                        adsSettings.Value = "0";
                        await wepdb.SaveChangesAsync();
                    }
                }
            }
            catch (Exception ex)
            {

            }
        }
        [Route("C61661F5")]
        [HttpPost]
        public async Task<IActionResult> HandlePaymentWebhook()
        {
            try
            {
                var json = await new StreamReader(HttpContext.Request.Body).ReadToEndAsync();
                try
                {
                    var stripeSignature = Request.Headers["Stripe-Signature"].FirstOrDefault();

                    // Check if the Stripe-Signature header is present
                    if (string.IsNullOrEmpty(stripeSignature))
                    {
                        return BadRequest("Missing Stripe-Signature header.");
                    }
                    var webhookSecret = _configuration["stripeSecCode"];
                    // var webhookSecret = "whsec_vIsX1gjZk47FJihjL3pbOSufINvHwA9n";
                    //  var webhookSecret = "whsec_bb70e7a0fed9b3ad9ede7f52588f7cfa22c5434e3d221038e211572a123d753d";

                    var stripeEvent = EventUtility.ConstructEvent(
                        json,
                        stripeSignature,
                        webhookSecret,
                        throwOnApiVersionMismatch: false
                    );
                    var stripeKey = _configuration["stripeKey"];

                    Stripe.StripeConfiguration.ApiKey = stripeKey;
                    // Handle the event
                    if (stripeEvent.Type == Stripe.Events.CheckoutSessionCompleted)
                    {
                        var session = stripeEvent.Data.Object as Session;
                        var customerService = new CustomerService();
                        var customer = customerService.Get(session.CustomerId);
                        var customerEmail = customer.Email;
                        var user = await wepdb.Users.AsNoTracking().FirstOrDefaultAsync(w => w.Email.ToLower() == customerEmail.ToLower() && w.AppId == CommonData.GlobalAppId);
                        await HandleTrialPart(session, user);

                        if (session.SubscriptionId == null)
                        {
                            var sessionService = new SessionService();
                            var lineItems = sessionService.ListLineItems(session.Id);
                            foreach (var item in lineItems.Data)
                            {
                                var priceId = item.Price.Id;
                                var quantity = item.Quantity;

                                // Optionally, fetch the product name using the ProductService
                                var productService = new ProductService();
                                var priceService = new PriceService();

                                var price = priceService.Get(priceId);
                                var product = productService.Get(price.ProductId);
                                var productName = product.Name;

                                if (productName.ToLower().Contains("credits"))
                                {
                                    try
                                    {
                                        int qty = (int)quantity * 10;
                                        await UpdateLatestCredits(user.Id, user.Email, qty);
                                    }
                                    catch (Exception ex)
                                    {

                                    }
                                }
                            }
                        }
                    }
                    if (stripeEvent.Type == Stripe.Events.InvoicePaymentSucceeded)
                    {
                        // payment done after the trial
                        var invoice = stripeEvent.Data.Object as Invoice;
                        if (invoice != null && invoice.SubscriptionId != null)
                        {
                            var customerId = invoice.CustomerId;
                            var customerService = new CustomerService();
                            var customer = customerService.Get(customerId);
                            // The payment after the trial has succeeded
                            var subscriptionService = new SubscriptionService();
                            var subscription = subscriptionService.Get(invoice.SubscriptionId);

                            // Handle the active subscription (e.g., mark as active in your system)
                            var subscriptionItem = subscription.Items.Data.FirstOrDefault();
                            // Fetch the Product object to get the Product Name
                            var productService = new ProductService();
                            var product = productService.Get(subscriptionItem.Plan.ProductId);
                            var user = await wepdb.Users.AsNoTracking().FirstOrDefaultAsync(w => w.Email.ToLower() == customer.Email.ToLower() && w.AppId == CommonData.GlobalAppId);
                            // The product name
                            var plan = product.Name;

                            var checkFromOffers = await weadb.Plans.AsNoTracking().AnyAsync(w => w.Name.ToLower() == plan.ToLower());
                            if (checkFromOffers)
                            {
                                var offerDetail = await weadb.Plans.AsNoTracking().FirstOrDefaultAsync(w => w.Name.ToLower() == plan.ToLower());

                                // add the userPlan
                                var checkForUserPlan = await wepdb.UserPlans.AsNoTracking().AnyAsync(w => w.PlanId == offerDetail.Id && w.UserId == user.Id && w.IsActive == true);
                                if (!checkForUserPlan)
                                {
                                    // checkForOtherUserPlam
                                    var existingOtherPlans = await wepdb.UserPlans.Where(w => w.UserId == user.Id && w.PlanId != offerDetail.Id && w.IsActive == true).ToListAsync();
                                    if (existingOtherPlans.Count > 0)
                                    {
                                        foreach (var existingPlan in existingOtherPlans)
                                        {
                                            existingPlan.IsActive = false;
                                            existingPlan.ModifiedDate = DateTime.UtcNow;
                                            await wepdb.SaveChangesAsync();
                                        }
                                    }

                                    UserPlan userPlan = new UserPlan()
                                    {
                                        Id = Guid.NewGuid(),
                                        CreatedDate = DateTime.UtcNow,
                                        IsActive = true,
                                        ModifiedDate = DateTime.UtcNow,
                                        PlanId = offerDetail.Id,
                                        UserId = user.Id
                                    };
                                    wepdb.UserPlans.Add(userPlan);
                                    await wepdb.SaveChangesAsync();
                                }
                                else
                                {
                                    var userPlanData = await wepdb.UserPlans.FirstOrDefaultAsync(w => w.PlanId == offerDetail.Id && w.UserId == user.Id && w.IsActive == true);
                                    userPlanData.IsActive = true;
                                    userPlanData.ModifiedDate = DateTime.UtcNow;
                                    await wepdb.SaveChangesAsync();
                                }
                                if (offerDetail.Id == CommonData.FreemiumPlanId)
                                {
                                    // enable ads for the plan
                                    var adsSettings = await wepdb.UserSettings.FirstOrDefaultAsync(w => w.UserId == user.Id && w.SettingId == CommonData.ShowAdsSettingId);
                                    if (adsSettings != null)
                                    {
                                        adsSettings.Value = "1";
                                        await wepdb.SaveChangesAsync();
                                    }

                                    // check for the existing plan
                                    var isExistingUserPlan = await wepdb.UserPlans.AsNoTracking().AnyAsync(w => w.UserId == user.Id && w.PlanId != CommonData.FreemiumPlanId);
                                    if (!isExistingUserPlan)
                                    {
                                        // add 20 credits for the user
                                        await UpdateLatestCredits(user.Id, user.Email, 20);
                                    }
                                }
                                else
                                {
                                    if (subscription.Status != "trialing")
                                    {
                                        // update the credits
                                        int creditsToAdd = int.Parse(offerDetail.Credits);
                                        await UpdateLatestCredits(user.Id, user.Email, creditsToAdd);

                                        // remove ads for the plan
                                        var adsSettings = await wepdb.UserSettings.FirstOrDefaultAsync(w => w.UserId == user.Id && w.SettingId == CommonData.ShowAdsSettingId);
                                        if (adsSettings != null)
                                        {
                                            adsSettings.Value = "0";
                                            await wepdb.SaveChangesAsync();
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if (stripeEvent.Type == Stripe.Events.CustomerSubscriptionUpdated)
                    {
                        var subscription = stripeEvent.Data.Object as Subscription;
                        var customerId = subscription.CustomerId;
                        var customerService = new CustomerService();
                        var customer = customerService.Get(customerId);
                        var customerEmail = customer.Email;
                        var user = await wepdb.Users.FirstOrDefaultAsync(w => w.Email.ToLower() == customer.Email.ToLower() && w.AppId == CommonData.GlobalAppId);
                        var subscriptionItem = subscription.Items.Data.FirstOrDefault();
                        // Fetch the Product object to get the Product Name
                        var productService = new ProductService();
                        var product = productService.Get(subscriptionItem.Plan.ProductId);

                        // The product name
                        var plan = product.Name;
                        var offerDetail = await weadb.Plans.AsNoTracking().FirstOrDefaultAsync(w => w.Name.ToLower() == plan.ToLower());
                        // Check if the subscription status has changed
                        if (subscription.Status == "canceled")
                        {
                            // Handle the canceled subscription
                            var userPlanData = await wepdb.UserPlans.FirstOrDefaultAsync(w => w.PlanId == offerDetail.Id && w.UserId == user.Id && w.IsActive == true);
                            if (userPlanData != null)
                            {
                                userPlanData.IsActive = false;
                                userPlanData.ModifiedDate = DateTime.UtcNow;
                                await wepdb.SaveChangesAsync();
                            }
                            // check for freemium plan
                            var checkForFreemium = await wepdb.UserPlans.AsNoTracking().AnyAsync(w => w.UserId == user.Id && w.IsActive == true && w.PlanId == CommonData.FreemiumPlanId);
                            if (!checkForFreemium)
                            {
                                // assign the freemium plan for the user
                                UserPlan userPlan = new UserPlan()
                                {
                                    CreatedDate = DateTime.UtcNow,
                                    Id = Guid.NewGuid(),
                                    IsActive = true,
                                    ModifiedDate = DateTime.UtcNow,
                                    PlanId = CommonData.FreemiumPlanId,
                                    UserId = user.Id
                                };
                                wepdb.UserPlans.Add(userPlan);
                                await wepdb.SaveChangesAsync();
                            }
                            // enable ads for the plan
                            var adsSettings = await wepdb.UserSettings.FirstOrDefaultAsync(w => w.UserId == user.Id && w.SettingId == CommonData.ShowAdsSettingId);
                            if (adsSettings != null)
                            {
                                adsSettings.Value = "1";
                                await wepdb.SaveChangesAsync();
                            }
                        }
                    }
                    if (stripeEvent.Type == Stripe.Events.CustomerSubscriptionDeleted)
                    {
                        var subscription = stripeEvent.Data.Object as Subscription;
                        var customerId = subscription.CustomerId;
                        var customerService = new CustomerService();
                        var customer = customerService.Get(customerId);
                        var customerEmail = customer.Email;
                        var user = await wepdb.Users.FirstOrDefaultAsync(w => w.Email.ToLower() == customer.Email.ToLower() && w.AppId == CommonData.GlobalAppId);
                        var subscriptionItem = subscription.Items.Data.FirstOrDefault();
                        // Fetch the Product object to get the Product Name
                        var productService = new ProductService();
                        var product = productService.Get(subscriptionItem.Plan.ProductId);

                        // The product name
                        var plan = product.Name;
                        var offerDetail = await weadb.Plans.AsNoTracking().FirstOrDefaultAsync(w => w.Name.ToLower() == plan.ToLower());
                        // Check if the subscription status has changed
                        if (subscription.Status == "canceled")
                        {
                            // Handle the canceled subscription
                            var userPlanData = await wepdb.UserPlans.FirstOrDefaultAsync(w => w.PlanId == offerDetail.Id && w.UserId == user.Id && w.IsActive == true);
                            if (userPlanData != null)
                            {
                                userPlanData.IsActive = false;
                                userPlanData.ModifiedDate = DateTime.UtcNow;
                                await wepdb.SaveChangesAsync();
                            }
                            // check for freemium plan
                            var checkForFreemium = await wepdb.UserPlans.AsNoTracking().AnyAsync(w => w.UserId == user.Id && w.IsActive == true && w.PlanId == CommonData.FreemiumPlanId);
                            if (!checkForFreemium)
                            {
                                // assign the freemium plan for the user
                                UserPlan userPlan = new UserPlan()
                                {
                                    CreatedDate = DateTime.UtcNow,
                                    Id = Guid.NewGuid(),
                                    IsActive = true,
                                    ModifiedDate = DateTime.UtcNow,
                                    PlanId = CommonData.FreemiumPlanId,
                                    UserId = user.Id
                                };
                                wepdb.UserPlans.Add(userPlan);
                                await wepdb.SaveChangesAsync();
                            }
                            // enable ads for the plan
                            var adsSettings = await wepdb.UserSettings.FirstOrDefaultAsync(w => w.UserId == user.Id && w.SettingId == CommonData.ShowAdsSettingId);
                            if (adsSettings != null)
                            {
                                adsSettings.Value = "1";
                                await wepdb.SaveChangesAsync();
                            }
                        }
                    }

                }
                catch (Exception ex)
                {
                    TelegramService.SendMessageToGaganTestBot("Stripe webhook exception: " + ex.ToString());
                }
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest();
            }
        }
        private async Task UpdateLatestCredits(Guid userId, string userEmail, int credits)
        {
            try
            {
                var EmailSplit = userEmail.Split('@');
                var domain = EmailSplit[1];
                var isFreeDomain = await weddb.FreeDomains.AsNoTracking().AnyAsync(w => w.DomainName.ToLower() == domain.ToLower());
                if (isFreeDomain)
                {
                    var isCreditPresent = await wepdb.UserSettings.Where(x => x.UserId == userId && x.SettingId == CommonData.NonVanishingCreditSettingId).FirstOrDefaultAsync();
                    if (isCreditPresent != null)
                    {
                        isCreditPresent.Value = (int.Parse(isCreditPresent.Value) + credits).ToString();
                        await wepdb.SaveChangesAsync();
                    }
                    else
                    {
                        UserSetting creditSetting = new UserSetting();
                        creditSetting.Id = Guid.NewGuid();
                        creditSetting.UserId = userId;
                        creditSetting.SettingId = CommonData.VanishingCreditSettingId;
                        creditSetting.CreatedDate = DateTime.UtcNow;
                        creditSetting.Value = credits.ToString();
                        creditSetting.ModifiedDate = DateTime.UtcNow;
                        creditSetting.IsActive = true;
                        creditSetting.IsUserUpdated = true;
                        creditSetting.IsAdminUpdated = true;
                        wepdb.UserSettings.Add(creditSetting);
                        await wepdb.SaveChangesAsync();
                    }
                    CreditUsage creditUsage = new CreditUsage();
                    creditUsage.Id = Guid.NewGuid();
                    creditUsage.UserId = userId;
                    creditUsage.RedeemDateTime = DateTime.UtcNow;
                    creditUsage.Credit = credits.ToString();
                    creditUsage.ValidTill = DateTime.UtcNow.AddYears(1);
                    creditUsage.IsCredited = true;
                    weodb.CreditUsages.Add(creditUsage);
                    await weodb.SaveChangesAsync();
                }
                else
                {
                    var org = await weodb.Orgs.Where(x => x.Name.ToLower() == domain.ToLower() && x.AppId.ToLower() == CommonData.GlobalAppId.ToString().ToLower() && x.IsActive == true).AsNoTracking().FirstOrDefaultAsync();
                    if (org != null)
                    {
                        var orgCreditUsage = await weodb.OrgCreditsNews.Where(x => x.OrgId == org.Id).FirstOrDefaultAsync();
                        if (orgCreditUsage != null)
                        {
                            int creditsToUpdate = int.Parse(orgCreditUsage.Credits) + credits;
                            orgCreditUsage.Credits = creditsToUpdate.ToString();
                            await weodb.SaveChangesAsync();

                            CreditUsage creditUsage = new CreditUsage();
                            creditUsage.Id = Guid.NewGuid();
                            creditUsage.UserId = userId;
                            creditUsage.RedeemDateTime = DateTime.UtcNow;
                            creditUsage.Credit = credits.ToString();
                            creditUsage.ValidTill = DateTime.UtcNow.AddYears(1);
                            creditUsage.IsCredited = true;
                            weodb.CreditUsages.Add(creditUsage);
                            await weodb.SaveChangesAsync();
                        }
                        else
                        {
                            OrgCreditsNew orgCredits = new OrgCreditsNew()
                            {
                                Credits = credits.ToString(),
                                Id = Guid.NewGuid(),
                                LastPurchaseDate = DateTime.UtcNow,
                                OrgId = org.Id
                            };
                            weodb.OrgCreditsNews.Add(orgCredits);
                            await weodb.SaveChangesAsync();

                            CreditUsage creditUsage = new CreditUsage();
                            creditUsage.Id = Guid.NewGuid();
                            creditUsage.UserId = userId;
                            creditUsage.RedeemDateTime = DateTime.UtcNow;
                            creditUsage.Credit = credits.ToString();
                            creditUsage.ValidTill = DateTime.UtcNow.AddYears(1);
                            creditUsage.IsCredited = true;
                            weodb.CreditUsages.Add(creditUsage);
                            await weodb.SaveChangesAsync();
                        }
                    }
                }
            }
            catch (Exception ex)
            {

            }
        }
        private async Task HandleTrialPart(Session session, User user)
        {
            try
            {
                if (!string.IsNullOrEmpty(session.SubscriptionId))
                {
                    var subscriptionService = new SubscriptionService();
                    var subscription = subscriptionService.Get(session.SubscriptionId);

                    var subscriptionItem = subscription.Items.Data.FirstOrDefault();
                    if (subscriptionItem != null)
                    {
                        if (subscription.Status == "trialing")
                        {
                            // update the usersettings
                            // start trial 
                            var userTrialSetting = await wepdb.UserSettings.FirstOrDefaultAsync(w => w.UserId == user.Id && w.SettingId == CommonData.UserTrialStartDateSettingId);
                            if (userTrialSetting != null)
                            {
                                // trial starts from now
                                userTrialSetting.Value = DateTime.UtcNow.ToString();
                                userTrialSetting.ModifiedDate = DateTime.UtcNow;
                                await wepdb.SaveChangesAsync();
                            }
                            var trialDaysSetting = await wepdb.UserSettings.FirstOrDefaultAsync(w => w.UserId == user.Id && w.SettingId == CommonData.UserTrialDaysSettingId);
                            if (trialDaysSetting != null)
                            {
                                int trialDays = int.Parse(trialDaysSetting.Value.ToString());
                                // create signal to end the trial
                                Signal signal = new Signal()
                                {
                                    UserId = user.Id,
                                    CreatedDate = DateTime.UtcNow,
                                    Id = Guid.NewGuid(),
                                    IsActive = true,
                                    IsComplete = false,
                                    IsProcessing = false,
                                    IsSeen = false,
                                    Iteration = 0,
                                    ModifiedDate = DateTime.UtcNow,
                                    SignalTypeId = CommonData.TrialStatusUpdateSignalTypeId,
                                    TriggerOn = DateTime.UtcNow.AddDays(trialDays)
                                };
                                wepdb.Signals.Add(signal);
                                await wepdb.SaveChangesAsync();
                            }
                            await UpdateForCredits(user);

                            // Fetch the Product object to get the Product Name
                            var productService = new ProductService();
                            var product = productService.Get(subscriptionItem.Plan.ProductId);

                            // The product name
                            var plan = product.Name;

                            //   var plan = subscriptionItem.Plan.Nickname;
                            var checkFromOffers = await weadb.Plans.AsNoTracking().AnyAsync(w => w.Name.ToLower() == plan.ToLower());
                            if (checkFromOffers)
                            {
                                var offerDetail = await weadb.Plans.AsNoTracking().FirstOrDefaultAsync(w => w.Name.ToLower() == plan.ToLower());

                                // add the userPlan
                                var checkForUserPlan = await wepdb.UserPlans.AsNoTracking().AnyAsync(w => w.PlanId == offerDetail.Id && w.UserId == user.Id && w.IsActive == true);
                                if (!checkForUserPlan)
                                {
                                    // checkForOtherUserPlam
                                    var existingOtherPlans = await wepdb.UserPlans.Where(w => w.UserId == user.Id && w.PlanId != offerDetail.Id && w.IsActive == true).ToListAsync();
                                    if (existingOtherPlans.Count > 0)
                                    {
                                        foreach (var existingPlan in existingOtherPlans)
                                        {
                                            existingPlan.IsActive = false;
                                            existingPlan.ModifiedDate = DateTime.UtcNow;
                                            await wepdb.SaveChangesAsync();
                                        }
                                    }
                                    UserPlan userPlan = new UserPlan()
                                    {
                                        Id = Guid.NewGuid(),
                                        CreatedDate = DateTime.UtcNow,
                                        IsActive = true,
                                        ModifiedDate = DateTime.UtcNow,
                                        PlanId = offerDetail.Id,
                                        UserId = user.Id
                                    };
                                    wepdb.UserPlans.Add(userPlan);
                                    await wepdb.SaveChangesAsync();
                                }
                                else
                                {
                                    var userPlanData = await wepdb.UserPlans.FirstOrDefaultAsync(w => w.PlanId == offerDetail.Id && w.UserId == user.Id && w.IsActive == true);
                                    userPlanData.IsActive = true;
                                    userPlanData.ModifiedDate = DateTime.UtcNow;
                                    await wepdb.SaveChangesAsync();
                                }
                                if (offerDetail.Id == CommonData.FreemiumPlanId)
                                {
                                    // enable ads for the plan
                                    var adsSettings = await wepdb.UserSettings.FirstOrDefaultAsync(w => w.UserId == user.Id && w.SettingId == CommonData.ShowAdsSettingId);
                                    if (adsSettings != null)
                                    {
                                        adsSettings.Value = "1";
                                        await wepdb.SaveChangesAsync();
                                    }
                                    // check for the existing plan
                                    var isExistingUserPlan = await wepdb.UserPlans.AsNoTracking().AnyAsync(w => w.UserId == user.Id && w.PlanId != CommonData.FreemiumPlanId);
                                    if (!isExistingUserPlan)
                                    {
                                        // add 20 credits for the user
                                        await UpdateLatestCredits(user.Id, user.Email, 20);
                                    }
                                }
                                else
                                {
                                    // remove ads for the plan
                                    var adsSettings = await wepdb.UserSettings.FirstOrDefaultAsync(w => w.UserId == user.Id && w.SettingId == CommonData.ShowAdsSettingId);
                                    if (adsSettings != null)
                                    {
                                        adsSettings.Value = "0";
                                        await wepdb.SaveChangesAsync();
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {

            }
        }
        public async Task UpdateForCredits(User user)
        {
            try
            {
                var EmailSplit = user.Email.Split('@');
                var domain = EmailSplit[1].ToLower();
                var isFreeDomain = await weddb.FreeDomains.AsNoTracking().AnyAsync(w => w.DomainName.ToLower() == domain.ToLower());
                if (isFreeDomain)
                {
                    Guid userId = user.Id;
                    var isCreditPresent = await wepdb.UserSettings.Where(x => x.UserId == userId && x.SettingId == CommonData.NonVanishingCreditSettingId).FirstOrDefaultAsync();
                    if (isCreditPresent != null)
                    {
                        isCreditPresent.Value = "20";
                        isCreditPresent.ModifiedDate = DateTime.UtcNow;
                        await wepdb.SaveChangesAsync();
                    }
                    else
                    {
                        UserSetting creditSetting = new UserSetting();
                        creditSetting.Id = Guid.NewGuid();
                        creditSetting.UserId = userId;
                        creditSetting.SettingId = CommonData.VanishingCreditSettingId;
                        creditSetting.CreatedDate = DateTime.UtcNow;
                        creditSetting.Value = "20";
                        creditSetting.ModifiedDate = DateTime.UtcNow;
                        creditSetting.IsActive = true;
                        creditSetting.IsUserUpdated = true;
                        creditSetting.IsAdminUpdated = true;
                        wepdb.UserSettings.Add(creditSetting);
                        await wepdb.SaveChangesAsync();
                    }
                    CreditUsage creditUsage = new CreditUsage();
                    creditUsage.Id = Guid.NewGuid();
                    creditUsage.UserId = userId;
                    creditUsage.RedeemDateTime = DateTime.UtcNow;
                    creditUsage.Credit = "20";
                    creditUsage.ValidTill = DateTime.UtcNow.AddYears(1);
                    creditUsage.IsCredited = true;
                    weodb.CreditUsages.Add(creditUsage);
                    await weodb.SaveChangesAsync();
                }
                else
                {
                    var org = await weodb.Orgs.AsNoTracking().Where(x => x.Name.ToLower() == domain.ToLower() && x.AppId.ToLower() == CommonData.GlobalAppId.ToString().ToLower() && x.IsActive == true).AsNoTracking().FirstOrDefaultAsync();
                    if (org != null)
                    {
                        var orgCreditUsage = await weodb.OrgCreditsNews.Where(x => x.OrgId == org.Id).FirstOrDefaultAsync();
                        if (orgCreditUsage != null)
                        {
                            int creditsToUpdate = int.Parse(orgCreditUsage.Credits) + 20;
                            orgCreditUsage.Credits = creditsToUpdate.ToString();
                            await weodb.SaveChangesAsync();

                            CreditUsage creditUsage = new CreditUsage();
                            creditUsage.Id = Guid.NewGuid();
                            creditUsage.UserId = user.Id;
                            creditUsage.RedeemDateTime = DateTime.UtcNow;
                            creditUsage.Credit = "20";
                            creditUsage.ValidTill = DateTime.UtcNow.AddYears(1);
                            creditUsage.IsCredited = true;
                            weodb.CreditUsages.Add(creditUsage);
                            await weodb.SaveChangesAsync();
                        }
                        else
                        {
                            OrgCreditsNew orgCredits = new OrgCreditsNew()
                            {
                                Credits = "20",
                                Id = Guid.NewGuid(),
                                LastPurchaseDate = DateTime.UtcNow,
                                OrgId = org.Id
                            };
                            weodb.OrgCreditsNews.Add(orgCredits);
                            await weodb.SaveChangesAsync();

                            CreditUsage creditUsage = new CreditUsage();
                            creditUsage.Id = Guid.NewGuid();
                            creditUsage.UserId = user.Id;
                            creditUsage.RedeemDateTime = DateTime.UtcNow;
                            creditUsage.Credit = "20";
                            creditUsage.ValidTill = DateTime.UtcNow.AddYears(1);
                            creditUsage.IsCredited = true;
                            weodb.CreditUsages.Add(creditUsage);
                            await weodb.SaveChangesAsync();
                        }
                    }
                }
            }
            catch (Exception ex)
            {

            }
        }
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                wepdb.Dispose();
                weddb.Dispose();
                weadb.Dispose();
                wetdb.Dispose();
                weodb.Dispose();
            }
            base.Dispose(disposing);
        }

        // public async Task CheckAndCreditsInCreditsOrgNew(User user)
        // {
        //     try
        //     {
        //         var checkForCoupons = await wepdb.UserCoupons.AnyAsync(w => w.EmailId == user.Email && w.IsActive == true);
        //         if (!checkForCoupons)
        //         {
        //             var splitMail = user.Email.Split('@');
        //             var domain = splitMail[1];
        //             var isOrg = await weodb.Orgs.Where(x => x.Name.ToLower().Contains(domain.ToLower()) && x.IsActive == true && x.AppId.ToLower() == user.AppId.ToString().ToLower()).FirstOrDefaultAsync();
        //             var presentInCreditTable = await weodb.OrgCreditsNews.Where(x => x.OrgId == isOrg.Id).FirstOrDefaultAsync();
        //             if (presentInCreditTable == null)
        //             {
        //                 OrgCreditsNew orgCreditsNew = new OrgCreditsNew();
        //                 orgCreditsNew.Id = Guid.NewGuid();
        //                 orgCreditsNew.Credits = "3";
        //                 orgCreditsNew.LastPurchaseDate = DateTime.UtcNow;
        //                 orgCreditsNew.OrgId = isOrg.Id;
        //                 weodb.OrgCreditsNews.Add(orgCreditsNew);
        //                 weodb.SaveChanges();
        //                 CreditUsage creditUsage = new CreditUsage();
        //                 creditUsage.Id = Guid.NewGuid();
        //                 creditUsage.UserId = user.Id;
        //                 creditUsage.IsCredited = true;
        //                 creditUsage.IsDebited = false;
        //                 creditUsage.Credit = "3";
        //                 weodb.CreditUsages.Add(creditUsage);
        //                 await weodb.SaveChangesAsync();
        //             }
        //         }
        //     }
        //     catch (Exception ex)
        //     {

        //     }
        // }

        public async Task UpdateUserProviders(string email, Guid userId, User user)
        {
            var splitMail = email.Split('@');
            var domain = splitMail[1];
            var isOrg = await weodb.Orgs.Where(x => x.Name.ToLower() == domain.ToLower() && x.IsActive == true && x.AppId.ToLower() == user.AppId.ToString().ToLower()).AsNoTracking().FirstOrDefaultAsync();

            if (isOrg != null)
            {
                var orgProviders = weodb.OrgProviders.Where(x => x.OrgId == isOrg.Id && (x.ProviderId == CommonData.BeanBagProviderId || x.ProviderId == CommonData.ApolloProviderId) && x.IsActive == true).ToList();
                if (orgProviders != null)
                {
                    foreach (var provider in orgProviders)
                    {
                        var provision = new Provision();
                        var existingProvision = weodb.Provisions.Any(w => w.AppId == user.AppId && w.EmailAddress == email
                  && w.ProviderId == provider.ProviderId && w.OrgId == isOrg.Id && w.UserId == userId);
                        if (!existingProvision)
                        {
                            var provision1 = new Provision();
                            provision1.Id = Guid.NewGuid();
                            provision1.OrgId = isOrg.Id; //CommonData.WhatElseCustomerOrgId;
                            provision1.AppId = user.AppId;
                            // provision1.UserCustomerId = user.ChargebeeCustomerId;
                            provision1.ProviderTypeId = weddb.Providers.Where(x => x.Id == provider.ProviderId).Select(x => x.ProviderTypeId).FirstOrDefault();
                            provision1.ProviderId = provider.ProviderId;
                            provision1.UserId = userId;
                            provision1.UserProviderId = Guid.Empty;
                            provision1.CreatedDate = DateTime.UtcNow;
                            provision1.ModifiedDate = DateTime.UtcNow;
                            provision1.IsActive = true;
                            provision1.IsConverted = false;
                            provision1.IsEnterpriseConverted = false;
                            //provision.SanitizedNumber = user.SanitizedNumber;
                            provision1.IsRedeemed = false;
                            //provision.PhoneNumber = user.ProvidedNumber ?? "";
                            //provision.Salutation = user.Salutation;
                            provision1.FirstName = user.FirstName ?? " ";
                            provision1.LastName = user.LastName ?? " ";
                            provision1.MiddleName = user.MiddleName;
                            provision1.CountryId = user.CountryId;
                            provision1.UserId = user.Id;
                            //provision1.IsFree = ;
                            //provision1.IsProvisioned = isProvisioned;
                            provision1.IsPayed = false;
                            provision1.IsRequested = false;
                            // provision1.IsAccepted = isAccepted;
                            provision1.IsPurchasedByUser = false;
                            provision1.IsPurchasedOnAndroid = false;
                            provision1.IsPurchasedOnIos = false;
                            provision1.EmailAddress = email;
                            // provision1.IsClaimed = isClaimed;
                            // provision1.IsTrial = isTrail;
                            weodb.Provisions.Add(provision1);
                            weodb.SaveChanges();

                            provision = provision1;
                        }
                        else
                        {
                            provision = weodb.Provisions.First(w => w.AppId == user.AppId && w.EmailAddress == email
                         && w.ProviderId == provider.ProviderId && w.OrgId == isOrg.Id && w.UserId == userId);
                        }
                        // var userPovider = wepdb.UserProviders.Where(x => x.ProviderId == provider.ProviderId && x.IsActive == true && x.UserId == userId).FirstOrDefault();
                        var userPovider =
                            await _userProviderCaching.GetUserProvidersFromRedisOrDbAsyncWithUserId(userId, provider.ProviderId);
                        if (userPovider == null)
                        {
                            Models.Models.PeopleModel.UserProvider userProviderdata = new Models.Models.PeopleModel.UserProvider();
                            userProviderdata.Id = Guid.NewGuid();
                            userProviderdata.UserId = userId;
                            userProviderdata.AppId = user.AppId;
                            userProviderdata.ProviderId = provider.ProviderId;
                            userProviderdata.IsActive = true;
                            userProviderdata.ActiveFrom = DateTime.UtcNow;
                            userProviderdata.ActiveTill = DateTime.UtcNow.AddYears(1);
                            userProviderdata.CreatedDate = DateTime.UtcNow;
                            userProviderdata.ModifiedDate = DateTime.UtcNow;
                            userProviderdata.ProvisionId = provision.Id;
                            userProviderdata.OrgId = isOrg.Id;
                            userProviderdata.IsAuthenticated = true;
                            userProviderdata.IsFree = false;
                            // wepdb.UserProviders.Add(userProviderdata);
                            // wepdb.SaveChanges();
                            await _userProviderCaching.AddUserProviderInRedisAndDb(userProviderdata);
                            userPovider = userProviderdata;


                        }
                        var orgIdentifiers = weodb.OrgIdentifiers.Where(x => x.OrgProviderId == provider.Id && x.IsActive == true).ToList();
                        if (orgIdentifiers != null)
                        {
                            foreach (var oPI in orgIdentifiers)
                            {
                                var userProvideI = wepdb.UserProviderIdentifiers.Where(x => x.UserProviderId == userPovider.Id && x.IdentifierId == oPI.IdentifierId && x.IsActive == true).FirstOrDefault();
                                if (userProvideI == null)
                                {
                                    Models.Models.PeopleModel.UserProviderIdentifier upI = new Models.Models.PeopleModel.UserProviderIdentifier();
                                    upI.Id = Guid.NewGuid();
                                    upI.IdentifierId = oPI.IdentifierId;
                                    upI.UserProviderId = userPovider.Id;
                                    upI.Value = oPI.Value;
                                    upI.IsActive = true;
                                    upI.CreatedDate = DateTime.UtcNow;
                                    upI.ModifiedDate = DateTime.UtcNow;
                                    await _userProviderCaching.AddUserProviderIdentifierInRedisAndDb(upI);
                                    // wepdb.UserProviderIdentifiers.Add(upI);
                                    // wepdb.SaveChanges();
                                }
                            }
                        }

                    }
                }
            }
        }
    }


}
