using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.HttpsPolicy;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.OpenApi.Models;
using OnePage.Common;
using OnePage.Models.Models;
using OnePage.Models.Models.AdminModel;
using OnePage.Models.Models.DataModel;
using OnePage.Models.Models.OrgModel;
using OnePage.Models.Models.PeopleModel;
using OnePage.Models.Models.TokenModel;
using OnePage.Register.Interfaces;
using OnePage.Register.Services;
using OnePage.Services;
using OnePage.Services.Interfaces;
//using OnePage.Register.Membership;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Amazon.Extensions.NETCore.Setup;
using Amazon.SQS;
using Microsoft.EntityFrameworkCore;
using OnePage.Models.Models.ENTEventModel;

namespace OnePage.Register
{
    public class Startup
    {
        // DataContext weddb;
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
            // weddb = new DataContext();
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production";
            Console.WriteLine($"Environment: {environment}");

            var appSettingsFile = $"appsettings.{environment}.json";
            Console.WriteLine($"Loading configuration from: {appSettingsFile}");

            var configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", optional: false)
                .AddJsonFile(appSettingsFile, optional: true)
                .AddEnvironmentVariables()
                .Build();

            services.AddScoped<UserProviderCaching>();
            services.AddControllersWithViews();
            services.AddScoped<ITelemetryTracker, TelemetryTracker>();
            services.AddHttpClient("WebClient", client => client.Timeout = TimeSpan.FromSeconds(600));
            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo { Title = "OnePage.Register", Version = "v1" });
                c.AddSecurityDefinition("GuidAuth", new OpenApiSecurityScheme
                {
                    Description = "Guid Auth Token",
                    Name = "authToken",
                    In = ParameterLocation.Header,
                    Type = SecuritySchemeType.ApiKey,
                    Scheme = "GuidAuth"
                });
                c.AddSecurityRequirement(new OpenApiSecurityRequirement
{
    {
        new OpenApiSecurityScheme
        {
            Reference = new OpenApiReference
            {
                Type = ReferenceType.SecurityScheme,
                Id = "GuidAuth"
            }
        },
        new string[] { }
    }
});
            });

        
            // services.AddTransient<OrgContext>();
            // services.AddTransient<DataContext>();
            // services.AddTransient<PeopleContext>();
            // services.AddTransient<AdminContext>();
            // services.AddTransient<TokenContext>();
            // services.AddTransient<ContactContext>();
            // services.AddTransient<EventRDSContext>();
            services.AddDbContext<OrgContext>(options =>
                options.UseSqlServer(Configuration["DBConnections:WEOrgConnectionString"]));

            services.AddDbContext<DataContext>(options =>
                options.UseSqlServer(Configuration["DBConnections:WEDataConnectionString"]));

            services.AddDbContext<PeopleContext>(options =>
                options.UseSqlServer(Configuration["DBConnections:WEPeopleConnectionString"]));

            services.AddDbContext<AdminContext>(options =>
                options.UseSqlServer(Configuration["DBConnections:WEAdminConnectionString"]));

            services.AddDbContext<TokenContext>(options =>
                options.UseSqlServer(Configuration["DBConnections:WETokenConnectionString"]));

            services.AddDbContext<ContactContext>(options =>
                options.UseSqlServer(Configuration["DBConnections:WEContactConnectionString"]));

            services.AddDbContext<EventRDSContext>(options =>
                options.UseSqlServer(Configuration["DBConnections:WEEventRDSConnectionString"]));
            services.AddDbContext<ENTEventContext>(option =>
                option.UseSqlServer(Configuration["DBConnections:WEEventRDSConnectionString"]));


            services.AddCors();
            services.AddApplicationInsightsTelemetry(Configuration["APPINSIGHTS_CONNECTIONSTRING"]);
            services.AddHttpContextAccessor();
            services.AddScoped<IRedisCaching, RedisCaching>();
            services.AddScoped<ProviderService>();
            services.AddScoped<ICustomCacheService, CustomCacheService>();
            services.AddScoped<MetricsService>();
            services.AddScoped<CalendarService>();
            services.AddScoped<ResearchService>();
            services.AddScoped<ContactService>();
            services.AddScoped<AuthService>();
            services.AddScoped<BotService>();

            var awsOptions = new AWSOptions
            {
                Profile = "default", 
                Region = Amazon.RegionEndpoint.EUWest3 
            };

            
            services.AddDefaultAWSOptions(awsOptions);



            services.AddAWSService<IAmazonSQS>();
            services.AddScoped<ISqsMessageSender, SqsMessageSender>();

            // If using IIS:
            services.Configure<IISServerOptions>(options =>
            {
                options.AllowSynchronousIO = true;
            });
            //services.AddScoped<IMembershipService, OnePageMembershipProvider>();
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }
            else
            {
                app.UseExceptionHandler("/Home/Error");
                // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
                app.UseHsts();
            }

            app.UseSwagger();
            app.UseCors();
            app.UseCors(builder =>
            {
                builder.WithOrigins(new string[] { "https://eu-fc-ap-wc.azurewebsites.net/", "http://localhost:4200/", "https://funnel-2ae28.web.app", "http://get1page.com", "http://www.go1.page.com", "http://get1page.net", "http://www.go1.page.net" })
                .AllowAnyOrigin()
                .AllowAnyMethod()
                .AllowAnyHeader();
            });
            app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", "OnePage.Register v1")
            );

            app.UseHttpsRedirection();
            app.UseStaticFiles();

            app.UseRouting();

            app.UseAuthentication();
            app.UseAuthorization();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllerRoute(
                    name: "default",
                    pattern: "{controller=Home}/{action=Index}/{id?}");
            });
            LoadGenericValues();
        }
        public void LoadGenericValues()
        {
            try
            {
                var optionsBuilder = new DbContextOptionsBuilder<DataContext>();
                optionsBuilder.UseSqlServer(Configuration["DBConnections:WEDataConnectionString"]);

                using (var dbContext = new DataContext(optionsBuilder.Options))
                {
                    // Test connection first
                    dbContext.Database.CanConnect();

                    CommonData.headerList = dbContext.Headers.ToList();
                    CommonData.urlList = dbContext.Urls.ToList();
                    CommonData.identifierList = dbContext.Identifiers.ToList();
                    CommonData.providerList = dbContext.Providers.ToList();
                    CommonData.providerUrlList = dbContext.ProviderUrls.ToList();
                }

                Console.WriteLine("LoadGenericValues completed successfully");
            }
            catch (Exception ex)
            {
                // Log the error and initialize empty lists to prevent null reference exceptions
                Console.WriteLine($"LoadGenericValues failed: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");

                // Initialize empty lists to prevent null reference exceptions
                CommonData.headerList = new List<OnePage.Models.Models.DataModel.Header>();
                CommonData.urlList = new List<OnePage.Models.Models.DataModel.Url>();
                CommonData.identifierList = new List<OnePage.Models.Models.DataModel.Identifier>();
                CommonData.providerList = new List<OnePage.Models.Models.DataModel.Provider>();
                CommonData.providerUrlList = new List<OnePage.Models.Models.DataModel.ProviderUrl>();

                // Try to send error to Telegram if possible
                try
                {
                    OnePage.Services.TelegramService.SendMessageToTestBot2($"LoadGenericValues failed: {ex.Message}");
                }
                catch
                {
                    // Ignore telegram errors during startup
                }
            }
        }
    }
}
