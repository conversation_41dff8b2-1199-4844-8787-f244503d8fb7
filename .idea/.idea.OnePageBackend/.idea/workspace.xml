<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoGeneratedRunConfigurationManager">
    <projectFile profileName=".NET Core">16.proactive-messages/ProactiveBot.csproj</projectFile>
    <projectFile pubXmlPath="16.proactive-messages/Properties/PublishProfiles/eu-fc-wb-bt - Web Deploy.pubxml">16.proactive-messages/ProactiveBot.csproj</projectFile>
    <projectFile profileName="OnePage.Activities">OnePage.Activities/OnePage.Activities.csproj</projectFile>
    <projectFile profileName="OnePage.Call">OnePage.Call/OnePage.Call.csproj</projectFile>
    <projectFile profileName="OnePage.CatchAll">OnePage.CatchAll/OnePage.CatchAll.csproj</projectFile>
    <projectFile profileName="OnePage.Connect">OnePage.Connect/OnePage.Connect.csproj</projectFile>
    <projectFile pubXmlPath="OnePage.Connect/Properties/PublishProfiles/eu-fc-wb-at - Web Deploy.pubxml">OnePage.Connect/OnePage.Connect.csproj</projectFile>
    <projectFile profileName="OnePage.CustomerAdminPortal">OnePage.CustomerAdminPortal/OnePage.CustomerAdminPortal.csproj</projectFile>
    <projectFile profileName="OnePage.CustomerOnboarding">OnePage.CustomerOnboarding/OnePage.CustomerOnboarding.csproj</projectFile>
    <projectFile profileName="OnePage.Events">OnePage.Events/OnePage.Events.csproj</projectFile>
    <projectFile pubXmlPath="OnePage.Events/Properties/PublishProfiles/eu-fc-cs-ce - Web Deploy.pubxml">OnePage.Events/OnePage.Events.csproj</projectFile>
    <projectFile profileName="OnePage.GetCardsAndAction">OnePage.GetCardsAndAction/OnePage.GetCardsAndAction.csproj</projectFile>
    <projectFile profileName="OnePage.GetCardsAndActions">OnePage.GetCardsAndActions/OnePage.GetCardsAndActions.csproj</projectFile>
    <projectFile profileName="http">OnePage.InsightApis/OnePage.InsightApis.csproj</projectFile>
    <projectFile profileName="https">OnePage.InsightApis/OnePage.InsightApis.csproj</projectFile>
    <projectFile profileName="OnePage.Insights">OnePage.Insights/OnePage.Insights.csproj</projectFile>
    <projectFile pubXmlPath="OnePage.Insights/Properties/PublishProfiles/eu-fc-ap-in - Web Deploy.pubxml">OnePage.Insights/OnePage.Insights.csproj</projectFile>
    <projectFile profileName=".NET Core">OnePage.MessagingExtensionBot/OnePage.MessagingExtensionBot.csproj</projectFile>
    <projectFile pubXmlPath="OnePage.MessagingExtensionBot/Properties/PublishProfiles/eu-ne-cp-bt - Web Deploy.pubxml">OnePage.MessagingExtensionBot/OnePage.MessagingExtensionBot.csproj</projectFile>
    <projectFile pubXmlPath="OnePage.MessagingExtensionBot/Properties/PublishProfiles/eu-ne-wb-bt - Web Deploy.pubxml">OnePage.MessagingExtensionBot/OnePage.MessagingExtensionBot.csproj</projectFile>
    <projectFile profileName="OnePage.NewOpp">OnePage.NewOpp/OnePage.NewOpp.csproj</projectFile>
    <projectFile profileName="OnePage.OnDemand">OnePage.OnDemand/OnePage.OnDemand.csproj</projectFile>
    <projectFile pubXmlPath="OnePage.OnDemand/Properties/PublishProfiles/eu-fc-ap-on - Web Deploy.pubxml">OnePage.OnDemand/OnePage.OnDemand.csproj</projectFile>
    <projectFile profileName="OnePage.OneTime">OnePage.OneTime/OnePage.OneTime.csproj</projectFile>
    <projectFile pubXmlPath="OnePage.OneTime/Properties/PublishProfiles/eu-fc-ap-ut - Web Deploy.pubxml">OnePage.OneTime/OnePage.OneTime.csproj</projectFile>
    <projectFile profileName="OnePage.Register">OnePage.Register/OnePage.Register.csproj</projectFile>
    <projectFile pubXmlPath="OnePage.Register/Properties/PublishProfiles/eu-fc-ap-lr - Web Deploy.pubxml">OnePage.Register/OnePage.Register.csproj</projectFile>
    <projectFile profileName="OnePage.ServerZone">OnePage.ServerZone/OnePage.ServerZone.csproj</projectFile>
    <projectFile pubXmlPath="OnePage.ServerZone/Properties/PublishProfiles/eu-fc-ap-sz - Web Deploy.pubxml">OnePage.ServerZone/OnePage.ServerZone.csproj</projectFile>
    <projectFile profileName="OnePage.Signalr">OnePage.Signalr/OnePage.Signalr.csproj</projectFile>
    <projectFile profileName="OnePage.Sync">OnePage.Sync/OnePage.Sync.csproj</projectFile>
    <projectFile pubXmlPath="OnePage.Sync/Properties/PublishProfiles/eu-fc-ap-sy - Web Deploy.pubxml">OnePage.Sync/OnePage.Sync.csproj</projectFile>
    <projectFile profileName="OnePage.TeamsMessagingExtension">OnePage.TeamsMessagingExtension/OnePage.TeamsMessagingExtension.csproj</projectFile>
    <projectFile profileName="OnePage.WebAdmin">OnePage.WebAdmin/OnePage.WebAdmin.csproj</projectFile>
    <projectFile pubXmlPath="OnePage.WebAdmin/Properties/PublishProfiles/eu-fc-wb-ad - Web Deploy.pubxml">OnePage.WebAdmin/OnePage.WebAdmin.csproj</projectFile>
    <projectFile profileName="OnePage.WebApp">OnePage.WebApp/OnePage.WebApp.csproj</projectFile>
    <projectFile pubXmlPath="OnePage.WebApp/Properties/PublishProfiles/eu-fc-ap-wc - Web Deploy.pubxml">OnePage.WebApp/OnePage.WebApp.csproj</projectFile>
    <projectFile profileName="OnePage.WebAppEnterprise">OnePage.WebAppEnterprise/OnePage.WebAppEnterprise.csproj</projectFile>
    <projectFile profileName="OnePage.WebProxy">OnePage.WebProxy/OnePage.WebProxy.csproj</projectFile>
    <projectFile pubXmlPath="OnePage.WebProxy/Properties/PublishProfiles/eu-ne-ap-wp - Web Deploy.pubxml">OnePage.WebProxy/OnePage.WebProxy.csproj</projectFile>
    <projectFile profileName="OnePage.Webhook.MC.Ent">OnePage.Webhook.MC.Ent/OnePage.Webhook.MC.Ent.csproj</projectFile>
    <projectFile profileName="OnePage.Webhook.MC">OnePage.Webhook.MC/OnePage.Webhook.MC.csproj</projectFile>
    <projectFile profileName="OnePage.Webhook.Microsoft.Enterprise">OnePage.Webhook.Microsoft.Enterprise/OnePage.Webhook.Microsoft.Enterprise.csproj</projectFile>
    <projectFile>OnePage.Webhook.Microsoft/OnePage.Webhook.Microsoft.csproj</projectFile>
    <projectFile profileName="OnePage.Webhook.Microsoft2">OnePage.Webhook.Microsoft2/OnePage.Webhook.Microsoft2.csproj</projectFile>
    <projectFile pubXmlPath="OnePage.Webhook.Microsoft2/Properties/PublishProfiles/eu-fc-fn-mg - Zip Deploy.pubxml">OnePage.Webhook.Microsoft2/OnePage.Webhook.Microsoft2.csproj</projectFile>
    <projectFile profileName="OnePage.WebhookCl">OnePage.WebhookCl/OnePage.WebhookCl.csproj</projectFile>
    <projectFile profileName="OnePageBackend">OnePageBackend/OnePage.Contacts.csproj</projectFile>
    <projectFile pubXmlPath="OnePageBackend/Properties/PublishProfiles/eu-fc-cs-co - Web Deploy.pubxml">OnePageBackend/OnePage.Contacts.csproj</projectFile>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="3eb552b8-aa11-4929-990f-ec0bf0bca2af" name="Changes" comment="&quot;changed appsettings for staging&quot;">
      <change beforePath="$PROJECT_DIR$/.idea/.idea.OnePageBackend/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/.idea.OnePageBackend/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/OnePage.OneTime/Startup.cs" beforeDir="false" afterPath="$PROJECT_DIR$/OnePage.OneTime/Startup.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/OnePage.OneTime/appsettings.Development.json" beforeDir="false" afterPath="$PROJECT_DIR$/OnePage.OneTime/appsettings.Development.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/OnePage.WebApp/Controllers/AuthController.cs" beforeDir="false" afterPath="$PROJECT_DIR$/OnePage.WebApp/Controllers/AuthController.cs" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
<<<<<<< HEAD
=======
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/620bc7f42ca5470aa37fc87957c5d0db209800/42/3d946322/ActionMethodExecutor.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/6e3212d57d1d4cfc9914680fd43feb3856790/5e/0d74f756/SqlServerDbContextOptionsExtensions.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/c21a9621256e40dbbf724986af856144133178/d2/8cfead71/SingleQueryingEnumerable`1.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/e7bd73da3c6440fbb258d36855a3f9401bfb78/c3/3d0dc8bc/DbContextServices.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/5ba1ecc6a2197193ec944ac6db9e4273eb55854fa50448c6c9ebfbe7f12475/ConnectionMultiplexer.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/b45040d1d5783fc65c68f446c337eed236761ac37e70e7d6bb618f32364e8/EndPointCollection.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/d68c6f52e558cad78615d582bf9394d423c1f45de7af1ccf8bc447eb8b4b225/ConfigurationOptions.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/OnePage.Call/Controllers/CallController.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/OnePage.NewOpp/Controllers/MuserController.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="mock:///Users/<USER>/Oayaw/api/OnePage.Register/Controllers/RegisterController.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/OnePage.Services/ResearchService.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/OnePage.WebAdmin/Controllers/ResearchController.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/OnePage.WebApp/Controllers/ContactController.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="mock:///Users/<USER>/Oayaw/api/OnePage.WebApp/Controllers/ProvidersController.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/OnePage.WebProxy/Controllers/CRMController.cs" root0="FORCE_HIGHLIGHTING" />
  </component>
>>>>>>> c432fff8ca51c675f655c73962bbba2e9854763f
  <component name="MetaFilesCheckinStateConfiguration" checkMetaFiles="false" />
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="2g93hc03TyE14DfjBabPlCCFuO7" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    ".NET Launch Settings Profile.OnePage.NewOpp.executor": "Debug",
    ".NET Launch Settings Profile.OnePage.OneTime: IIS Express.executor": "Debug",
    ".NET Launch Settings Profile.OnePage.WebApp: IIS Express.executor": "Debug",
    ".NET Launch Settings Profile.OnePage.WebProxy: IIS Express.executor": "Debug",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "git-widget-placeholder": "1888__two__contacts__add",
    "node.js.detected.package.eslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "preferences.keymap",
    "vue.rearranger.settings.migration": "true"
  },
  "keyToStringList": {
    "rider.external.source.directories": [
      "/Users/<USER>/Library/Application Support/JetBrains/Rider2023.3/resharper-host/DecompilerCache",
      "/Users/<USER>/Library/Application Support/JetBrains/Rider2023.3/resharper-host/SourcesCache",
      "/Users/<USER>/.local/share/Symbols/src"
    ]
  }
<<<<<<< HEAD
}</component>
  <component name="RunManager">
    <configuration name="Publish OnePage.GetCardsAndAction to Azure" type="AzureFunctionAppPublish" factoryName="Azure Function App">
      <option name="AZURE_FUNCTION_APP_PROJECT" value="" />
      <option name="AZURE_FUNCTION_APP_CONFIGURATION" value="Release" />
      <option name="AZURE_FUNCTION_APP_PLATFORM" value="Any CPU" />
      <option name="AZURE_FUNCTION_APP_SUBSCRIPTION_ID" value="" />
      <option name="AZURE_FUNCTION_APP_IS_CREATE_APP" value="0" />
      <option name="AZURE_FUNCTION_APP_ID" value="/subscriptions/11d87597-c43d-460a-8ca9-4fe68e590b3b/resourceGroups/az_function_group/providers/Microsoft.Web/sites/OnePageGetCardsAndActions" />
      <option name="AZURE_FUNCTION_APP_NAME" value="" />
      <option name="AZURE_FUNCTION_APP_IS_CREATE_RESOURCE_GROUP" value="0" />
      <option name="AZURE_FUNCTION_APP_RESOURCE_GROUP_NAME" value="" />
      <option name="AZURE_FUNCTION_APP_IS_CREATE_APP_SERVICE_PLAN" value="0" />
      <option name="AZURE_FUNCTION_APP_SERVICE_PLAN_ID" value="/subscriptions/11d87597-c43d-460a-8ca9-4fe68e590b3b/resourceGroups/az_function_group/providers/Microsoft.Web/serverfarms/NorthEuropePlan" />
      <option name="AZURE_FUNCTION_APP_SERVICE_PLAN_NAME" value="" />
      <option name="AZURE_FUNCTION_APP_LOCATION" value="eastus" />
      <option name="AZURE_FUNCTION_APP_PRICING_TIER" value="" />
      <option name="AZURE_FUNCTION_APP_OPERATING_SYSTEM" value="WINDOWS" />
      <option name="AZURE_FUNCTION_APP_RUNTIME" value="DOTNET" />
      <option name="AZURE_FUNCTION_APP_RUNTIMEVERSION" value="~4" />
      <option name="AZURE_FUNCTION_APP_FXVERSION" value="DOTNET|6.0" />
      <option name="AZURE_FUNCTION_APP_IS_CREATE_STORAGE_ACCOUNT" value="0" />
      <option name="AZURE_FUNCTION_APP_STORAGE_ACCOUNT_ID" value="/subscriptions/11d87597-c43d-460a-8ca9-4fe68e590b3b/resourceGroups/eu-ne-st/providers/Microsoft.Storage/storageAccounts/dbbak" />
      <option name="AZURE_FUNCTION_APP_STORAGE_ACCOUNT_NAME" value="" />
      <option name="AZURE_FUNCTION_APP_STORAGE_ACCOUNT_TYPE" value="Standard_LRS" />
      <option name="AZURE_FUNCTION_APP_IS_DEPLOY_TO_SLOT" value="0" />
      <option name="AZURE_FUNCTION_APP_SLOT_NAME" value="" />
      <option name="AZURE_FUNCTION_APP_OPEN_BROWSER_PUBLISH" value="0" />
      <option name="AZURE_SQL_DATABASE_SUBSCRIPTION_ID" value="" />
      <option name="AZURE_SQL_DATABASE_IS_ENABLED" value="0" />
      <option name="AZURE_SQL_DATABASE_CONNECTION_STRING_NAME" value="" />
      <option name="AZURE_SQL_DATABASE_IS_CREATE" value="0" />
      <option name="AZURE_SQL_DATABASE_ID" value="/subscriptions/11d87597-c43d-460a-8ca9-4fe68e590b3b/resourceGroups/eu-ne-saas-group/providers/Microsoft.Sql/servers/eu-ne-saas-sql/databases/AMPSaaSDB" />
      <option name="AZURE_SQL_DATABASE_NAME" value="" />
      <option name="AZURE_SQL_DATABASE_IS_CREATE_RESOURCE_GROUP" value="0" />
      <option name="AZURE_SQL_DATABASE_RESOURCE_GROUP_NAME" value="" />
      <option name="AZURE_SQL_DATABASE_IS_CREATE_SQL_SERVER" value="0" />
      <option name="AZURE_SQL_DATABASE_SQL_SERVER_ID" value="" />
      <option name="AZURE_SQL_DATABASE_SQL_SERVER_NAME" value="" />
      <option name="AZURE_SQL_DATABASE_SQL_SERVER_ADMIN_LOGIN" value="saasdbadmin909" />
      <option name="AZURE_SQL_DATABASE_LOCATION" value="eastus" />
      <option name="AZURE_SQL_DATABASE_COLLATION" value="SQL_Latin1_General_CP1_CI_AS" />
      <method v="2" />
    </configuration>
=======
}]]></component>
  <component name="RunManager" selected=".NET Launch Settings Profile.OnePage.WebApp: IIS Express">
>>>>>>> c432fff8ca51c675f655c73962bbba2e9854763f
    <configuration name="OnePage.GetCardsAndAction" type="AzureFunctionsHost" factoryName="Azure Functions host">
      <option name="EXE_PATH" value="" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/OnePage.GetCardsAndAction/OnePage.GetCardsAndAction.csproj" />
      <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
      <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
      <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
      <option name="PROJECT_KIND" value="RunnableProjectKind(name=RunnableProjectKind(name=RunnableProjectKind(name=None)))" />
      <option name="PROJECT_TFM" value="" />
      <option name="FUNCTION_NAMES" value="" />
      <method v="2">
        <option name="BuildFunctionsProject" enabled="true" />
        <option name="RunAzuriteTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="OnePage.WebhookCl" type="AzureFunctionsHost" factoryName="Azure Functions host">
      <option name="EXE_PATH" value="" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/OnePage.WebhookCl/OnePage.WebhookCl.csproj" />
      <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
      <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
      <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
      <option name="PROJECT_KIND" value="RunnableProjectKind(name=RunnableProjectKind(name=RunnableProjectKind(name=None)))" />
      <option name="PROJECT_TFM" value="" />
      <option name="FUNCTION_NAMES" value="" />
      <method v="2">
        <option name="BuildFunctionsProject" enabled="true" />
        <option name="RunAzuriteTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="OnePage.Webhook.MC" type="AzureFunctionsHost" factoryName="Azure Functions host">
      <option name="EXE_PATH" value="" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/OnePage.Webhook.MC/OnePage.Webhook.MC.csproj" />
      <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
      <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
      <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
      <option name="PROJECT_KIND" value="RunnableProjectKind(name=RunnableProjectKind(name=RunnableProjectKind(name=None)))" />
      <option name="PROJECT_TFM" value="" />
      <option name="FUNCTION_NAMES" value="" />
      <method v="2">
        <option name="BuildFunctionsProject" enabled="true" />
        <option name="RunAzuriteTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="OnePage.Webhook.MC.Ent" type="AzureFunctionsHost" factoryName="Azure Functions host">
      <option name="EXE_PATH" value="" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/OnePage.Webhook.MC.Ent/OnePage.Webhook.MC.Ent.csproj" />
      <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
      <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
      <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
      <option name="PROJECT_KIND" value="RunnableProjectKind(name=RunnableProjectKind(name=RunnableProjectKind(name=None)))" />
      <option name="PROJECT_TFM" value="" />
      <option name="FUNCTION_NAMES" value="" />
      <method v="2">
        <option name="BuildFunctionsProject" enabled="true" />
        <option name="RunAzuriteTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="OnePage.Webhook.Microsoft.Enterprise" type="AzureFunctionsHost" factoryName="Azure Functions host">
      <option name="EXE_PATH" value="" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/OnePage.Webhook.Microsoft.Enterprise/OnePage.Webhook.Microsoft.Enterprise.csproj" />
      <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
      <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
      <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
      <option name="PROJECT_KIND" value="RunnableProjectKind(name=RunnableProjectKind(name=RunnableProjectKind(name=None)))" />
      <option name="PROJECT_TFM" value="" />
      <option name="FUNCTION_NAMES" value="" />
      <method v="2">
        <option name="BuildFunctionsProject" enabled="true" />
        <option name="RunAzuriteTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="OnePage.NewOpp" type="LaunchSettings" factoryName=".NET Launch Settings Profile" temporary="true">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/OnePage.NewOpp/OnePage.NewOpp.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0" />
      <option name="LAUNCH_PROFILE_NAME" value="OnePage.NewOpp" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="OnePage.OneTime: IIS Express" type="LaunchSettings" factoryName=".NET Launch Settings Profile" temporary="true">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/OnePage.OneTime/OnePage.OneTime.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0" />
      <option name="LAUNCH_PROFILE_NAME" value="OnePage.OneTime" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="OnePage.WebApp: IIS Express" type="LaunchSettings" factoryName=".NET Launch Settings Profile" temporary="true">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/OnePage.WebApp/OnePage.WebApp.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0" />
      <option name="LAUNCH_PROFILE_NAME" value="OnePage.WebApp" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="OnePage.WebProxy: IIS Express" type="LaunchSettings" factoryName=".NET Launch Settings Profile" temporary="true">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/OnePage.WebProxy/OnePage.WebProxy.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0" />
      <option name="LAUNCH_PROFILE_NAME" value="OnePage.WebProxy" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue=".NET Launch Settings Profile.OnePage.WebApp: IIS Express" />
        <item itemvalue=".NET Launch Settings Profile.OnePage.OneTime: IIS Express" />
        <item itemvalue=".NET Launch Settings Profile.OnePage.NewOpp" />
        <item itemvalue=".NET Launch Settings Profile.OnePage.WebProxy: IIS Express" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="3eb552b8-aa11-4929-990f-ec0bf0bca2af" name="Changes" comment="&quot;changed appsettings for staging&quot;" />
      <created>1715096088451</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1715096088451</updated>
      <workItem from="1715096090957" duration="132000" />
      <workItem from="1715145008443" duration="8894000" />
    </task>
    <task id="LOCAL-00001" summary="&quot;changed appsettings for staging&quot;">
      <option name="closed" value="true" />
      <created>1715147460901</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1715147460902</updated>
    </task>
    <option name="localTasksCounter" value="2" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnityCheckinConfiguration" checkUnsavedScenes="false" />
  <component name="VcsManagerConfiguration">
<<<<<<< HEAD
    <option name="CLEAR_INITIAL_COMMIT_MESSAGE" value="true" />
    <MESSAGE value="&quot;changed appsettings for staging&quot;" />
    <option name="LAST_COMMIT_MESSAGE" value="&quot;changed appsettings for staging&quot;" />
=======
    <MESSAGE value="&quot;changed appsettings for staging&quot;" />
    <option name="LAST_COMMIT_MESSAGE" value="&quot;changed appsettings for staging&quot;" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.WebApp/Controllers/EventController.cs</url>
          <line>1061</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.WebApp/Controllers/EventController.cs" initialLine="1060" containingFunctionPresentation="Method 'AddContact'">
            <startOffsets>
              <option value="53540" />
            </startOffsets>
            <endOffsets>
              <option value="53567" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="109" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.WebApp/Controllers/ProvidersController.cs</url>
          <line>3874</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.WebApp/Controllers/ProvidersController.cs" initialLine="3693" containingFunctionPresentation="Method 'GetAllProvidersList2'">
            <startOffsets>
              <option value="213227" />
            </startOffsets>
            <endOffsets>
              <option value="213251" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="128" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.WebApp/Controllers/EventController.cs</url>
          <line>823</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.WebApp/Controllers/EventController.cs" initialLine="810" containingFunctionPresentation="Method 'GetEvents'">
            <startOffsets>
              <option value="42127" />
            </startOffsets>
            <endOffsets>
              <option value="42152" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="135" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.WebApp/Controllers/EventController.cs</url>
          <line>822</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.WebApp/Controllers/EventController.cs" initialLine="822" containingFunctionPresentation="Method 'GetEvents'">
            <startOffsets>
              <option value="42811" />
            </startOffsets>
            <endOffsets>
              <option value="42836" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="136" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.WebApp/Controllers/EventController.cs</url>
          <line>838</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.WebApp/Controllers/EventController.cs" initialLine="838" containingFunctionPresentation="Method 'GetEvents'">
            <startOffsets>
              <option value="43668" />
            </startOffsets>
            <endOffsets>
              <option value="43735" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="137" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.WebApp/Controllers/EventController.cs</url>
          <line>843</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.WebApp/Controllers/EventController.cs" initialLine="843" containingFunctionPresentation="Method 'GetEvents'">
            <startOffsets>
              <option value="44053" />
            </startOffsets>
            <endOffsets>
              <option value="44070" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="138" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.Services/ProviderService.cs</url>
          <line>1053</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.Services/ProviderService.cs" initialLine="1053" containingFunctionPresentation="Method 'GetIdentifiersForUserProvider'">
            <startOffsets>
              <option value="52625" />
            </startOffsets>
            <endOffsets>
              <option value="52626" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="143" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.WebApp/Controllers/ProvidersController.cs</url>
          <line>3781</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.WebApp/Controllers/ProvidersController.cs" initialLine="3627" containingFunctionPresentation="Method 'GetAllProvidersList2'">
            <startOffsets>
              <option value="206096" />
            </startOffsets>
            <endOffsets>
              <option value="206139" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="144" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.WebApp/Controllers/ProvidersController.cs</url>
          <line>3879</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.WebApp/Controllers/ProvidersController.cs" initialLine="3877" containingFunctionPresentation="Method 'GetAllProvidersList2'">
            <startOffsets>
              <option value="213347" />
            </startOffsets>
            <endOffsets>
              <option value="213391" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="146" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.WebApp/Controllers/EventController.cs</url>
          <line>487</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.WebApp/Controllers/EventController.cs" initialLine="487" containingFunctionPresentation="Method 'ProcessEventWithCardIds'">
            <startOffsets>
              <option value="24190" />
            </startOffsets>
            <endOffsets>
              <option value="24206" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="147" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.WebApp/Controllers/EventController.cs</url>
          <line>545</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.WebApp/Controllers/EventController.cs" initialLine="545" containingFunctionPresentation="Method 'ProcessEventWithCardIds'">
            <startOffsets>
              <option value="27184" />
              <option value="27222" />
            </startOffsets>
            <endOffsets>
              <option value="27336" />
              <option value="27325" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="148" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.Services/CalendarService.cs</url>
          <line>791</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.Services/CalendarService.cs" initialLine="790" containingFunctionPresentation="Method 'ProcessEvents'">
            <startOffsets>
              <option value="42690" />
            </startOffsets>
            <endOffsets>
              <option value="42798" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="149" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.Services/CalendarService.cs</url>
          <line>672</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.Services/CalendarService.cs" initialLine="671" containingFunctionPresentation="Method 'ProcessEvents'">
            <startOffsets>
              <option value="36202" />
            </startOffsets>
            <endOffsets>
              <option value="36289" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="150" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.Services/CalendarService.cs</url>
          <line>1968</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.Services/CalendarService.cs" initialLine="1988" containingFunctionPresentation="Method 'ProcessUserProviderAsync'">
            <startOffsets>
              <option value="109507" />
            </startOffsets>
            <endOffsets>
              <option value="109508" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="154" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.Services/CalendarService.cs</url>
          <line>674</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.Services/CalendarService.cs" initialLine="673" containingFunctionPresentation="Method 'ProcessEvents'">
            <startOffsets>
              <option value="36408" />
            </startOffsets>
            <endOffsets>
              <option value="36428" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="155" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.Services/CalendarService.cs</url>
          <line>814</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.Services/CalendarService.cs" initialLine="813" containingFunctionPresentation="Method 'ProcessEvents'">
            <startOffsets>
              <option value="43727" />
            </startOffsets>
            <endOffsets>
              <option value="43728" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="165" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.Services/CalendarService.cs</url>
          <line>696</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.Services/CalendarService.cs" initialLine="695" containingFunctionPresentation="Method 'ProcessEvents'">
            <startOffsets>
              <option value="37947" />
            </startOffsets>
            <endOffsets>
              <option value="37967" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="167" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.Services/CalendarService.cs</url>
          <line>1904</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.Services/CalendarService.cs" initialLine="1904" containingFunctionPresentation="Method 'GetAllEvents'">
            <startOffsets>
              <option value="106538" />
            </startOffsets>
            <endOffsets>
              <option value="106562" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="170" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.Services/CalendarService.cs</url>
          <line>1919</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.Services/CalendarService.cs" initialLine="1919" containingFunctionPresentation="Method 'GetAllEvents'">
            <startOffsets>
              <option value="107208" />
            </startOffsets>
            <endOffsets>
              <option value="107222" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="171" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.Services/CalendarService.cs</url>
          <line>726</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.Services/CalendarService.cs" initialLine="726" containingFunctionPresentation="Method 'ProcessEvents'">
            <startOffsets>
              <option value="39801" />
            </startOffsets>
            <endOffsets>
              <option value="39837" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="172" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.Services/CalendarService.cs</url>
          <line>766</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.Services/CalendarService.cs" initialLine="766" containingFunctionPresentation="Method 'ProcessEvents'">
            <startOffsets>
              <option value="41939" />
            </startOffsets>
            <endOffsets>
              <option value="41966" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="175" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.Services/CalendarService.cs</url>
          <line>774</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.Services/CalendarService.cs" initialLine="774" containingFunctionPresentation="Method 'ProcessEvents'">
            <startOffsets>
              <option value="42142" />
            </startOffsets>
            <endOffsets>
              <option value="42169" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="176" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.WebApp/Controllers/EventController.cs</url>
          <line>522</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.WebApp/Controllers/EventController.cs" initialLine="522" containingFunctionPresentation="Method 'ProcessEventWithCardIds'">
            <startOffsets>
              <option value="26039" />
            </startOffsets>
            <endOffsets>
              <option value="26115" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="179" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.Services/CalendarService.cs</url>
          <line>1988</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.Services/CalendarService.cs" initialLine="1988" containingFunctionPresentation="Method 'FirstHalfEvent'">
            <startOffsets>
              <option value="110345" />
            </startOffsets>
            <endOffsets>
              <option value="110408" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="180" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.WebApp/Controllers/EventController.cs</url>
          <line>804</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.WebApp/Controllers/EventController.cs" initialLine="804" containingFunctionPresentation="Method 'ProcessEventWithCardIds'">
            <startOffsets>
              <option value="41932" />
            </startOffsets>
            <endOffsets>
              <option value="41961" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="181" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.WebApp/Controllers/EventController.cs</url>
          <line>583</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.WebApp/Controllers/EventController.cs" initialLine="583" containingFunctionPresentation="Method 'ProcessEventWithCardIds'">
            <startOffsets>
              <option value="29117" />
            </startOffsets>
            <endOffsets>
              <option value="29128" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="182" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.WebApp/Controllers/EventController.cs</url>
          <line>615</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.WebApp/Controllers/EventController.cs" initialLine="615" containingFunctionPresentation="Method 'ProcessEventWithCardIds'">
            <startOffsets>
              <option value="30599" />
            </startOffsets>
            <endOffsets>
              <option value="30634" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="183" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.WebApp/Controllers/ProvidersController.cs</url>
          <line>4347</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.WebApp/Controllers/ProvidersController.cs" initialLine="4345" containingFunctionPresentation="Method 'GetUserSettingsFromDB'">
            <startOffsets>
              <option value="238958" />
            </startOffsets>
            <endOffsets>
              <option value="239001" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="184" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.Services/AuthService.cs</url>
          <line>47</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.Services/AuthService.cs" initialLine="48" containingFunctionPresentation="Method 'GetUser'">
            <startOffsets>
              <option value="1834" />
            </startOffsets>
            <endOffsets>
              <option value="1852" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="185" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.WebApp/Controllers/ProvidersController.cs</url>
          <line>4355</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.WebApp/Controllers/ProvidersController.cs" initialLine="4353" containingFunctionPresentation="Method 'GetUserSettingsFromDB'">
            <startOffsets>
              <option value="239154" />
            </startOffsets>
            <endOffsets>
              <option value="239667" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="187" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.WebApp/Controllers/LoginController.cs</url>
          <line>1143</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.WebApp/Controllers/LoginController.cs" initialLine="1142" containingFunctionPresentation="Method 'SendUserInfoMessage'">
            <startOffsets>
              <option value="67749" />
            </startOffsets>
            <endOffsets>
              <option value="67774" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="188" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.WebApp/Controllers/LoginController.cs</url>
          <line>1172</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.WebApp/Controllers/LoginController.cs" initialLine="1171" containingFunctionPresentation="Method 'SendUserInfoMessage'">
            <startOffsets>
              <option value="69728" />
            </startOffsets>
            <endOffsets>
              <option value="69887" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="189" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.WebApp/Controllers/LoginController.cs</url>
          <line>80</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.WebApp/Controllers/LoginController.cs" initialLine="80" containingFunctionPresentation="Constructor 'LoginController'">
            <startOffsets>
              <option value="3476" />
            </startOffsets>
            <endOffsets>
              <option value="3515" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="190" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.WebApp/Controllers/LoginController.cs</url>
          <line>1159</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.WebApp/Controllers/LoginController.cs" initialLine="1158" containingFunctionPresentation="Method 'SendUserInfoMessage'">
            <startOffsets>
              <option value="68734" />
            </startOffsets>
            <endOffsets>
              <option value="69007" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="191" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.WebProxy/Controllers/StorageController.cs</url>
          <line>69</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.WebProxy/Controllers/StorageController.cs" initialLine="68" containingFunctionPresentation="Method 'GetData'">
            <startOffsets>
              <option value="2756" />
            </startOffsets>
            <endOffsets>
              <option value="2799" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="192" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.WebApp/Controllers/EventController.cs</url>
          <line>2320</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.WebApp/Controllers/EventController.cs" initialLine="2320" containingFunctionPresentation="Method 'GetEventFireFiles'">
            <startOffsets>
              <option value="117150" />
            </startOffsets>
            <endOffsets>
              <option value="117175" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="193" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.WebApp/Controllers/EventController.cs</url>
          <line>2333</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.WebApp/Controllers/EventController.cs" initialLine="2333" containingFunctionPresentation="Method 'GetEventFireFiles'">
            <startOffsets>
              <option value="117855" />
            </startOffsets>
            <endOffsets>
              <option value="117887" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="197" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.WebApp/Controllers/EventController.cs</url>
          <line>2321</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.WebApp/Controllers/EventController.cs" initialLine="2321" containingFunctionPresentation="Method 'GetEventFireFiles'">
            <startOffsets>
              <option value="117188" />
            </startOffsets>
            <endOffsets>
              <option value="117189" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="201" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.Services/EventTranscriptService.cs</url>
          <line>155</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.Services/EventTranscriptService.cs" initialLine="150" containingFunctionPresentation="Method 'GetTranscripts'">
            <startOffsets>
              <option value="8076" />
            </startOffsets>
            <endOffsets>
              <option value="8077" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="202" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.Services/EventTranscriptService.cs</url>
          <line>166</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.Services/EventTranscriptService.cs" initialLine="161" containingFunctionPresentation="Method 'GetTranscripts'">
            <startOffsets>
              <option value="8925" />
            </startOffsets>
            <endOffsets>
              <option value="9014" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="203" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.Services/EventTranscriptService.cs</url>
          <line>211</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.Services/EventTranscriptService.cs" initialLine="206" containingFunctionPresentation="Method 'GetTranscripts'">
            <startOffsets>
              <option value="12213" />
            </startOffsets>
            <endOffsets>
              <option value="12227" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="204" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.Services/EventTranscriptService.cs</url>
          <line>218</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.Services/EventTranscriptService.cs" initialLine="213" containingFunctionPresentation="Method 'GetTranscripts'">
            <startOffsets>
              <option value="12438" />
            </startOffsets>
            <endOffsets>
              <option value="12450" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="205" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.Services/EventTranscriptService.cs</url>
          <line>225</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.Services/EventTranscriptService.cs" initialLine="220" containingFunctionPresentation="Method 'GetTranscripts'">
            <startOffsets>
              <option value="12601" />
            </startOffsets>
            <endOffsets>
              <option value="12613" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="206" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.Services/EventTranscriptService.cs</url>
          <line>122</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.Services/EventTranscriptService.cs" initialLine="122" containingFunctionPresentation="Method 'GetTranscripts'">
            <startOffsets>
              <option value="6349" />
            </startOffsets>
            <endOffsets>
              <option value="6368" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="207" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.Services/ProviderService.cs</url>
          <line>720</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.Services/ProviderService.cs" initialLine="720" containingFunctionPresentation="Method 'MakeRequest'">
            <startOffsets>
              <option value="34226" />
            </startOffsets>
            <endOffsets>
              <option value="34267" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="210" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.Services/EventTranscriptService.cs</url>
          <line>147</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.Services/EventTranscriptService.cs" initialLine="142" containingFunctionPresentation="Method 'GetTranscripts'">
            <startOffsets>
              <option value="7838" />
            </startOffsets>
            <endOffsets>
              <option value="7839" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="211" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.NewOpp/Controllers/MuserController.cs</url>
          <line>1792</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.NewOpp/Controllers/MuserController.cs" initialLine="1792" containingFunctionPresentation="Method 'Mverifynew'">
            <startOffsets>
              <option value="89640" />
            </startOffsets>
            <endOffsets>
              <option value="89712" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="212" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.WebApp/Controllers/MuserController.cs</url>
          <line>1974</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.WebApp/Controllers/MuserController.cs" initialLine="1974" containingFunctionPresentation="Method 'Mverifynew'">
            <startOffsets>
              <option value="97479" />
            </startOffsets>
            <endOffsets>
              <option value="116193" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="220" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.WebApp/Controllers/MuserController.cs</url>
          <line>1975</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.WebApp/Controllers/MuserController.cs" initialLine="1975" containingFunctionPresentation="Method 'Mverifynew'">
            <startOffsets>
              <option value="97495" />
            </startOffsets>
            <endOffsets>
              <option value="97496" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="221" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.OneTime/Controllers/SignalController.cs</url>
          <line>576</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.OneTime/Controllers/SignalController.cs" initialLine="576" containingFunctionPresentation="Method 'UpdateSignal'">
            <startOffsets>
              <option value="30886" />
            </startOffsets>
            <endOffsets>
              <option value="30887" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="271" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.WebApp/Controllers/AuthController.cs</url>
          <line>1750</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.WebApp/Controllers/AuthController.cs" initialLine="1747" containingFunctionPresentation="Method 'HandleTeamsSSO'">
            <startOffsets>
              <option value="101200" />
            </startOffsets>
            <endOffsets>
              <option value="101255" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="275" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.WebApp/Controllers/AuthController.cs</url>
          <line>1047</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.WebApp/Controllers/AuthController.cs" initialLine="1044" containingFunctionPresentation="Method 'HandleTeamsSSO'">
            <startOffsets>
              <option value="58619" />
            </startOffsets>
            <endOffsets>
              <option value="58703" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="278" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.WebApp/Controllers/AuthController.cs</url>
          <line>1140</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.WebApp/Controllers/AuthController.cs" initialLine="1137" containingFunctionPresentation="Method 'HandleTeamsSSO'">
            <startOffsets>
              <option value="64665" />
            </startOffsets>
            <endOffsets>
              <option value="64707" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="279" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.WebApp/Controllers/AuthController.cs</url>
          <line>1172</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.WebApp/Controllers/AuthController.cs" initialLine="1169" containingFunctionPresentation="Method 'HandleTeamsSSO'">
            <startOffsets>
              <option value="66308" />
            </startOffsets>
            <endOffsets>
              <option value="66400" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="280" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.WebApp/Controllers/AuthController.cs</url>
          <line>1229</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.WebApp/Controllers/AuthController.cs" initialLine="1226" containingFunctionPresentation="Method 'HandleTeamsSSO'">
            <startOffsets>
              <option value="69185" />
            </startOffsets>
            <endOffsets>
              <option value="69326" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="281" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.WebApp/Controllers/AuthController.cs</url>
          <line>1385</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.WebApp/Controllers/AuthController.cs" initialLine="1382" containingFunctionPresentation="Method 'HandleTeamsSSO'">
            <startOffsets>
              <option value="79681" />
            </startOffsets>
            <endOffsets>
              <option value="79710" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="282" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.WebApp/Controllers/AuthController.cs</url>
          <line>1389</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.WebApp/Controllers/AuthController.cs" initialLine="1386" containingFunctionPresentation="Method 'HandleTeamsSSO'">
            <startOffsets>
              <option value="80092" />
            </startOffsets>
            <endOffsets>
              <option value="80115" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="283" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.WebApp/Controllers/AuthController.cs</url>
          <line>2464</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.WebApp/Controllers/AuthController.cs" initialLine="2461" containingFunctionPresentation="Method 'ProcessValidateApi'">
            <startOffsets>
              <option value="142475" />
            </startOffsets>
            <endOffsets>
              <option value="142528" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="285" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.WebApp/Controllers/AuthController.cs</url>
          <line>2998</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.WebApp/Controllers/AuthController.cs" initialLine="2986" containingFunctionPresentation="Method 'UpdateOtherTablesUsingEmail'">
            <startOffsets>
              <option value="174200" />
            </startOffsets>
            <endOffsets>
              <option value="174248" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="286" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.WebApp/Controllers/AuthController.cs</url>
          <line>2344</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.WebApp/Controllers/AuthController.cs" initialLine="2341" containingFunctionPresentation="Method 'ProcessValidateApi'">
            <startOffsets>
              <option value="135465" />
            </startOffsets>
            <endOffsets>
              <option value="135538" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="287" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.WebApp/Controllers/AuthController.cs</url>
          <line>2688</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.WebApp/Controllers/AuthController.cs" initialLine="2679" containingFunctionPresentation="Method 'UpdateOtherTablesUsingEmail'">
            <startOffsets>
              <option value="157733" />
            </startOffsets>
            <endOffsets>
              <option value="157791" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="290" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.WebApp/Controllers/AuthController.cs</url>
          <line>2893</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.WebApp/Controllers/AuthController.cs" initialLine="2893" containingFunctionPresentation="Method 'UpdateOtherTablesUsingEmail'">
            <startOffsets>
              <option value="168738" />
            </startOffsets>
            <endOffsets>
              <option value="168886" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="291" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/OnePage.Services/UserProviderCaching.cs</url>
          <line>36</line>
          <properties documentPath="$PROJECT_DIR$/OnePage.Services/UserProviderCaching.cs" initialLine="54" containingFunctionPresentation="Method 'GetUserProvidersFromRedisOrDbAsyncWithUserId'">
            <startOffsets>
              <option value="1862" />
            </startOffsets>
            <endOffsets>
              <option value="1863" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="292" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
>>>>>>> c432fff8ca51c675f655c73962bbba2e9854763f
  </component>
</project>