﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace OnePage.Services
{
    public static class TelegramService
    {
        static readonly string gaganBotToken = "6880724844:AAH8y6CcDA634gKhODWCBle0S54mhzXoxck";
        static readonly string dreamcastToken = "6940417904:AAE6pU0G34TLRlsJWtH1ubMLsqDuU9EBdxI";
        static readonly string testBottoken = "5277113211:AAGCRaM-0ZL25CqTOfzcPMfwlMo6V36FMBI";
        static readonly string moukhabottoken = "6857698043:AAH0iNsf4NoZp3k6g1Tg8uW__BfzJYTd9lI";
        static readonly string token = "1979617440:AAE_q-2irwpi5IdoKIersDaruxOa81uIkFk";
        static readonly string rahulBotToken = "6566035574:AAE6cp3oPAU6qq71dmhMHYQD9BmXXMhl1Gc";
        // static readonly string token = "5277113211:AAGCRaM-0ZL25CqTOfzcPMfwlMo6V36FMBI";
        static readonly string chatId = "65294787";
        static readonly string gaganChatId = "6568143944";
        static readonly string savithaChatId = "5254402108";
        static readonly string kirtiChatId = "509474681";
        // static readonly string chatId = "184762097";
        static readonly string rahulChatId = "645581919";//rahul
        static readonly string prakashBotToken = "7168330395:AAH7KtPIyL5R6MnfpD2jdHvlnvMx-9D79m8";
        static readonly string prakashChatId = "796226735";

        public static string SendMessage(string message, string profileLink = "", string insightsLink = "")
        {
            string retval = string.Empty;
            string retval2 = string.Empty;
            string retval3 = string.Empty;
            string retval4 = string.Empty;
            string url = ""; string url2 = ""; string url3 = ""; string url4 = "";
            if (!string.IsNullOrEmpty(profileLink))
            {
                string linkurl1 = "reply_markup={\"inline_keyboard\":[[{\"text\":\"Profile\",\"url\":\"" + Uri.EscapeDataString(profileLink) + "\"}, {\"text\":\"Insights\",\"url\":\"" + Uri.EscapeDataString(insightsLink) + "\"}]]}";
                url = $"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatId}&text={message}&" + linkurl1;
                url2 = $"https://api.telegram.org/bot{token}/sendMessage?chat_id={savithaChatId}&text={message}&" + linkurl1;
                url3 = $"https://api.telegram.org/bot{token}/sendMessage?chat_id={kirtiChatId}&text={message}&" + linkurl1;
                //   url4 = $"https://api.telegram.org/bot{token}/sendMessage?chat_id={gaganChatId}&text={message}&" + linkurl1;
            }
            else
            {
                url = $"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatId}&text={message}";
                url2 = $"https://api.telegram.org/bot{token}/sendMessage?chat_id={savithaChatId}&text={message}";
                url3 = $"https://api.telegram.org/bot{token}/sendMessage?chat_id={kirtiChatId}&text={message}";
                //  url4 = $"https://api.telegram.org/bot{token}/sendMessage?chat_id={gaganChatId}&text={message}";
            }

            try
            {
                using (var webClient = new WebClient())
                {
                    retval = webClient.DownloadString(url);
                }
                using (var webClient1 = new WebClient())
                {
                    retval2 = webClient1.DownloadString(url2);
                }
                using (var webClient2 = new WebClient())
                {
                    retval3 = webClient2.DownloadString(url3);
                }
                //using (var webClient3 = new WebClient())
                //{
                //    retval4 = webClient3.DownloadString(url4);
                //}
            }
            catch (Exception ex)
            {
                return "";
            }

            return retval;
        }
        public async static Task<string> SendMessageToDreamcastBot(string message)
        {
            try
            {
                string retval = string.Empty;
                string url = $"https://api.telegram.org/bot{dreamcastToken}/sendMessage?chat_id={chatId}&text={message}";
                string durl2 = $"https://api.telegram.org/bot{dreamcastToken}/sendMessage?chat_id={savithaChatId}&text={message}";
                string durl3 = $"https://api.telegram.org/bot{dreamcastToken}/sendMessage?chat_id={kirtiChatId}&text={message}";
                //  string durl4 = $"https://api.telegram.org/bot{dreamcastToken}/sendMessage?chat_id={gaganChatId}&text={message}";
                using (var webClient = new WebClient())
                {
                    retval = webClient.DownloadString(url);
                }
                using (var webClient = new WebClient())
                {
                    retval = webClient.DownloadString(durl2);
                }
                using (var webClient = new WebClient())
                {
                    retval = webClient.DownloadString(durl3);
                }
                //using (var webClient = new WebClient())
                //{
                //    retval = webClient.DownloadString(durl4);
                //}
                return retval;
            }
            catch (Exception ex)
            {
                return "";
            }

            //string retval2 = string.Empty;
            //string retval3 = string.Empty;
            //string retval4 = string.Empty;
            //string durl = ""; string durl2 = ""; string durl3 = ""; string durl4 = "";

            //durl = $"https://api.telegram.org/bot{dreamcastToken}/sendMessage?chat_id={chatId}&text={message}";
            //durl2 = $"https://api.telegram.org/bot{dreamcastToken}/sendMessage?chat_id={savithaChatId}&text={message}";
            //durl3 = $"https://api.telegram.org/bot{dreamcastToken}/sendMessage?chat_id={kirtiChatId}&text={message}";
            //durl4 = $"https://api.telegram.org/bot{dreamcastToken}/sendMessage?chat_id={gaganChatId}&text={message}";
            //try
            //{
            //    string html;
            //    using (WebClient client = new WebClient())
            //    {
            //        client.Headers.Add("accept-language", " en-us");
            //        client.Headers.Add("accept", " text/html, application/xhtml+xml, */*");
            //        client.Headers.Add("user-agent", "mozilla/5.0 (compatible; msie 9.0; windows nt 6.1; trident/5.0)");
            //        html = client.DownloadString(durl4);
            //    }
            //    using (WebClient client2 = new WebClient())
            //    {
            //        client2.Headers.Add("accept-language", " en-us");
            //        client2.Headers.Add("accept", " text/html, application/xhtml+xml, */*");
            //        client2.Headers.Add("user-agent", "mozilla/5.0 (compatible; msie 9.0; windows nt 6.1; trident/5.0)");
            //        html = client2.DownloadString(durl3);
            //    }
            //    using (WebClient client = new WebClient())
            //    {
            //        client.Headers.Add("accept-language", " en-us");
            //        client.Headers.Add("accept", " text/html, application/xhtml+xml, */*");
            //        client.Headers.Add("user-agent", "mozilla/5.0 (compatible; msie 9.0; windows nt 6.1; trident/5.0)");
            //        html = client.DownloadString(durl2);
            //    }
            //    using (WebClient client = new WebClient())
            //    {
            //        client.Headers.Add("accept-language", " en-us");
            //        client.Headers.Add("accept", " text/html, application/xhtml+xml, */*");
            //        client.Headers.Add("user-agent", "mozilla/5.0 (compatible; msie 9.0; windows nt 6.1; trident/5.0)");
            //        html = client.DownloadString(durl);
            //    }
            //}
            //catch (Exception ex)
            //{
            //    return "";
            //}

            //return retval;
        }

        public async static Task<string> SendMessageToMoukhaBot(string message)
        {
            try
            {
                string retval = string.Empty;
                string url = $"https://api.telegram.org/bot{moukhabottoken}/sendMessage?chat_id={chatId}&text={message}";
                string durl4 = $"https://api.telegram.org/bot{moukhabottoken}/sendMessage?chat_id={gaganChatId}&text={message}";
                string durl2 = $"https://api.telegram.org/bot{moukhabottoken}/sendMessage?chat_id={savithaChatId}&text={message}";
                using (var webClient = new WebClient())
                {
                    retval = webClient.DownloadString(url);
                }
                using (var webClient = new WebClient())
                {
                    retval = webClient.DownloadString(durl4);
                }
                using (var webClient = new WebClient())
                {
                    retval = webClient.DownloadString(durl2);
                }
                return retval;
            }
            catch (Exception ex)
            {
                return "";
            }
        }
        public static string SendMessageToGaganTestBot(string message)
        {
            try
            {
                string retval = string.Empty;




                string url = $"https://api.telegram.org/bot{gaganBotToken}/sendMessage?chat_id={gaganChatId}&text={message}";

                using (var webClient = new WebClient())
                {
                    retval = webClient.DownloadString(url);
                }
                return "";
            }
            catch (Exception ex)
            {
                return "";
            }

        }
        public static string SendMessageToTestBot(string message)
        {
            try
            {
                string retval = string.Empty;

                //    string url = $"https://api.telegram.org/bot{rahulBotToken}/sendMessage?chat_id={rahulChatId}&text={message}";

                //    using (var webClient = new WebClient())
                //    {
                //        retval = webClient.DownloadString(url);
                //    }
                //    return "";


                //string url = $"https://api.telegram.org/bot{rahulBotToken}/sendMessage?chat_id={rahulChatId}&text={message}";

                //using (var webClient = new WebClient())
                //{
                //    retval = webClient.DownloadString(url);
                //}

                //string url = $"https://api.telegram.org/bot{testBottoken}/sendMessage?chat_id={rahulChatId}&text={message}";

                //using (var webClient = new WebClient())
                //{
                //    retval = webClient.DownloadString(url);
                //}
                //string url2 = $"https://api.telegram.org/bot{prakashBotToken}/sendMessage?chat_id={prakashChatId}&text={message}";

                //using (var webClient = new WebClient())
                //{
                //    retval = webClient.DownloadString(url2);
                //}

                return "";
            }
            catch (Exception ex)
            {
                return "";
            }
        }

            public static string SendMessageRahulToTestBot(string message)
            {
                try
                {
                    string retval = string.Empty;



                    //string url = $"https://api.telegram.org/bot{rahulBotToken}/sendMessage?chat_id={rahulChatId}&text={message}";

                    //using (var webClient = new WebClient())
                    //{
                    //    retval = webClient.DownloadString(url);
                    //}

                    return "";
                }
                catch (Exception ex)
                {
                    return "";
                }
            }
        
        public static string SendMessageToDreamcastTestBot(string message)
        {
            try
            {
                string retval = string.Empty;

                string url = $"https://api.telegram.org/bot{gaganBotToken}/sendMessage?chat_id={gaganChatId}&text={message}";

                using (var webClient = new WebClient())
                {
                    retval = webClient.DownloadString(url);
                }

                return "";
            }
            catch (Exception ex)
            {
                return "";
            }
        }
        public static string SendMessageToTestBot2(string message)
        {
            try
            {
               string retval = string.Empty;
                string url = $"https://api.telegram.org/bot{gaganBotToken}/sendMessage?chat_id={gaganChatId}&text={message}";

                using (var webClient = new WebClient())
                {
                    retval = webClient.DownloadString(url);
                }

                //string url = $"https://api.telegram.org/bot{rahulBotToken}/sendMessage?chat_id={rahulChatId}&text={message}";

                //using (var webClient = new WebClient())
                //{
                //    retval = webClient.DownloadString(url);
                //}

                return retval;
            }
            catch (Exception ex)
            {
                return "";
            }
        }
        public static string SendMessageToTestBot3(string message)
        {
            string retval = string.Empty;
            //  string url = $"https://api.telegram.org/bot{gaganBotToken}/sendMessage?chat_id={gaganChatId}&text={message}";

            //     using (var webClient = new WebClient())
            //     {
            //         retval = webClient.DownloadString(url);
            //     }

            string url = $"https://api.telegram.org/bot{rahulBotToken}/sendMessage?chat_id={rahulChatId}&text={message}";

            using (var webClient = new WebClient())
            {
                retval = webClient.DownloadString(url);
            }

            return retval;
        }
    }
}
